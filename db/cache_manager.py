#!/usr/bin/env python3
"""
Cache Manager for Form D Data

Implements caching policies for ZIP files and other data:
1. Time-to-live (TTL) based expiration
2. Least recently used (LRU) eviction
3. Priority-based retention
4. Size-based cleanup
"""

import os
import logging
import time
import shutil
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta

from db.database import DatabaseManager

class CacheManager:
    """
    Cache manager for Form D data.
    """

    def __init__(self, db_manager: DatabaseManager, data_dir: str = "data"):
        """
        Initialize the cache manager.

        Args:
            db_manager: Database manager instance
            data_dir: Directory for data storage
        """
        self.db_manager = db_manager
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Set up logging
        self.logger = logging.getLogger(__name__)
    
    def cache_zip_file(self, filename: str, url: str, date_str: str, 
                      file_path: Path, is_placeholder: bool = False) -> int:
        """
        Cache a ZIP file in the database.
        
        Args:
            filename: Name of the ZIP file
            url: URL where the ZIP file was downloaded from
            date_str: Date string in YYYYMMDD format
            file_path: Path to the ZIP file
            is_placeholder: Whether this is a placeholder entry
            
        Returns:
            ID of the cached ZIP file
        """
        # Get file size
        file_size = file_path.stat().st_size if file_path.exists() else None
        
        # Add to database
        zip_id = self.db_manager.add_zip_file(
            filename=filename,
            url=url,
            date_str=date_str,
            file_size=file_size,
            status="downloaded",
            is_placeholder=is_placeholder
        )
        
        # Add to cache management
        self.db_manager.update_cache_item(
            cache_type="zip",
            item_id=zip_id,
            size_bytes=file_size,
            priority=5 if is_placeholder else 0  # Higher priority for placeholders
        )
        
        return zip_id
    
    def get_cached_zip(self, date_str: str) -> Optional[Dict[str, Any]]:
        """
        Get a cached ZIP file for a specific date.
        
        Args:
            date_str: Date string in YYYYMMDD format
            
        Returns:
            ZIP file data or None if not found
        """
        return self.db_manager.get_zip_file(date_str=date_str)
    
    def update_zip_status(self, zip_id: int, status: str, 
                         error_message: Optional[str] = None) -> None:
        """
        Update the status of a cached ZIP file.
        
        Args:
            zip_id: ID of the ZIP file
            status: New status
            error_message: Error message if status is 'error'
        """
        self.db_manager.update_zip_status(zip_id, status, error_message)
    
    def clean_old_zip_files(self, older_than_days: int = 30, 
                           min_access_count: int = 3,
                           dry_run: bool = False) -> List[Dict[str, Any]]:
        """
        Clean up old ZIP files based on cache policies.
        
        Args:
            older_than_days: Only clean files older than this many days
            min_access_count: Only clean files accessed fewer than this many times
            dry_run: If True, only return the files that would be cleaned without deleting
            
        Returns:
            List of cleaned ZIP files
        """
        # Get cache items to clean
        items_to_clean = self.db_manager.get_cache_items_to_clean(
            cache_type="zip",
            older_than_days=older_than_days,
            min_access_count=min_access_count,
            exclude_priority_above=5,  # Don't clean high-priority items
            limit=100
        )
        
        cleaned_items = []
        
        for item in items_to_clean:
            # Get ZIP file details
            zip_file = self.db_manager.get_zip_file(zip_id=item["item_id"])
            
            if not zip_file:
                continue
            
            # Construct file path
            zip_path = self.data_dir / "raw" / zip_file["date_str"] / zip_file["filename"]
            
            if zip_path.exists():
                self.logger.info(f"Cleaning ZIP file: {zip_path}")
                
                if not dry_run:
                    try:
                        # Delete the file
                        zip_path.unlink()
                        
                        # Remove the cache item
                        self.db_manager.remove_cache_item(item["id"])
                        
                        # Update ZIP status
                        self.db_manager.update_zip_status(
                            zip_id=zip_file["id"],
                            status="cleaned",
                            error_message=None
                        )
                        
                        cleaned_items.append(zip_file)
                    except Exception as e:
                        self.logger.error(f"Error cleaning ZIP file {zip_path}: {e}")
                else:
                    cleaned_items.append(zip_file)
        
        return cleaned_items
    
    def clean_extracted_files(self, zip_id: int, keep_jsonl: bool = True) -> bool:
        """
        Clean up extracted files from a ZIP file, optionally keeping the JSONL.
        
        Args:
            zip_id: ID of the ZIP file
            keep_jsonl: Whether to keep the JSONL file
            
        Returns:
            True if cleaning was successful
        """
        # Get ZIP file details
        zip_file = self.db_manager.get_zip_file(zip_id=zip_id)
        
        if not zip_file:
            self.logger.warning(f"ZIP file with ID {zip_id} not found")
            return False
        
        # Construct directory path
        extract_dir = self.data_dir / "raw" / zip_file["date_str"]
        
        if not extract_dir.exists():
            self.logger.warning(f"Extract directory {extract_dir} does not exist")
            return False
        
        try:
            # Find JSONL file if we need to keep it
            jsonl_path = None
            if keep_jsonl:
                for file in extract_dir.glob(f"formd_{zip_file['date_str']}.jsonl"):
                    jsonl_path = file
                    break
            
            # Create a temporary directory to move files we want to keep
            if jsonl_path:
                temp_dir = extract_dir.parent / f"temp_{zip_file['date_str']}"
                temp_dir.mkdir(exist_ok=True)
                shutil.copy2(jsonl_path, temp_dir / jsonl_path.name)
            
            # Remove the extract directory
            shutil.rmtree(extract_dir)
            
            # Restore files we want to keep
            if jsonl_path:
                extract_dir.mkdir(exist_ok=True)
                shutil.copy2(temp_dir / jsonl_path.name, jsonl_path)
                shutil.rmtree(temp_dir)
            
            # Update ZIP status
            self.db_manager.update_zip_status(
                zip_id=zip_file["id"],
                status="cleaned_extracted",
                error_message=None
            )
            
            return True
        except Exception as e:
            self.logger.error(f"Error cleaning extracted files for ZIP {zip_id}: {e}")
            return False
    
    def get_total_cache_size(self) -> int:
        """
        Get the total size of all cached files in bytes.
        
        Returns:
            Total size in bytes
        """
        total_size = 0
        
        # Recursively get size of all files in data directory
        for path in self.data_dir.rglob("*"):
            if path.is_file():
                total_size += path.stat().st_size
        
        return total_size
    
    def enforce_size_limit(self, max_size_bytes: int = 10 * 1024 * 1024 * 1024) -> int:
        """
        Enforce a maximum cache size by cleaning old files.
        
        Args:
            max_size_bytes: Maximum cache size in bytes (default: 10GB)
            
        Returns:
            Number of bytes freed
        """
        current_size = self.get_total_cache_size()
        
        if current_size <= max_size_bytes:
            return 0
        
        bytes_to_free = current_size - max_size_bytes
        bytes_freed = 0
        
        # Clean old ZIP files first
        cleaned_zips = self.clean_old_zip_files(
            older_than_days=7,  # Start with very recent files
            min_access_count=1  # Include files accessed only once
        )
        
        for zip_file in cleaned_zips:
            bytes_freed += zip_file.get("file_size", 0) or 0
        
        # If we still need to free more space, clean older files
        if bytes_freed < bytes_to_free:
            cleaned_zips = self.clean_old_zip_files(
                older_than_days=1,  # Clean any files older than a day
                min_access_count=10  # But only if they haven't been accessed much
            )
            
            for zip_file in cleaned_zips:
                bytes_freed += zip_file.get("file_size", 0) or 0
        
        return bytes_freed
