#!/usr/bin/env python3
"""
Database Initialization Script

Initializes the SQLite database for Form D data storage:
1. Creates the database file if it doesn't exist
2. Applies the schema
3. Migrates existing data from the file system (optional)
"""

import os
import json
import logging
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

from db.database import DatabaseManager
from db.cache_manager import CacheManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def init_database(db_path: str = "db/formd.db", schema_path: str = "db/schema.sql") -> DatabaseManager:
    """
    Initialize the database.
    
    Args:
        db_path: Path to the SQLite database file
        schema_path: Path to the SQL schema file
        
    Returns:
        Initialized DatabaseManager
    """
    logger.info(f"Initializing database at {db_path} with schema {schema_path}")
    db_manager = DatabaseManager(db_path=db_path, schema_path=schema_path)
    return db_manager

def migrate_existing_data(db_manager: DatabaseManager, cache_manager: CacheManager, 
                         data_dir: str = "data", dry_run: bool = False) -> Dict[str, int]:
    """
    Migrate existing data from the file system to the database.
    
    Args:
        db_manager: Database manager instance
        cache_manager: Cache manager instance
        data_dir: Directory containing the data
        dry_run: If True, only print what would be done without making changes
        
    Returns:
        Dictionary with counts of migrated items by type
    """
    data_path = Path(data_dir)
    if not data_path.exists():
        logger.warning(f"Data directory {data_dir} does not exist")
        return {"zip_files": 0, "filings": 0}
    
    # Counters for migrated items
    migrated = {"zip_files": 0, "filings": 0}
    
    # Find ZIP files
    zip_files = list(data_path.rglob("*.zip"))
    logger.info(f"Found {len(zip_files)} ZIP files")
    
    for zip_path in zip_files:
        # Extract date from path
        try:
            # Try to extract date from parent directory name
            date_str = zip_path.parent.name
            
            # Skip if not a valid date format
            if not (date_str.isdigit() or date_str.startswith("20")):
                logger.warning(f"Skipping ZIP file with invalid date format: {zip_path}")
                continue
            
            # Get file info
            filename = zip_path.name
            file_size = zip_path.stat().st_size
            
            # Construct a placeholder URL
            url = f"migrated://{filename}"
            
            logger.info(f"Migrating ZIP file: {zip_path}")
            
            if not dry_run:
                # Add to database
                zip_id = cache_manager.cache_zip_file(
                    filename=filename,
                    url=url,
                    date_str=date_str,
                    file_path=zip_path,
                    is_placeholder=False
                )
                
                # Update status based on whether JSONL exists
                jsonl_path = zip_path.parent / f"formd_{date_str}.jsonl"
                if jsonl_path.exists():
                    cache_manager.update_zip_status(zip_id, "processed")
                else:
                    cache_manager.update_zip_status(zip_id, "downloaded")
                
                migrated["zip_files"] += 1
        except Exception as e:
            logger.error(f"Error migrating ZIP file {zip_path}: {e}")
    
    # Find JSONL files
    jsonl_files = list(data_path.rglob("*.jsonl"))
    logger.info(f"Found {len(jsonl_files)} JSONL files")
    
    for jsonl_path in jsonl_files:
        try:
            # Extract date from path
            date_str = jsonl_path.parent.name
            
            # Skip if not a valid date format
            if not (date_str.isdigit() or date_str.startswith("20")):
                logger.warning(f"Skipping JSONL file with invalid date format: {jsonl_path}")
                continue
            
            logger.info(f"Migrating filings from: {jsonl_path}")
            
            if not dry_run:
                # Find corresponding ZIP file
                zip_path = None
                for zip_file in zip_files:
                    if zip_file.parent == jsonl_path.parent:
                        zip_path = zip_file
                        break
                
                # Get ZIP ID if available
                zip_id = None
                if zip_path:
                    zip_info = db_manager.get_zip_file(filename=zip_path.name)
                    if zip_info:
                        zip_id = zip_info["id"]
                
                # Read and process filings
                with open(jsonl_path, 'r') as f:
                    for line in f:
                        try:
                            filing = json.loads(line.strip())
                            
                            # Extract required fields
                            accession_number = filing.get("accessionNumber", f"unknown_{migrated['filings']}")
                            issuer_name = filing.get("issuerName", "Unknown Issuer")
                            filing_date = filing.get("filingDate", date_str)
                            
                            # Add to database
                            db_manager.add_form_d_filing(
                                accession_number=accession_number,
                                issuer_name=issuer_name,
                                filing_date=filing_date,
                                json_data=filing,
                                source_zip_id=zip_id,
                                source_file=str(jsonl_path.relative_to(data_path))
                            )
                            
                            migrated["filings"] += 1
                        except Exception as e:
                            logger.error(f"Error migrating filing from {jsonl_path}: {e}")
        except Exception as e:
            logger.error(f"Error processing JSONL file {jsonl_path}: {e}")
    
    return migrated

def main(db_path: str = "db/formd.db", 
         schema_path: str = "db/schema.sql",
         data_dir: str = "data",
         migrate: bool = False,
         dry_run: bool = False):
    """
    Main function for initializing the database.
    
    Args:
        db_path: Path to the SQLite database file
        schema_path: Path to the SQL schema file
        data_dir: Directory containing the data
        migrate: Whether to migrate existing data
        dry_run: If True, only print what would be done without making changes
    """
    # Initialize database
    db_manager = init_database(db_path=db_path, schema_path=schema_path)
    
    # Create cache manager
    cache_manager = CacheManager(db_manager, data_dir=data_dir)
    
    # Migrate existing data if requested
    if migrate:
        logger.info("Migrating existing data...")
        migrated = migrate_existing_data(
            db_manager=db_manager,
            cache_manager=cache_manager,
            data_dir=data_dir,
            dry_run=dry_run
        )
        logger.info(f"Migrated {migrated['zip_files']} ZIP files and {migrated['filings']} filings")
    
    logger.info("Database initialization complete")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Initialize the Form D database")
    parser.add_argument("--db-path", type=str, default="db/formd.db", help="Path to the SQLite database file")
    parser.add_argument("--schema-path", type=str, default="db/schema.sql", help="Path to the SQL schema file")
    parser.add_argument("--data-dir", type=str, default="data", help="Directory containing the data")
    parser.add_argument("--migrate", action="store_true", help="Migrate existing data")
    parser.add_argument("--dry-run", action="store_true", help="Only print what would be done without making changes")
    
    args = parser.parse_args()
    
    main(
        db_path=args.db_path,
        schema_path=args.schema_path,
        data_dir=args.data_dir,
        migrate=args.migrate,
        dry_run=args.dry_run
    )
