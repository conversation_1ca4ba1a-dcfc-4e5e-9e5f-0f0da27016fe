{"cik": "**********", "entityType": "other", "sic": "", "sicDescription": "", "ownerOrg": "", "insiderTransactionForOwnerExists": 0, "insiderTransactionForIssuerExists": 0, "name": "Nationwide BOLI Private Placement Variable Account", "tickers": [], "exchanges": [], "ein": "*********", "lei": null, "description": "", "website": "", "investorWebsite": "", "category": "", "fiscalYearEnd": null, "stateOfIncorporation": "OH", "stateOfIncorporationDescription": "OH", "addresses": {"mailing": {"street1": "ONE NATIONWIDE PLAZA", "street2": null, "city": "COLUMBUS", "stateOrCountry": "OH", "zipCode": "43215", "stateOrCountryDescription": "OH", "isForeignLocation": 0, "foreignStateTerritory": null, "country": null, "countryCode": null}, "business": {"street1": "ONE NATIONWIDE PLAZA", "street2": null, "city": "COLUMBUS", "stateOrCountry": "OH", "zipCode": "43215", "stateOrCountryDescription": "OH", "isForeignLocation": null, "foreignStateTerritory": null, "country": null, "countryCode": null}}, "phone": "************", "flags": "", "formerNames": [{"name": "Nationwide PPVUL Separate Account - 6", "from": "2021-03-25T00:00:00.000Z", "to": "2023-07-21T00:00:00.000Z"}], "filings": {"recent": {"accessionNumber": ["**********-25-000006", "**********-25-000005", "**********-25-000003", "**********-25-000002", "**********-25-000001", "**********-24-000014", "**********-24-000013", "**********-24-000012", "**********-24-000011", "**********-24-000010", "**********-24-000009", "**********-24-000008", "**********-24-000007", "**********-24-000006", "**********-24-000005", "**********-24-000004", "**********-24-000003", "**********-24-000002", "**********-24-000001", "**********-23-000003", "**********-23-000002", "**********-23-000066", "**********-23-000058", "**********-23-000049", "**********-23-000046", "**********-23-000038", "**********-23-000030", "**********-23-000026", "**********-23-000018", "**********-23-000010", "**********-23-000002", "**********-22-000007", "**********-22-000034", "**********-22-000027", "**********-22-000020", "**********-22-000015", "**********-22-000008", "**********-22-000006", "**********-22-000005", "**********-22-000004", "**********-22-000003", "**********-22-000002", "**********-22-000001", "**********-21-000013", "**********-21-000012", "**********-21-000011", "**********-21-000010", "**********-21-000009", "**********-21-000008", "**********-21-000007", "**********-21-000006", "**********-21-000005", "**********-21-000004", "**********-21-000002", "**********-21-000001", "**********-20-000014", "**********-20-000013", "**********-20-000012", "**********-20-000011", "**********-20-000010", "**********-20-000009", "**********-20-000008", "**********-20-000007", "**********-20-000006", "**********-20-000004", "**********-20-000002", "**********-20-000001", "**********-19-000012", "**********-19-000011", "**********-19-000009", "**********-19-000008", "**********-19-000007", "**********-19-000006", "**********-19-000005", "**********-19-000004", "**********-19-000003", "**********-19-000002", "**********-19-000001", "0001601405-19-000001", "**********-18-000011", "**********-18-000010", "**********-18-000009", "**********-18-000008", "**********-18-000007", "**********-18-000006", "**********-18-000023", "**********-18-000005", "**********-18-000004", "**********-18-000003", "**********-18-000002", "**********-18-000001", "**********-17-000018", "**********-17-000017", "**********-17-000016", "**********-17-000015", "**********-17-000014", "**********-17-000013", "**********-17-000012", "**********-17-000011", "**********-17-000010", "**********-17-000009", "**********-17-000008", "**********-17-000007", "**********-17-000006", "**********-17-000005", "**********-17-000004", "**********-17-000003", "**********-17-000002", "**********-17-000001", "**********-16-000006", "**********-16-000005", "**********-16-000004", "**********-16-000003", "9999999997-08-011655", "9999999997-07-012209", "9999999997-06-045182", "9999999997-05-045009"], "filingDate": ["2025-05-19", "2025-04-18", "2025-03-19", "2025-02-19", "2025-01-15", "2024-12-26", "2024-11-20", "2024-10-15", "2024-09-18", "2024-08-16", "2024-08-16", "2024-07-19", "2024-06-18", "2024-05-17", "2024-04-17", "2024-04-17", "2024-03-20", "2024-02-16", "2024-01-17", "2023-12-19", "2023-11-16", "2023-10-25", "2023-09-19", "2023-08-22", "2023-07-21", "2023-06-20", "2023-05-22", "2023-04-21", "2023-03-20", "2023-02-23", "2023-01-23", "2022-12-16", "2022-11-16", "2022-10-31", "2022-09-21", "2022-08-22", "2022-07-19", "2022-06-27", "2022-05-27", "2022-04-25", "2022-03-22", "2022-02-23", "2022-01-25", "2021-12-21", "2021-11-22", "2021-10-27", "2021-09-17", "2021-08-26", "2021-07-23", "2021-06-24", "2021-05-26", "2021-04-21", "2021-03-25", "2021-02-23", "2021-01-19", "2020-12-18", "2020-11-20", "2020-10-21", "2020-09-18", "2020-08-24", "2020-07-17", "2020-06-22", "2020-05-20", "2020-04-20", "2020-03-20", "2020-02-26", "2020-01-31", "2019-12-11", "2019-10-25", "2019-09-23", "2019-08-26", "2019-07-16", "2019-06-19", "2019-05-16", "2019-04-16", "2019-03-15", "2019-02-20", "2019-01-16", "2019-01-16", "2018-12-18", "2018-11-19", "2018-10-16", "2018-09-18", "2018-08-17", "2018-07-18", "2018-06-19", "2018-05-18", "2018-04-19", "2018-03-16", "2018-02-20", "2018-01-11", "2017-12-05", "2017-12-05", "2017-12-05", "2017-10-25", "2017-10-25", "2017-10-04", "2017-09-05", "2017-08-18", "2017-08-16", "2017-07-12", "2017-06-22", "2017-06-22", "2017-06-06", "2017-05-03", "2017-04-04", "2017-03-31", "2017-02-10", "2017-02-02", "2016-11-30", "2016-11-30", "2016-11-22", "2016-10-31", "2008-02-29", "2007-03-05", "2006-10-27", "2005-10-28"], "reportDate": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "acceptanceDateTime": ["2025-05-19T12:28:36.000Z", "2025-04-18T13:17:40.000Z", "2025-03-19T14:20:54.000Z", "2025-02-19T21:02:50.000Z", "2025-01-15T21:26:41.000Z", "2024-12-26T14:28:37.000Z", "2024-11-20T21:23:29.000Z", "2024-10-15T18:28:44.000Z", "2024-09-18T19:32:46.000Z", "2024-08-16T19:55:52.000Z", "2024-08-16T19:37:19.000Z", "2024-07-19T12:54:42.000Z", "2024-06-18T14:28:34.000Z", "2024-05-17T20:36:55.000Z", "2024-04-17T19:20:06.000Z", "2024-04-17T19:16:49.000Z", "2024-03-20T14:33:09.000Z", "2024-02-16T19:35:58.000Z", "2024-01-17T21:37:55.000Z", "2023-12-19T15:55:12.000Z", "2023-11-16T17:54:17.000Z", "2023-10-25T14:01:56.000Z", "2023-09-19T18:31:13.000Z", "2023-08-22T13:34:23.000Z", "2023-07-21T13:51:27.000Z", "2023-06-20T13:15:03.000Z", "2023-05-22T17:51:59.000Z", "2023-04-21T16:01:53.000Z", "2023-03-20T16:33:37.000Z", "2023-02-23T13:15:57.000Z", "2023-01-23T13:43:57.000Z", "2022-12-16T18:02:19.000Z", "2022-11-16T13:12:24.000Z", "2022-10-31T14:16:27.000Z", "2022-09-21T11:46:07.000Z", "2022-08-22T12:46:04.000Z", "2022-07-19T11:51:23.000Z", "2022-06-27T15:27:04.000Z", "2022-05-27T19:17:18.000Z", "2022-04-25T14:28:33.000Z", "2022-03-22T16:54:45.000Z", "2022-02-23T18:12:14.000Z", "2022-01-25T12:42:27.000Z", "2021-12-21T16:33:10.000Z", "2021-11-22T18:22:00.000Z", "2021-10-27T17:12:02.000Z", "2021-09-17T14:03:23.000Z", "2021-08-26T20:02:19.000Z", "2021-07-23T12:58:12.000Z", "2021-06-24T14:30:22.000Z", "2021-05-26T15:01:49.000Z", "2021-04-21T14:49:28.000Z", "2021-03-25T19:44:04.000Z", "2021-02-23T14:48:50.000Z", "2021-01-19T16:01:20.000Z", "2020-12-18T16:37:02.000Z", "2020-11-20T19:54:25.000Z", "2020-10-21T19:00:43.000Z", "2020-09-18T14:18:17.000Z", "2020-08-24T13:19:54.000Z", "2020-07-17T15:11:01.000Z", "2020-06-22T17:40:32.000Z", "2020-05-20T18:17:38.000Z", "2020-04-20T18:03:25.000Z", "2020-03-20T16:34:43.000Z", "2020-02-26T16:40:52.000Z", "2020-01-31T17:01:24.000Z", "2019-12-11T16:19:08.000Z", "2019-10-25T14:12:53.000Z", "2019-09-23T17:45:25.000Z", "2019-08-26T18:11:32.000Z", "2019-07-16T14:34:03.000Z", "2019-06-19T14:15:25.000Z", "2019-05-16T13:53:44.000Z", "2019-04-16T13:29:24.000Z", "2019-03-15T17:54:11.000Z", "2019-02-20T14:37:18.000Z", "2019-01-16T14:43:37.000Z", "2019-01-16T14:17:00.000Z", "2018-12-18T18:34:57.000Z", "2018-11-19T14:45:42.000Z", "2018-10-16T13:07:30.000Z", "2018-09-18T17:01:01.000Z", "2018-08-17T13:09:24.000Z", "2018-07-18T12:04:12.000Z", "2018-06-19T11:16:14.000Z", "2018-05-18T14:46:37.000Z", "2018-04-19T13:27:18.000Z", "2018-03-16T12:45:42.000Z", "2018-02-20T13:52:30.000Z", "2018-01-11T20:41:12.000Z", "2017-12-05T14:19:49.000Z", "2017-12-05T14:10:10.000Z", "2017-12-05T14:03:43.000Z", "2017-10-25T12:41:30.000Z", "2017-10-25T12:34:48.000Z", "2017-10-04T14:08:40.000Z", "2017-09-05T18:03:42.000Z", "2017-08-18T18:59:55.000Z", "2017-08-16T13:44:53.000Z", "2017-07-12T11:51:09.000Z", "2017-06-22T13:16:51.000Z", "2017-06-22T13:06:37.000Z", "2017-06-06T11:34:30.000Z", "2017-05-03T17:32:46.000Z", "2017-04-04T12:12:55.000Z", "2017-03-31T13:30:04.000Z", "2017-02-10T13:59:46.000Z", "2017-02-02T18:21:26.000Z", "2016-11-30T20:03:49.000Z", "2016-11-30T19:56:45.000Z", "2016-11-22T18:58:18.000Z", "2016-10-31T13:25:35.000Z", "2008-03-18T17:54:29.000Z", "2007-03-16T14:39:47.000Z", "2006-11-09T14:24:04.000Z", "2005-11-07T19:52:53.000Z"], "act": ["33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "34", "34", "34", "34"], "form": ["D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "REGDEX/A", "REGDEX/A", "REGDEX/A", "REGDEX/A"], "fileNumber": ["021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-303013", "021-300048", "021-300047", "021-300046", "021-297361", "021-297360", "021-295956", "021-294044", "021-293131", "021-292901", "021-290479", "021-289230", "021-289229", "021-288096", "021-285955", "021-283966", "021-283764", "021-280464", "021-279899", "021-275374", "021-275372", "021-274981", "021-273473", "021-82896", "021-82896", "021-82896", "021-82896"], "filmNumber": ["25962686", "25849311", "25751142", "25639807", "25533215", "241577136", "241480699", "241371357", "241307236", "241216567", "241216347", "241126169", "241050463", "24960463", "24850618", "24850607", "24766684", "24647575", "24538929", "231495918", "231413671", "231344277", "231263332", "231191129", "231101276", "231023705", "23943768", "23835319", "23745486", "23656030", "23542618", "221467361", "221392990", "221344182", "221255016", "221182303", "221090406", "221042313", "22976457", "22847743", "22758263", "22662300", "22551121", "211507870", "211431240", "211352021", "211259416", "211212364", "211109157", "211041188", "21963745", "21839984", "21772172", "21663404", "21534547", "201398572", "201332003", "201250416", "201182939", "201124834", "201033076", "20978090", "20897496", "20802221", "20731120", "20653922", "20564054", "191279288", "191168030", "191107388", "191052199", "19956395", "19905328", "19830735", "19750054", "19684469", "19617255", "19528416", "19528359", "181240032", "181191397", "181123605", "181075194", "181024828", "18957683", "18906193", "18845427", "18762606", "18694296", "18623222", "18523624", "171238775", "171238760", "171238746", "171152129", "171152124", "171120600", "171068231", "171040937", "171035467", "17961041", "17924129", "17924119", "17893038", "17808388", "17736221", "17728266", "17589800", "17567641", "162025099", "162025078", "162012726", "161960207", "08041225", "07046075", "06061244", "05068224"], "items": ["06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06", "06", "06", "06"], "core_type": ["D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "REGDEX/A", "REGDEX/A", "REGDEX/A", "REGDEX/A"], "size": [9259, 9162, 9169, 9169, 9169, 9072, 9072, 9072, 8981, 8981, 8788, 8788, 8788, 8787, 8787, 8787, 8787, 8787, 8787, 8788, 8755, 8755, 8755, 8755, 8523, 8523, 8522, 8522, 8522, 8522, 8426, 8427, 8523, 8523, 8523, 8523, 8523, 8523, 8522, 8522, 8522, 8522, 8522, 8523, 8523, 8523, 8523, 8523, 8523, 8523, 8522, 8522, 8522, 8473, 8473, 8474, 8474, 8474, 8474, 8474, 8568, 8567, 8570, 8570, 8570, 8571, 8495, 8570, 8570, 8570, 8570, 8475, 8475, 8475, 8475, 8475, 8474, 8475, 8475, 8476, 8476, 8476, 8476, 8476, 8475, 8475, 8475, 8475, 8475, 8475, 8306, 11189, 11244, 11237, 11238, 13154, 11220, 12264, 11223, 12206, 11181, 11226, 11229, 12183, 11227, 13144, 12205, 12226, 12196, 11189, 11237, 11245, 7170, 1575, 1575, 1575, 1575], "isXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "isInlineXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "primaryDocument": ["xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "9999999997-08-011655.paper", "9999999997-07-012209.paper", "9999999997-06-045182.paper", "9999999997-05-045009.paper"], "primaryDocDescription": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "AUTO-GENERATED PAPER DOCUMENT", "AUTO-GENERATED PAPER DOCUMENT", "AUTO-GENERATED PAPER DOCUMENT", "AUTO-GENERATED PAPER DOCUMENT"]}, "files": []}}