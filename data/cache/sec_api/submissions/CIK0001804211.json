{"cik": "**********", "entityType": "other", "sic": "", "sicDescription": "", "ownerOrg": "", "insiderTransactionForOwnerExists": 0, "insiderTransactionForIssuerExists": 0, "name": "Nationwide PPVUL Separate Account - 3", "tickers": [], "exchanges": [], "ein": "*********", "lei": null, "description": "", "website": "", "investorWebsite": "", "category": "", "fiscalYearEnd": "1231", "stateOfIncorporation": "OH", "stateOfIncorporationDescription": "OH", "addresses": {"mailing": {"street1": "ONE NATIONWIDE PLAZA", "street2": null, "city": "COLUMBUS", "stateOrCountry": "OH", "zipCode": "43215", "stateOrCountryDescription": "OH", "isForeignLocation": 0, "foreignStateTerritory": null, "country": null, "countryCode": null}, "business": {"street1": "ONE NATIONWIDE PLAZA", "street2": null, "city": "COLUMBUS", "stateOrCountry": "OH", "zipCode": "43215", "stateOrCountryDescription": "OH", "isForeignLocation": null, "foreignStateTerritory": null, "country": null, "countryCode": null}}, "phone": "************", "flags": "", "formerNames": [{"name": "Nationwide Private Placement Variable Account PP3", "from": "2020-02-26T00:00:00.000Z", "to": "2021-02-23T00:00:00.000Z"}], "filings": {"recent": {"accessionNumber": ["**********-25-000006", "**********-25-000005", "**********-25-000003", "**********-25-000002", "**********-25-000001", "**********-24-000012", "**********-24-000011", "**********-24-000010", "**********-24-000009", "**********-24-000008", "**********-24-000007", "**********-24-000006", "**********-24-000005", "**********-24-000004", "**********-24-000003", "**********-24-000002", "**********-24-000001", "**********-23-000008", "**********-23-000007", "**********-23-000006", "**********-23-000061", "**********-23-000051", "**********-23-000001", "**********-23-000040", "**********-23-000032", "**********-23-000028", "**********-23-000020", "**********-23-000012", "**********-23-000004", "**********-22-000012", "**********-22-000007", "**********-22-000029", "**********-22-000022", "**********-22-000017", "**********-22-000010", "**********-22-000006", "**********-22-000005", "**********-22-000004", "**********-22-000003", "**********-22-000002", "**********-22-000001", "**********-21-000014", "**********-21-000013", "**********-21-000012", "**********-21-000011", "**********-21-000010", "**********-21-000009", "**********-21-000008", "**********-21-000007", "**********-21-000006", "**********-21-000005", "**********-21-000002", "**********-21-000001", "**********-20-000012", "**********-20-000011", "**********-20-000010", "**********-20-000009", "**********-20-000008", "**********-20-000007", "**********-20-000006", "**********-20-000005", "**********-20-000004", "**********-20-000002", "**********-20-000001"], "filingDate": ["2025-05-19", "2025-04-18", "2025-03-19", "2025-02-19", "2025-01-15", "2024-12-26", "2024-11-20", "2024-10-15", "2024-09-18", "2024-08-16", "2024-07-19", "2024-06-18", "2024-05-17", "2024-04-17", "2024-03-20", "2024-02-16", "2024-01-17", "2023-12-19", "2023-11-16", "2023-10-25", "2023-09-19", "2023-08-22", "2023-07-21", "2023-06-20", "2023-05-22", "2023-04-21", "2023-03-20", "2023-02-23", "2023-01-23", "2022-12-16", "2022-11-16", "2022-10-31", "2022-09-21", "2022-08-22", "2022-07-19", "2022-06-27", "2022-05-27", "2022-04-25", "2022-03-22", "2022-02-23", "2022-01-25", "2021-12-21", "2021-11-22", "2021-10-27", "2021-09-17", "2021-08-26", "2021-07-23", "2021-06-24", "2021-05-26", "2021-04-21", "2021-03-25", "2021-02-23", "2021-01-19", "2020-12-18", "2020-11-20", "2020-10-21", "2020-09-18", "2020-08-24", "2020-07-17", "2020-06-22", "2020-05-21", "2020-04-20", "2020-03-20", "2020-02-26"], "reportDate": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "acceptanceDateTime": ["2025-05-19T12:39:59.000Z", "2025-04-18T17:18:50.000Z", "2025-03-19T14:29:44.000Z", "2025-02-19T21:14:45.000Z", "2025-01-15T21:39:37.000Z", "2024-12-26T14:44:55.000Z", "2024-11-20T21:32:03.000Z", "2024-10-15T18:37:11.000Z", "2024-09-18T19:47:50.000Z", "2024-08-16T19:58:45.000Z", "2024-07-19T13:04:58.000Z", "2024-06-18T15:01:41.000Z", "2024-05-17T20:47:39.000Z", "2024-04-17T19:34:12.000Z", "2024-03-20T14:49:04.000Z", "2024-02-16T19:45:43.000Z", "2024-01-17T21:50:35.000Z", "2023-12-19T16:10:49.000Z", "2023-11-16T18:01:31.000Z", "2023-10-25T19:15:34.000Z", "2023-09-19T18:47:10.000Z", "2023-08-22T13:44:25.000Z", "2023-07-21T14:19:59.000Z", "2023-06-20T13:26:58.000Z", "2023-05-22T18:05:07.000Z", "2023-04-21T16:15:30.000Z", "2023-03-20T16:57:24.000Z", "2023-02-23T13:28:59.000Z", "2023-01-23T13:55:41.000Z", "2022-12-16T18:13:56.000Z", "2022-11-16T13:27:09.000Z", "2022-10-31T15:00:28.000Z", "2022-09-21T11:59:13.000Z", "2022-08-22T12:57:51.000Z", "2022-07-19T12:11:56.000Z", "2022-06-27T15:49:23.000Z", "2022-05-27T19:36:15.000Z", "2022-04-25T14:56:48.000Z", "2022-03-22T17:03:24.000Z", "2022-02-23T18:32:12.000Z", "2022-01-25T14:00:45.000Z", "2021-12-21T16:50:48.000Z", "2021-11-22T18:34:40.000Z", "2021-10-27T17:23:02.000Z", "2021-09-17T14:20:52.000Z", "2021-08-26T20:28:13.000Z", "2021-07-23T13:07:21.000Z", "2021-06-24T15:35:59.000Z", "2021-05-26T15:12:51.000Z", "2021-04-21T15:49:46.000Z", "2021-03-25T19:50:56.000Z", "2021-02-23T15:09:26.000Z", "2021-01-19T16:12:28.000Z", "2020-12-18T16:50:10.000Z", "2020-11-20T20:10:57.000Z", "2020-10-21T19:22:11.000Z", "2020-09-18T14:59:08.000Z", "2020-08-24T13:47:24.000Z", "2020-07-17T15:26:20.000Z", "2020-06-22T18:11:16.000Z", "2020-05-21T12:01:07.000Z", "2020-04-20T19:43:04.000Z", "2020-03-20T16:58:09.000Z", "2020-02-26T17:12:27.000Z"], "act": ["33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33"], "form": ["D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D"], "fileNumber": ["021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377", "021-361377"], "filmNumber": ["25962738", "25849893", "25751164", "25640175", "25533382", "241577502", "241480839", "241371390", "241307342", "241216578", "241126297", "241050532", "24960687", "24850681", "24766760", "24647628", "24539070", "231495978", "231413699", "231345411", "231263390", "231191149", "231101365", "231023729", "23943841", "23835359", "23745548", "23656052", "23542635", "221467401", "221393044", "221344375", "221255025", "221182335", "221090433", "221042572", "22976597", "22847872", "22758288", "22662372", "22551225", "211507914", "211431319", "211352091", "211259431", "211212679", "211109220", "211041466", "21963832", "21840260", "21772207", "21663453", "21534577", "201398620", "201332104", "201250476", "201183011", "201124947", "201033108", "20978172", "20900014", "20802522", "20731216", "20654131"], "items": ["06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7"], "core_type": ["D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D"], "size": [9455, 9455, 9363, 9363, 9268, 9269, 9269, 9269, 9269, 9269, 9175, 9175, 9174, 9174, 9174, 9174, 9173, 9174, 9141, 9141, 9046, 9037, 9046, 9046, 8952, 8952, 8952, 8952, 8952, 8953, 8859, 8859, 8859, 8764, 8764, 8668, 8667, 8667, 8667, 8667, 8567, 8568, 8568, 8474, 8568, 8568, 8567, 8567, 8566, 8471, 8195, 8145, 8050, 8051, 8051, 8051, 7961, 7867, 7867, 7676, 7582, 7393, 7393, 7299], "isXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "isInlineXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "primaryDocument": ["xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml"], "primaryDocDescription": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]}, "files": []}}