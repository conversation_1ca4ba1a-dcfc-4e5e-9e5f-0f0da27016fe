{"cik": "**********", "entityType": "other", "sic": "", "sicDescription": "", "ownerOrg": "", "insiderTransactionForOwnerExists": 0, "insiderTransactionForIssuerExists": 0, "name": "NATIONWIDE PRIVATE PLACEMENT VARIABLE ACCOUNT", "tickers": [], "exchanges": [], "ein": "*********", "lei": null, "description": "", "website": "", "investorWebsite": "", "category": "", "fiscalYearEnd": "1231", "stateOfIncorporation": "OH", "stateOfIncorporationDescription": "OH", "addresses": {"mailing": {"street1": "ONE NATIONWIDE PLAZA", "street2": null, "city": "COLUMBUS", "stateOrCountry": "OH", "zipCode": "43215", "stateOrCountryDescription": "OH", "isForeignLocation": 0, "foreignStateTerritory": null, "country": null, "countryCode": null}, "business": {"street1": "ONE NATIONWIDE PLAZA", "street2": null, "city": "COLUMBUS", "stateOrCountry": "OH", "zipCode": "43215", "stateOrCountryDescription": "OH", "isForeignLocation": null, "foreignStateTerritory": null, "country": null, "countryCode": null}}, "phone": "************", "flags": "", "formerNames": [{"name": "NATIONWIDE PRIVATE PLACEMENT VARIBLE ACCOUNT", "from": "2002-03-08T00:00:00.000Z", "to": "2006-05-03T00:00:00.000Z"}], "filings": {"recent": {"accessionNumber": ["**********-25-000006", "**********-25-000005", "**********-25-000003", "**********-25-000002", "**********-25-000001", "**********-24-000012", "**********-24-000011", "**********-24-000010", "**********-24-000009", "**********-24-000008", "**********-24-000007", "**********-24-000006", "**********-24-000005", "**********-24-000004", "**********-24-000003", "**********-24-000002", "**********-24-000001", "**********-23-000068", "**********-23-000067", "**********-23-000065", "**********-23-000057", "**********-23-000048", "**********-23-000045", "**********-23-000037", "**********-23-000029", "**********-23-000025", "**********-23-000017", "**********-23-000009", "**********-23-000001", "**********-22-000036", "**********-22-000033", "**********-22-000026", "**********-22-000019", "**********-22-000014", "**********-22-000007", "**********-22-000006", "**********-22-000005", "**********-22-000004", "**********-22-000003", "**********-22-000002", "**********-22-000001", "**********-21-000012", "**********-21-000011", "**********-21-000010", "**********-21-000009", "**********-21-000008", "**********-21-000007", "**********-21-000006", "**********-21-000005", "**********-21-000004", "**********-21-000003", "**********-21-000002", "**********-21-000001", "**********-20-000012", "**********-20-000011", "**********-20-000010", "**********-20-000009", "**********-20-000008", "**********-20-000007", "**********-20-000006", "**********-20-000005", "**********-20-000004", "**********-20-000003", "**********-20-000002", "**********-20-000001", "**********-19-000011", "**********-19-000010", "**********-19-000009", "0001343394-19-000010", "**********-19-000008", "**********-19-000007", "**********-19-000006", "**********-19-000005", "**********-19-000004", "**********-19-000003", "**********-19-000002", "**********-19-000001", "**********-18-000030", "**********-18-000029", "**********-18-000028", "**********-18-000027", "**********-18-000026", "**********-18-000025", "**********-18-000024", "**********-18-000022", "**********-18-000021", "**********-18-000020", "**********-18-000019", "**********-18-000018", "**********-18-000017", "**********-18-000016", "**********-18-000015", "**********-18-000014", "**********-18-000013", "**********-18-000012", "**********-18-000011", "**********-18-000010", "**********-18-000009", "**********-18-000008", "**********-18-000007", "**********-18-000006", "**********-18-000005", "**********-18-000004", "**********-18-000003", "**********-18-000002", "**********-18-000001", "**********-17-000381", "**********-17-000380", "**********-17-000379", "**********-17-000378", "**********-17-000377", "**********-17-000376", "**********-17-000375", "**********-17-000374", "**********-17-000373", "**********-17-000372", "**********-17-000371", "**********-17-000370", "**********-17-000369", "**********-17-000368", "**********-17-000367", "**********-17-000366", "**********-17-000365", "**********-17-000364", "**********-17-000363", "**********-17-000362", "**********-17-000361", "**********-17-000360", "**********-17-000359", "**********-17-000358", "**********-17-000357", "**********-17-000356", "**********-17-000355", "**********-17-000354", "**********-17-000353", "**********-17-000352", "**********-17-000351", "**********-17-000350", "**********-17-000349", "**********-17-000348", "**********-17-000347", "**********-17-000346", "**********-17-000345", "**********-17-000344", "**********-17-000343", "**********-17-000342", "**********-17-000341", "**********-17-000340", "**********-17-000339", "**********-17-000338", "**********-17-000337", "**********-17-000336", "**********-17-000335", "**********-17-000334", "**********-17-000333", "**********-17-000332", "**********-17-000331", "**********-17-000330", "**********-17-000329", "**********-17-000328", "**********-17-000327", "**********-17-000326", "**********-17-000325", "**********-17-000324", "**********-17-000323", "**********-17-000322", "**********-17-000321", "**********-17-000320", "**********-17-000319", "**********-17-000318", "**********-17-000317", "**********-17-000316", "**********-17-000315", "**********-17-000314", "**********-17-000313", "**********-17-000312", "**********-17-000311", "**********-17-000310", "**********-17-000309", "**********-17-000308", "**********-17-000307", "**********-17-000306", "**********-17-000305", "**********-17-000304", "**********-17-000303", "**********-17-000302", "**********-17-000301", "**********-17-000300", "**********-17-000299", "**********-17-000298", "**********-17-000297", "**********-17-000296", "**********-17-000295", "**********-17-000294", "**********-17-000293", "**********-17-000292", "**********-17-000291", "**********-17-000290", "**********-17-000289", "**********-17-000288", "**********-17-000287", "**********-17-000286", "**********-17-000285", "**********-17-000284", "**********-17-000283", "**********-17-000282", "**********-17-000281", "**********-17-000280", "**********-17-000279", "**********-17-000278", "**********-17-000277", "**********-17-000276", "**********-17-000275", "**********-17-000274", "**********-17-000273", "**********-17-000272", "**********-17-000271", "**********-17-000270", "**********-17-000269", "**********-17-000268", "**********-17-000267", "**********-17-000266", "**********-17-000265", "**********-17-000264", "**********-17-000263", "**********-17-000262", "**********-17-000261", "**********-17-000260", "**********-17-000259", "**********-17-000258", "**********-17-000257", "**********-17-000256", "**********-17-000255", "**********-17-000254", "**********-17-000253", "**********-17-000252", "**********-17-000251", "**********-17-000250", "**********-17-000249", "**********-17-000248", "**********-17-000247", "**********-17-000246", "**********-17-000245", "**********-17-000244", "**********-17-000243", "**********-17-000242", "**********-17-000241", "**********-17-000240", "**********-17-000239", "**********-17-000238", "**********-17-000237", "**********-17-000236", "**********-17-000235", "**********-17-000234", "**********-17-000233", "**********-17-000232", "**********-17-000231", "**********-17-000230", "**********-17-000229", "**********-17-000228", "**********-17-000227", "**********-17-000226", "**********-17-000225", "**********-17-000224", "**********-17-000223", "**********-17-000222", "**********-17-000221", "**********-17-000220", "**********-17-000219", "**********-17-000218", "**********-17-000217", "**********-17-000216", "**********-17-000215", "**********-17-000214", "**********-17-000213", "**********-17-000212", "**********-17-000211", "**********-17-000210", "**********-17-000209", "**********-17-000208", "**********-17-000207", "**********-17-000206", "**********-17-000205", "**********-17-000204", "**********-17-000203", "**********-17-000202", "**********-17-000201", "**********-17-000200", "**********-17-000199", "**********-17-000198", "**********-17-000197", "**********-17-000196", "**********-17-000195", "**********-17-000194", "**********-17-000193", "**********-17-000192", "**********-17-000191", "**********-17-000190", "**********-17-000189", "**********-17-000188", "**********-17-000187", "**********-17-000186", "**********-17-000185", "**********-17-000184", "**********-17-000183", "**********-17-000182", "**********-17-000181", "**********-17-000180", "**********-17-000179", "**********-17-000178", "**********-17-000177", "**********-17-000176", "**********-17-000175", "**********-17-000174", "**********-17-000173", "**********-17-000172", "**********-17-000171", "**********-17-000170", "**********-17-000169", "**********-17-000168", "**********-17-000167", "**********-17-000166", "**********-17-000165", "**********-17-000164", "**********-17-000163", "**********-17-000162", "**********-17-000161", "**********-17-000160", "**********-17-000159", "**********-17-000158", "**********-17-000157", "**********-17-000156", "**********-17-000155", "**********-17-000154", "**********-17-000153", "**********-17-000152", "**********-17-000151", "**********-17-000150", "**********-17-000149", "**********-17-000148", "**********-17-000147", "**********-17-000146", "**********-17-000145", "**********-17-000144", "**********-17-000143", "**********-17-000142", "**********-17-000141", "**********-17-000140", "**********-17-000139", "**********-17-000138", "**********-17-000137", "**********-17-000136", "**********-17-000135", "**********-17-000134", "**********-17-000133", "**********-17-000132", "**********-17-000131", "**********-17-000130", "**********-17-000129", "**********-17-000128", "**********-17-000127", "**********-17-000126", "**********-17-000125", "**********-17-000124", "**********-17-000123", "**********-17-000122", "**********-17-000121", "**********-17-000120", "**********-17-000119", "**********-17-000118", "**********-17-000117", "**********-17-000116", "**********-17-000115", "**********-17-000114", "**********-17-000113", "**********-17-000112", "**********-17-000111", "**********-17-000110", "**********-17-000109", "**********-17-000108", "**********-17-000107", "**********-17-000106", "**********-17-000105", "**********-17-000104", "**********-17-000103", "**********-17-000102", "**********-17-000101", "**********-17-000100", "**********-17-000099", "**********-17-000098", "**********-17-000097", "**********-17-000096", "**********-17-000095", "**********-17-000094", "**********-17-000093", "**********-17-000092", "**********-17-000091", "**********-17-000090", "**********-17-000089", "**********-17-000088", "**********-17-000087", "**********-17-000086", "**********-17-000085", "**********-17-000084", "**********-17-000083", "**********-17-000082", "**********-17-000081", "**********-17-000080", "**********-17-000079", "**********-17-000078", "**********-17-000077", "**********-17-000076", "**********-17-000075", "**********-17-000074", "**********-17-000073", "**********-17-000072", "**********-17-000071", "**********-17-000070", "**********-17-000069", "**********-17-000068", "**********-17-000067", "**********-17-000066", "**********-17-000065", "**********-17-000064", "**********-17-000063", "**********-17-000062", "**********-17-000061", "**********-17-000060", "**********-17-000059", "**********-17-000058", "**********-17-000057", "**********-17-000056", "**********-17-000055", "**********-17-000054", "**********-17-000053", "**********-17-000052", "**********-17-000051", "**********-17-000050", "**********-17-000049", "**********-17-000048", "**********-17-000047", "**********-17-000046", "**********-17-000045", "**********-17-000044", "**********-17-000043", "**********-17-000042", "**********-17-000041", "**********-17-000040", "**********-17-000039", "**********-17-000038", "**********-17-000037", "**********-17-000036", "**********-17-000035", "**********-17-000034", "**********-17-000033", "**********-17-000032", "**********-17-000031", "**********-17-000030", "**********-17-000029", "**********-17-000028", "**********-17-000027", "**********-17-000026", "**********-17-000025", "**********-17-000024", "**********-17-000023", "**********-17-000022", "**********-17-000021", "**********-17-000020", "**********-17-000019", "**********-17-000018", "**********-17-000017", "**********-17-000016", "**********-17-000015", "**********-17-000014", "**********-17-000013", "**********-17-000012", "**********-17-000011", "**********-17-000010", "**********-17-000009", "**********-17-000008", "**********-17-000007", "**********-17-000006", "**********-17-000005", "**********-17-000004", "**********-17-000003", "**********-17-000002", "**********-17-000001", "**********-16-000767", "**********-16-000766", "**********-16-000765", "**********-16-000764", "**********-16-000763", "**********-16-000762", "**********-16-000761", "**********-16-000760", "**********-16-000759", "**********-16-000758", "**********-16-000757", "**********-16-000756", "**********-16-000755", "**********-16-000754", "**********-16-000753", "**********-16-000752", "**********-16-000751", "**********-16-000750", "**********-16-000749", "**********-16-000748", "**********-16-000747", "**********-16-000746", "**********-16-000745", "**********-16-000744", "**********-16-000743", "**********-16-000742", "**********-16-000741", "**********-16-000740", "**********-16-000739", "**********-16-000738", "**********-16-000737", "**********-16-000736", "**********-16-000735", "**********-16-000734", "**********-16-000733", "**********-16-000732", "**********-16-000731", "**********-16-000730", "**********-16-000729", "**********-16-000728", "**********-16-000727", "**********-16-000726", "**********-16-000725", "**********-16-000724", "**********-16-000723", "**********-16-000722", "**********-16-000721", "**********-16-000720", "**********-16-000719", "**********-16-000718", "**********-16-000717", "**********-16-000716", "**********-16-000715", "**********-16-000714", "**********-16-000713", "**********-16-000712", "**********-16-000711", "**********-16-000710", "**********-16-000709", "**********-16-000708", "**********-16-000707", "**********-16-000706", "**********-16-000705", "**********-16-000704", "**********-16-000703", "**********-16-000702", "**********-16-000701", "**********-16-000700", "**********-16-000699", "**********-16-000698", "**********-16-000697", "**********-16-000696", "**********-16-000695", "**********-16-000694", "**********-16-000693", "**********-16-000692", "**********-16-000691", "**********-16-000690", "**********-16-000689", "**********-16-000688", "**********-16-000687", "**********-16-000686", "**********-16-000685", "**********-16-000684", "**********-16-000683", "**********-16-000682", "**********-16-000681", "**********-16-000680", "**********-16-000679", "**********-16-000678", "**********-16-000677", "**********-16-000676", "**********-16-000675", "**********-16-000674", "**********-16-000673", "**********-16-000672", "**********-16-000671", "**********-16-000670", "**********-16-000669", "**********-16-000668", "**********-16-000667", "**********-16-000666", "**********-16-000665", "**********-16-000664", "**********-16-000663", "**********-16-000662", "**********-16-000661", "**********-16-000660", "**********-16-000659", "**********-16-000658", "**********-16-000657", "**********-16-000656", "**********-16-000655", "**********-16-000654", "**********-16-000653", "**********-16-000652", "**********-16-000651", "**********-16-000650", "**********-16-000649", "**********-16-000648", "**********-16-000647", "**********-16-000646", "**********-16-000645", "**********-16-000644", "**********-16-000643", "**********-16-000642", "**********-16-000641", "**********-16-000640", "**********-16-000639", "**********-16-000638", "**********-16-000637", "**********-16-000636", "**********-16-000635", "**********-16-000634", "**********-16-000633", "**********-16-000632", "**********-16-000631", "**********-16-000630", "**********-16-000629", "**********-16-000628", "**********-16-000627", "**********-16-000626", "**********-16-000625", "**********-16-000624", "**********-16-000623", "**********-16-000622", "**********-16-000621", "**********-16-000620", "**********-16-000619", "**********-16-000618", "**********-16-000617", "**********-16-000616", "**********-16-000615", "**********-16-000614", "**********-16-000613", "**********-16-000612", "**********-16-000611", "**********-16-000610", "**********-16-000609", "**********-16-000608", "**********-16-000607", "**********-16-000606", "**********-16-000605", "**********-16-000604", "**********-16-000603", "**********-16-000602", "**********-16-000601", "**********-16-000600", "**********-16-000599", "**********-16-000598", "**********-16-000597", "**********-16-000596", "**********-16-000595", "**********-16-000594", "**********-16-000593", "**********-16-000592", "**********-16-000591", "**********-16-000590", "**********-16-000589", "**********-16-000588", "**********-16-000587", "**********-16-000586", "**********-16-000585", "**********-16-000584", "**********-16-000583", "**********-16-000582", "**********-16-000581", "**********-16-000580", "**********-16-000579", "**********-16-000578", "**********-16-000577", "**********-16-000576", "**********-16-000575", "**********-16-000574", "**********-16-000573", "**********-16-000572", "**********-16-000571", "**********-16-000570", "**********-16-000569", "**********-16-000568", "**********-16-000567", "**********-16-000566", "**********-16-000565", "**********-16-000564", "**********-16-000563", "**********-16-000562", "**********-16-000561", "**********-16-000560", "**********-16-000559", "**********-16-000558", "**********-16-000557", "**********-16-000556", "**********-16-000555", "**********-16-000554", "**********-16-000553", "**********-16-000552", "**********-16-000551", "**********-16-000550", "**********-16-000549", "**********-16-000548", "**********-16-000547", "**********-16-000546", "**********-16-000545", "**********-16-000544", "**********-16-000543", "**********-16-000542", "**********-16-000541", "**********-16-000540", "**********-16-000539", "**********-16-000538", "**********-16-000537", "**********-16-000536", "**********-16-000535", "**********-16-000534", "**********-16-000533", "**********-16-000532", "**********-16-000531", "**********-16-000530", "**********-16-000529", "**********-16-000528", "**********-16-000527", "**********-16-000526", "**********-16-000525", "**********-16-000524", "**********-16-000523", "**********-16-000522", "**********-16-000521", "**********-16-000520", "**********-16-000519", "**********-16-000518", "**********-16-000517", "**********-16-000516", "**********-16-000515", "**********-16-000514", "**********-16-000513", "**********-16-000512", "**********-16-000511", "**********-16-000510", "**********-16-000509", "**********-16-000508", "**********-16-000507", "**********-16-000506", "**********-16-000505", "**********-16-000504", "**********-16-000503", "**********-16-000502", "**********-16-000501", "**********-16-000500", "**********-16-000499", "**********-16-000498", "**********-16-000497", "**********-16-000496", "**********-16-000495", "**********-16-000494", "**********-16-000493", "**********-16-000492", "**********-16-000491", "**********-16-000490", "**********-16-000489", "**********-16-000488", "**********-16-000487", "**********-16-000486", "**********-16-000485", "**********-16-000484", "**********-16-000483", "**********-16-000482", "**********-16-000481", "**********-16-000480", "**********-16-000479", "**********-16-000478", "**********-16-000477", "**********-16-000476", "**********-16-000475", "**********-16-000474", "**********-16-000473", "**********-16-000472", "**********-16-000471", "**********-16-000470", "**********-16-000469", "**********-16-000468", "**********-16-000467", "**********-16-000466", "**********-16-000465", "**********-16-000464", "**********-16-000463", "**********-16-000462", "**********-16-000461", "**********-16-000460", "**********-16-000459", "**********-16-000458", "**********-16-000457", "**********-16-000456", "**********-16-000455", "**********-16-000454", "**********-16-000453", "**********-16-000452", "**********-16-000451", "**********-16-000450", "**********-16-000449", "**********-16-000448", "**********-16-000447", "**********-16-000446", "**********-16-000445", "**********-16-000444", "**********-16-000443", "**********-16-000442", "**********-16-000441", "**********-16-000440", "**********-16-000439", "**********-16-000438", "**********-16-000437", "**********-16-000436", "**********-16-000435", "**********-16-000434", "**********-16-000433", "**********-16-000432", "**********-16-000431", "**********-16-000430", "**********-16-000429", "**********-16-000428", "**********-16-000427", "**********-16-000426", "**********-16-000425", "**********-16-000424", "**********-16-000423", "**********-16-000422", "**********-16-000421", "**********-16-000420", "**********-16-000419", "**********-16-000418", "**********-16-000417", "**********-16-000416", "**********-16-000415", "**********-16-000414", "**********-16-000413", "**********-16-000412", "**********-16-000411", "**********-16-000410", "**********-16-000409", "**********-16-000408", "**********-16-000407", "**********-16-000406", "**********-16-000405", "**********-16-000404", "**********-16-000403", "**********-16-000402", "**********-16-000401", "**********-16-000400", "**********-16-000399", "**********-16-000398", "**********-16-000397", "**********-16-000396", "**********-16-000395", "**********-16-000394", "**********-16-000393", "**********-16-000392", "**********-16-000391", "**********-16-000390", "**********-16-000389", "**********-16-000388", "**********-16-000387", "**********-16-000386", "**********-16-000385", "**********-16-000384", "**********-16-000383", "**********-16-000382", "**********-16-000381", "**********-16-000380", "**********-16-000379", "**********-16-000378", "**********-16-000377", "**********-16-000376", "**********-16-000375", "**********-16-000374", "**********-16-000373", "**********-16-000372", "**********-16-000371", "**********-16-000370", "**********-16-000369", "**********-15-000368", "**********-15-000367", "**********-15-000366", "**********-15-000365", "**********-15-000364", "**********-15-000363", "**********-15-000362", "**********-15-000361", "**********-15-000360", "**********-15-000359", "**********-15-000358", "**********-15-000357", "**********-15-000356", "**********-15-000355", "**********-15-000354", "**********-15-000353", "**********-15-000352", "**********-15-000351", "**********-15-000350", "**********-15-000349", "**********-15-000348", "**********-15-000347", "**********-15-000346", "**********-15-000345", "**********-15-000344", "**********-15-000343", "**********-15-000342", "**********-15-000341", "**********-15-000340", "**********-15-000339", "**********-15-000338", "**********-15-000337", "**********-15-000336", "**********-15-000335", "**********-15-000334", "**********-15-000333", "**********-15-000332", "**********-15-000331", "**********-15-000330", "**********-15-000329", "**********-15-000328", "**********-15-000327", "**********-15-000326", "**********-15-000325", "**********-15-000324", "**********-15-000323", "**********-15-000322", "**********-15-000321", "**********-15-000320", "**********-15-000319", "**********-15-000318", "**********-15-000317", "**********-15-000316", "**********-15-000315", "**********-15-000314", "**********-15-000313", "**********-15-000312", "**********-15-000311", "**********-15-000310", "**********-15-000309", "**********-15-000308", "**********-15-000307", "**********-15-000306", "**********-15-000305", "**********-15-000304", "**********-15-000303", "**********-15-000302", "**********-15-000301", "**********-15-000300", "**********-15-000299", "**********-15-000298", "**********-15-000297", "**********-15-000296", "**********-15-000295", "**********-15-000294", "**********-15-000293", "**********-15-000292", "**********-15-000291", "**********-15-000290", "**********-15-000289", "**********-15-000288", "**********-15-000287", "**********-15-000286", "**********-15-000285", "**********-15-000284", "**********-15-000283", "**********-15-000282", "**********-15-000281", "**********-15-000280", "**********-15-000279", "**********-15-000278", "**********-15-000277", "**********-15-000276", "**********-15-000275", "**********-15-000274", "**********-15-000273", "**********-15-000272", "**********-15-000271", "**********-15-000270", "**********-15-000269", "**********-15-000268", "**********-15-000267", "**********-15-000266", "**********-15-000265", "**********-15-000264", "**********-15-000263", "**********-15-000262", "**********-15-000261", "**********-15-000260", "**********-15-000259", "**********-15-000258", "**********-15-000257", "**********-15-000256", "**********-15-000255", "**********-15-000254", "**********-15-000253", "**********-15-000252", "**********-15-000251", "**********-15-000250"], "filingDate": ["2025-05-19", "2025-04-18", "2025-03-19", "2025-02-19", "2025-01-15", "2024-12-26", "2024-11-20", "2024-10-15", "2024-09-18", "2024-08-16", "2024-07-19", "2024-06-18", "2024-05-17", "2024-04-17", "2024-03-20", "2024-02-16", "2024-01-17", "2023-12-19", "2023-11-16", "2023-10-25", "2023-09-19", "2023-08-22", "2023-07-21", "2023-06-20", "2023-05-22", "2023-04-21", "2023-03-20", "2023-02-23", "2023-01-23", "2022-12-16", "2022-11-16", "2022-10-31", "2022-09-21", "2022-08-22", "2022-07-19", "2022-06-27", "2022-05-27", "2022-04-25", "2022-03-22", "2022-02-23", "2022-01-25", "2021-12-21", "2021-11-22", "2021-10-27", "2021-09-17", "2021-08-26", "2021-07-23", "2021-06-24", "2021-05-26", "2021-04-21", "2021-03-24", "2021-02-23", "2021-01-19", "2020-12-18", "2020-11-20", "2020-10-21", "2020-09-18", "2020-08-24", "2020-07-17", "2020-06-22", "2020-05-20", "2020-04-20", "2020-03-20", "2020-02-25", "2020-02-04", "2019-12-23", "2019-12-11", "2019-10-28", "2019-09-23", "2019-08-22", "2019-07-16", "2019-06-19", "2019-05-16", "2019-04-16", "2019-03-15", "2019-02-26", "2019-01-16", "2018-12-18", "2018-11-19", "2018-10-16", "2018-09-18", "2018-08-21", "2018-07-18", "2018-06-19", "2018-05-18", "2018-04-19", "2018-03-16", "2018-02-20", "2018-01-11", "2018-01-04", "2018-01-04", "2018-01-04", "2018-01-04", "2018-01-04", "2018-01-04", "2018-01-04", "2018-01-04", "2018-01-04", "2018-01-04", "2018-01-04", "2018-01-04", "2018-01-04", "2018-01-04", "2018-01-04", "2018-01-04", "2018-01-02", "2017-12-22", "2017-12-21", "2017-12-20", "2017-12-20", "2017-12-20", "2017-12-20", "2017-12-20", "2017-12-20", "2017-12-20", "2017-12-20", "2017-12-20", "2017-12-20", "2017-12-20", "2017-12-20", "2017-12-19", "2017-12-19", "2017-12-19", "2017-12-19", "2017-12-19", "2017-12-11", "2017-12-06", "2017-12-06", "2017-12-06", "2017-12-06", "2017-12-06", "2017-12-06", "2017-12-05", "2017-11-22", "2017-11-22", "2017-11-22", "2017-11-22", "2017-11-22", "2017-11-22", "2017-11-22", "2017-11-22", "2017-11-22", "2017-11-22", "2017-11-22", "2017-11-22", "2017-11-22", "2017-11-22", "2017-11-22", "2017-11-22", "2017-11-20", "2017-11-20", "2017-11-20", "2017-11-17", "2017-11-17", "2017-11-17", "2017-11-09", "2017-11-09", "2017-11-07", "2017-11-07", "2017-11-07", "2017-11-07", "2017-11-07", "2017-11-03", "2017-11-03", "2017-10-25", "2017-10-25", "2017-10-25", "2017-10-20", "2017-10-20", "2017-10-20", "2017-10-20", "2017-10-16", "2017-10-16", "2017-10-16", "2017-10-16", "2017-10-16", "2017-10-16", "2017-10-16", "2017-10-16", "2017-10-16", "2017-10-16", "2017-10-11", "2017-10-11", "2017-10-05", "2017-10-04", "2017-10-03", "2017-10-03", "2017-10-03", "2017-10-03", "2017-10-03", "2017-10-03", "2017-10-03", "2017-10-03", "2017-10-03", "2017-10-02", "2017-09-25", "2017-09-21", "2017-09-19", "2017-09-19", "2017-09-19", "2017-09-19", "2017-09-19", "2017-09-19", "2017-09-19", "2017-09-18", "2017-09-18", "2017-09-13", "2017-09-08", "2017-09-08", "2017-09-08", "2017-09-07", "2017-08-30", "2017-08-30", "2017-08-30", "2017-08-30", "2017-08-30", "2017-08-30", "2017-08-30", "2017-08-30", "2017-08-30", "2017-08-30", "2017-08-30", "2017-08-28", "2017-08-24", "2017-08-21", "2017-08-21", "2017-08-14", "2017-08-14", "2017-08-14", "2017-08-11", "2017-08-11", "2017-08-11", "2017-08-10", "2017-08-10", "2017-08-10", "2017-08-10", "2017-07-27", "2017-07-21", "2017-07-18", "2017-07-17", "2017-07-14", "2017-07-13", "2017-07-13", "2017-07-07", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-06", "2017-07-05", "2017-06-28", "2017-06-28", "2017-06-28", "2017-06-27", "2017-06-23", "2017-06-23", "2017-06-23", "2017-06-23", "2017-06-22", "2017-06-22", "2017-06-22", "2017-06-22", "2017-06-22", "2017-06-19", "2017-06-19", "2017-06-19", "2017-06-19", "2017-06-19", "2017-06-19", "2017-06-19", "2017-06-19", "2017-06-16", "2017-06-16", "2017-06-16", "2017-06-15", "2017-06-14", "2017-06-14", "2017-06-14", "2017-06-14", "2017-06-14", "2017-06-13", "2017-06-12", "2017-06-12", "2017-05-22", "2017-05-22", "2017-05-22", "2017-05-22", "2017-05-22", "2017-05-22", "2017-05-22", "2017-05-22", "2017-05-22", "2017-05-22", "2017-05-22", "2017-05-22", "2017-05-22", "2017-05-22", "2017-05-22", "2017-05-22", "2017-05-22", "2017-05-22", "2017-05-22", "2017-05-18", "2017-05-16", "2017-05-16", "2017-05-12", "2017-05-11", "2017-05-11", "2017-05-09", "2017-05-09", "2017-05-09", "2017-05-01", "2017-05-01", "2017-04-25", "2017-04-25", "2017-04-25", "2017-04-25", "2017-04-25", "2017-04-25", "2017-04-25", "2017-04-25", "2017-04-25", "2017-04-21", "2017-04-21", "2017-04-21", "2017-04-19", "2017-04-19", "2017-04-19", "2017-04-17", "2017-04-17", "2017-04-17", "2017-04-17", "2017-04-17", "2017-04-17", "2017-04-14", "2017-04-13", "2017-04-13", "2017-04-12", "2017-04-12", "2017-04-11", "2017-04-11", "2017-04-11", "2017-04-11", "2017-04-11", "2017-04-10", "2017-04-10", "2017-04-07", "2017-04-07", "2017-04-07", "2017-04-07", "2017-04-07", "2017-04-07", "2017-04-07", "2017-04-06", "2017-04-06", "2017-04-06", "2017-04-06", "2017-04-06", "2017-04-06", "2017-04-06", "2017-04-06", "2017-04-06", "2017-04-06", "2017-04-04", "2017-04-04", "2017-04-04", "2017-04-04", "2017-03-31", "2017-03-31", "2017-03-30", "2017-03-30", "2017-03-29", "2017-03-28", "2017-03-28", "2017-03-28", "2017-03-28", "2017-03-28", "2017-03-28", "2017-03-27", "2017-03-24", "2017-03-23", "2017-03-23", "2017-03-23", "2017-03-21", "2017-03-21", "2017-03-21", "2017-03-21", "2017-03-21", "2017-03-20", "2017-03-16", "2017-03-14", "2017-03-13", "2017-03-13", "2017-03-13", "2017-03-08", "2017-03-08", "2017-02-24", "2017-02-24", "2017-02-24", "2017-02-23", "2017-02-21", "2017-02-21", "2017-02-21", "2017-02-17", "2017-02-14", "2017-02-13", "2017-02-13", "2017-02-06", "2017-02-06", "2017-02-06", "2017-02-02", "2017-02-02", "2017-02-02", "2017-02-02", "2017-02-02", "2017-01-27", "2017-01-27", "2017-01-27", "2017-01-27", "2017-01-27", "2017-01-26", "2017-01-24", "2017-01-24", "2017-01-23", "2017-01-23", "2017-01-23", "2017-01-23", "2017-01-23", "2017-01-23", "2017-01-23", "2017-01-23", "2017-01-23", "2017-01-23", "2017-01-19", "2017-01-17", "2017-01-17", "2017-01-13", "2017-01-13", "2017-01-13", "2017-01-13", "2017-01-12", "2017-01-10", "2017-01-10", "2017-01-09", "2017-01-09", "2017-01-09", "2017-01-09", "2017-01-09", "2017-01-09", "2017-01-06", "2017-01-06", "2017-01-06", "2017-01-06", "2017-01-06", "2017-01-06", "2017-01-06", "2017-01-05", "2017-01-05", "2017-01-03", "2017-01-03", "2017-01-03", "2017-01-03", "2017-01-03", "2016-12-21", "2016-12-21", "2016-12-21", "2016-12-21", "2016-12-21", "2016-12-21", "2016-12-12", "2016-12-12", "2016-12-12", "2016-12-12", "2016-12-12", "2016-12-12", "2016-12-12", "2016-12-08", "2016-12-08", "2016-12-08", "2016-12-08", "2016-12-08", "2016-12-08", "2016-12-08", "2016-12-08", "2016-12-07", "2016-12-07", "2016-12-07", "2016-12-07", "2016-12-07", "2016-12-07", "2016-12-07", "2016-12-07", "2016-12-07", "2016-12-07", "2016-12-07", "2016-12-07", "2016-12-07", "2016-12-07", "2016-12-07", "2016-12-07", "2016-12-07", "2016-12-07", "2016-12-06", "2016-12-06", "2016-12-06", "2016-12-06", "2016-12-06", "2016-12-06", "2016-12-06", "2016-12-06", "2016-12-06", "2016-12-06", "2016-12-06", "2016-12-06", "2016-12-06", "2016-12-06", "2016-12-05", "2016-12-05", "2016-12-05", "2016-12-05", "2016-11-23", "2016-11-22", "2016-11-22", "2016-11-22", "2016-11-22", "2016-11-22", "2016-11-22", "2016-11-21", "2016-11-21", "2016-11-21", "2016-11-21", "2016-11-21", "2016-11-16", "2016-11-16", "2016-11-16", "2016-11-16", "2016-11-15", "2016-11-15", "2016-11-15", "2016-11-15", "2016-11-15", "2016-11-15", "2016-11-15", "2016-11-15", "2016-11-09", "2016-11-09", "2016-11-07", "2016-11-04", "2016-11-03", "2016-11-03", "2016-10-25", "2016-10-25", "2016-10-24", "2016-10-19", "2016-10-18", "2016-10-18", "2016-10-18", "2016-10-18", "2016-10-18", "2016-10-18", "2016-10-18", "2016-10-18", "2016-10-13", "2016-10-13", "2016-10-13", "2016-10-13", "2016-10-13", "2016-10-11", "2016-10-06", "2016-10-06", "2016-10-06", "2016-10-06", "2016-10-04", "2016-10-04", "2016-09-26", "2016-09-26", "2016-09-26", "2016-09-26", "2016-09-26", "2016-09-26", "2016-09-22", "2016-09-22", "2016-09-19", "2016-09-13", "2016-09-13", "2016-09-12", "2016-09-09", "2016-09-09", "2016-09-08", "2016-09-06", "2016-09-06", "2016-09-02", "2016-09-02", "2016-08-30", "2016-08-24", "2016-08-17", "2016-08-17", "2016-08-12", "2016-08-11", "2016-08-09", "2016-08-08", "2016-08-08", "2016-08-08", "2016-08-05", "2016-08-05", "2016-08-04", "2016-08-04", "2016-08-04", "2016-08-04", "2016-08-04", "2016-08-03", "2016-08-01", "2016-08-01", "2016-07-26", "2016-07-21", "2016-07-21", "2016-07-21", "2016-07-21", "2016-07-21", "2016-07-18", "2016-07-14", "2016-07-14", "2016-07-14", "2016-07-12", "2016-07-12", "2016-07-12", "2016-07-12", "2016-07-12", "2016-07-06", "2016-07-05", "2016-07-05", "2016-07-05", "2016-06-24", "2016-06-24", "2016-06-24", "2016-06-24", "2016-06-24", "2016-06-24", "2016-06-24", "2016-06-24", "2016-06-24", "2016-06-24", "2016-06-24", "2016-06-24", "2016-06-24", "2016-06-24", "2016-06-23", "2016-06-23", "2016-06-23", "2016-06-21", "2016-06-21", "2016-06-21", "2016-06-21", "2016-06-21", "2016-06-21", "2016-06-14", "2016-06-14", "2016-06-14", "2016-06-10", "2016-06-07", "2016-06-03", "2016-06-03", "2016-06-03", "2016-06-03", "2016-05-26", "2016-05-26", "2016-05-24", "2016-05-24", "2016-05-24", "2016-05-24", "2016-05-19", "2016-05-19", "2016-05-19", "2016-05-19", "2016-05-19", "2016-05-19", "2016-05-19", "2016-05-16", "2016-05-10", "2016-05-10", "2016-05-10", "2016-05-10", "2016-05-10", "2016-05-10", "2016-04-29", "2016-04-29", "2016-04-29", "2016-04-29", "2016-04-28", "2016-04-28", "2016-04-28", "2016-04-28", "2016-04-28", "2016-04-26", "2016-04-26", "2016-04-26", "2016-04-22", "2016-04-22", "2016-04-20", "2016-04-20", "2016-04-20", "2016-04-19", "2016-04-15", "2016-04-15", "2016-04-15", "2016-04-15", "2016-04-15", "2016-04-15", "2016-04-13", "2016-04-13", "2016-04-13", "2016-04-13", "2016-04-12", "2016-04-08", "2016-04-08", "2016-04-08", "2016-04-08", "2016-04-08", "2016-04-07", "2016-04-07", "2016-04-07", "2016-04-07", "2016-04-07", "2016-04-07", "2016-04-05", "2016-04-05", "2016-03-31", "2016-03-31", "2016-03-31", "2016-03-31", "2016-03-31", "2016-03-31", "2016-03-29", "2016-03-29", "2016-03-29", "2016-03-29", "2016-03-28", "2016-03-28", "2016-03-23", "2016-03-23", "2016-03-23", "2016-03-23", "2016-03-23", "2016-03-23", "2016-03-23", "2016-03-22", "2016-03-21", "2016-03-18", "2016-03-18", "2016-03-18", "2016-03-18", "2016-03-17", "2016-03-17", "2016-03-17", "2016-03-17", "2016-03-17", "2016-03-16", "2016-03-16", "2016-03-16", "2016-03-16", "2016-03-16", "2016-03-16", "2016-03-16", "2016-03-16", "2016-03-16", "2016-03-16", "2016-03-16", "2016-03-16", "2016-03-16", "2016-03-11", "2016-03-11", "2016-03-11", "2016-03-11", "2016-03-11", "2016-03-11", "2016-03-11", "2016-03-11", "2016-03-11", "2016-03-10", "2016-03-10", "2016-03-10", "2016-03-10", "2016-03-10", "2016-03-10", "2016-03-08", "2016-03-08", "2016-03-02", "2016-03-02", "2016-03-02", "2016-03-02", "2016-03-02", "2016-02-23", "2016-02-19", "2016-02-19", "2016-02-19", "2016-02-19", "2016-02-17", "2016-02-12", "2016-02-12", "2016-02-05", "2016-02-05", "2016-02-05", "2016-02-05", "2016-02-05", "2016-01-27", "2016-01-27", "2016-01-27", "2016-01-27", "2016-01-22", "2016-01-22", "2016-01-22", "2016-01-22", "2016-01-22", "2016-01-22", "2016-01-22", "2016-01-21", "2016-01-21", "2016-01-21", "2016-01-21", "2016-01-21", "2016-01-21", "2016-01-21", "2016-01-21", "2016-01-21", "2016-01-21", "2016-01-21", "2016-01-21", "2016-01-19", "2016-01-14", "2016-01-13", "2016-01-12", "2016-01-12", "2016-01-12", "2016-01-12", "2016-01-12", "2016-01-11", "2016-01-11", "2016-01-11", "2016-01-11", "2016-01-11", "2016-01-08", "2016-01-08", "2016-01-08", "2016-01-08", "2016-01-08", "2016-01-08", "2016-01-08", "2016-01-08", "2016-01-08", "2016-01-08", "2016-01-08", "2016-01-08", "2016-01-08", "2016-01-08", "2016-01-08", "2016-01-07", "2016-01-07", "2016-01-07", "2016-01-07", "2016-01-07", "2016-01-06", "2016-01-06", "2015-12-21", "2015-12-21", "2015-12-16", "2015-12-16", "2015-12-16", "2015-12-15", "2015-12-15", "2015-12-15", "2015-12-15", "2015-12-15", "2015-12-15", "2015-12-14", "2015-12-14", "2015-12-14", "2015-12-14", "2015-12-14", "2015-12-14", "2015-12-14", "2015-12-14", "2015-12-14", "2015-12-11", "2015-12-11", "2015-12-11", "2015-12-11", "2015-12-11", "2015-12-11", "2015-12-11", "2015-12-09", "2015-12-09", "2015-12-08", "2015-12-08", "2015-12-08", "2015-12-08", "2015-12-08", "2015-12-08", "2015-12-08", "2015-12-08", "2015-12-08", "2015-12-08", "2015-12-08", "2015-12-08", "2015-12-08", "2015-12-08", "2015-12-08", "2015-12-07", "2015-12-04", "2015-12-04", "2015-12-04", "2015-12-04", "2015-12-04", "2015-12-04", "2015-12-03", "2015-12-03", "2015-12-03", "2015-12-03", "2015-12-03", "2015-12-03", "2015-12-01", "2015-11-30", "2015-11-30", "2015-11-30", "2015-11-25", "2015-11-25", "2015-11-25", "2015-11-25", "2015-11-25", "2015-11-25", "2015-11-25", "2015-11-24", "2015-11-24", "2015-11-24", "2015-11-24", "2015-11-24", "2015-11-24", "2015-11-24", "2015-11-24", "2015-11-24", "2015-11-18", "2015-11-18", "2015-11-13", "2015-11-12", "2015-11-12", "2015-11-12", "2015-11-12", "2015-11-12", "2015-11-10", "2015-11-10", "2015-10-14", "2015-10-14", "2015-10-14", "2015-10-14", "2015-10-14", "2015-10-14", "2015-10-14", "2015-10-14", "2015-10-14", "2015-10-14", "2015-10-14", "2015-10-14", "2015-10-14", "2015-10-14", "2015-10-14", "2015-10-14", "2015-10-05", "2015-09-16", "2015-09-16", "2015-09-16", "2015-09-16", "2015-09-16", "2015-09-16", "2015-09-16", "2015-09-03", "2015-09-03", "2015-09-03", "2015-08-26", "2015-08-26", "2015-08-26", "2015-08-26", "2015-08-26"], "reportDate": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "acceptanceDateTime": ["2025-05-19T12:34:33.000Z", "2025-04-18T13:29:55.000Z", "2025-03-19T14:25:02.000Z", "2025-02-19T21:07:14.000Z", "2025-01-15T21:31:30.000Z", "2024-12-26T14:33:53.000Z", "2024-11-20T21:27:55.000Z", "2024-10-15T18:33:02.000Z", "2024-09-18T19:42:29.000Z", "2024-08-16T19:41:44.000Z", "2024-07-19T13:00:18.000Z", "2024-06-18T14:51:54.000Z", "2024-05-17T20:42:03.000Z", "2024-04-17T19:27:26.000Z", "2024-03-20T14:43:59.000Z", "2024-02-16T19:40:51.000Z", "2024-01-17T21:45:06.000Z", "2023-12-19T16:00:50.000Z", "2023-11-16T17:50:20.000Z", "2023-10-25T13:51:26.000Z", "2023-09-19T18:25:22.000Z", "2023-08-22T13:23:57.000Z", "2023-07-21T13:40:51.000Z", "2023-06-20T13:03:16.000Z", "2023-05-22T17:45:29.000Z", "2023-04-21T15:55:28.000Z", "2023-03-20T16:22:07.000Z", "2023-02-23T13:08:17.000Z", "2023-01-23T13:38:20.000Z", "2022-12-16T17:56:23.000Z", "2022-11-16T13:04:16.000Z", "2022-10-31T14:07:49.000Z", "2022-09-21T11:39:38.000Z", "2022-08-22T12:38:40.000Z", "2022-07-19T11:40:17.000Z", "2022-06-27T15:17:39.000Z", "2022-05-27T19:12:12.000Z", "2022-04-25T14:23:20.000Z", "2022-03-22T16:49:58.000Z", "2022-02-23T18:05:28.000Z", "2022-01-25T12:37:00.000Z", "2021-12-21T16:27:28.000Z", "2021-11-22T18:17:21.000Z", "2021-10-27T17:05:51.000Z", "2021-09-17T13:57:48.000Z", "2021-08-26T19:48:35.000Z", "2021-07-23T12:53:28.000Z", "2021-06-24T14:24:28.000Z", "2021-05-26T14:34:39.000Z", "2021-04-21T14:19:00.000Z", "2021-03-24T17:54:37.000Z", "2021-02-23T14:37:35.000Z", "2021-01-19T15:54:00.000Z", "2020-12-18T16:21:41.000Z", "2020-11-20T18:09:27.000Z", "2020-10-21T18:46:30.000Z", "2020-09-18T14:03:17.000Z", "2020-08-24T13:11:09.000Z", "2020-07-17T15:02:09.000Z", "2020-06-22T17:33:26.000Z", "2020-05-20T18:08:05.000Z", "2020-04-20T17:57:01.000Z", "2020-03-20T15:15:48.000Z", "2020-02-25T21:10:20.000Z", "2020-02-04T16:36:49.000Z", "2019-12-23T16:39:55.000Z", "2019-12-11T15:35:46.000Z", "2019-10-28T17:39:21.000Z", "2019-09-23T17:51:38.000Z", "2019-08-22T17:06:46.000Z", "2019-07-16T15:18:09.000Z", "2019-06-19T14:20:47.000Z", "2019-05-16T13:48:23.000Z", "2019-04-16T13:22:04.000Z", "2019-03-15T17:46:43.000Z", "2019-02-26T11:59:01.000Z", "2019-01-16T14:51:38.000Z", "2018-12-18T18:40:38.000Z", "2018-11-19T15:01:52.000Z", "2018-10-16T14:17:25.000Z", "2018-09-18T16:55:01.000Z", "2018-08-21T12:12:21.000Z", "2018-07-18T11:31:03.000Z", "2018-06-19T11:42:03.000Z", "2018-05-18T14:28:29.000Z", "2018-04-19T13:16:30.000Z", "2018-03-16T12:40:23.000Z", "2018-02-20T13:37:37.000Z", "2018-01-11T20:24:51.000Z", "2018-01-04T15:50:03.000Z", "2018-01-04T15:47:06.000Z", "2018-01-04T15:41:36.000Z", "2018-01-04T15:38:42.000Z", "2018-01-04T15:33:57.000Z", "2018-01-04T15:28:47.000Z", "2018-01-04T15:23:52.000Z", "2018-01-04T15:03:09.000Z", "2018-01-04T14:57:52.000Z", "2018-01-04T14:53:33.000Z", "2018-01-04T14:49:21.000Z", "2018-01-04T14:44:03.000Z", "2018-01-04T14:35:11.000Z", "2018-01-04T14:30:34.000Z", "2018-01-04T14:24:02.000Z", "2018-01-04T14:19:13.000Z", "2018-01-02T13:58:36.000Z", "2017-12-22T19:10:03.000Z", "2017-12-21T18:14:33.000Z", "2017-12-20T19:33:27.000Z", "2017-12-20T19:28:33.000Z", "2017-12-20T19:22:28.000Z", "2017-12-20T19:17:18.000Z", "2017-12-20T19:12:52.000Z", "2017-12-20T19:05:58.000Z", "2017-12-20T18:58:51.000Z", "2017-12-20T18:52:12.000Z", "2017-12-20T18:47:00.000Z", "2017-12-20T18:41:05.000Z", "2017-12-20T18:36:15.000Z", "2017-12-20T18:31:44.000Z", "2017-12-19T18:46:21.000Z", "2017-12-19T18:41:10.000Z", "2017-12-19T18:35:34.000Z", "2017-12-19T18:30:08.000Z", "2017-12-19T18:24:16.000Z", "2017-12-11T19:14:34.000Z", "2017-12-06T14:24:32.000Z", "2017-12-06T14:08:32.000Z", "2017-12-06T14:05:39.000Z", "2017-12-06T14:02:53.000Z", "2017-12-06T13:00:42.000Z", "2017-12-06T12:57:40.000Z", "2017-12-05T13:17:15.000Z", "2017-11-22T15:11:37.000Z", "2017-11-22T15:08:33.000Z", "2017-11-22T15:01:59.000Z", "2017-11-22T14:59:11.000Z", "2017-11-22T14:40:01.000Z", "2017-11-22T14:36:28.000Z", "2017-11-22T14:24:46.000Z", "2017-11-22T14:21:20.000Z", "2017-11-22T14:17:02.000Z", "2017-11-22T13:25:20.000Z", "2017-11-22T13:20:07.000Z", "2017-11-22T13:15:43.000Z", "2017-11-22T13:11:06.000Z", "2017-11-22T13:04:57.000Z", "2017-11-22T12:56:52.000Z", "2017-11-22T12:25:25.000Z", "2017-11-20T14:15:44.000Z", "2017-11-20T12:59:11.000Z", "2017-11-20T12:54:21.000Z", "2017-11-17T13:41:04.000Z", "2017-11-17T13:36:06.000Z", "2017-11-17T13:29:51.000Z", "2017-11-09T14:31:03.000Z", "2017-11-09T14:23:55.000Z", "2017-11-07T14:58:03.000Z", "2017-11-07T14:53:44.000Z", "2017-11-07T14:47:21.000Z", "2017-11-07T14:43:33.000Z", "2017-11-07T14:38:51.000Z", "2017-11-03T13:43:07.000Z", "2017-11-03T13:39:44.000Z", "2017-10-25T11:43:36.000Z", "2017-10-25T11:37:30.000Z", "2017-10-25T11:28:10.000Z", "2017-10-20T19:01:54.000Z", "2017-10-20T18:50:05.000Z", "2017-10-20T18:48:39.000Z", "2017-10-20T16:55:10.000Z", "2017-10-16T19:00:48.000Z", "2017-10-16T18:54:17.000Z", "2017-10-16T18:46:53.000Z", "2017-10-16T18:38:19.000Z", "2017-10-16T18:32:49.000Z", "2017-10-16T18:25:24.000Z", "2017-10-16T18:00:14.000Z", "2017-10-16T17:54:57.000Z", "2017-10-16T17:44:56.000Z", "2017-10-16T17:21:27.000Z", "2017-10-11T13:44:31.000Z", "2017-10-11T13:40:03.000Z", "2017-10-05T14:13:03.000Z", "2017-10-04T14:13:57.000Z", "2017-10-03T19:03:02.000Z", "2017-10-03T19:00:48.000Z", "2017-10-03T18:56:27.000Z", "2017-10-03T18:52:03.000Z", "2017-10-03T18:47:44.000Z", "2017-10-03T18:43:36.000Z", "2017-10-03T18:39:37.000Z", "2017-10-03T14:39:31.000Z", "2017-10-03T14:34:19.000Z", "2017-10-02T12:23:42.000Z", "2017-09-25T17:51:26.000Z", "2017-09-21T11:57:52.000Z", "2017-09-19T15:07:50.000Z", "2017-09-19T15:02:35.000Z", "2017-09-19T14:58:47.000Z", "2017-09-19T14:53:25.000Z", "2017-09-19T14:48:59.000Z", "2017-09-19T14:45:19.000Z", "2017-09-19T14:40:32.000Z", "2017-09-18T13:55:09.000Z", "2017-09-18T11:51:02.000Z", "2017-09-13T14:19:53.000Z", "2017-09-08T12:04:15.000Z", "2017-09-08T11:59:36.000Z", "2017-09-08T11:43:38.000Z", "2017-09-07T13:05:26.000Z", "2017-08-30T18:01:09.000Z", "2017-08-30T17:54:58.000Z", "2017-08-30T17:48:45.000Z", "2017-08-30T17:44:12.000Z", "2017-08-30T17:38:50.000Z", "2017-08-30T13:26:47.000Z", "2017-08-30T13:22:13.000Z", "2017-08-30T13:06:22.000Z", "2017-08-30T13:01:48.000Z", "2017-08-30T12:55:21.000Z", "2017-08-30T12:47:47.000Z", "2017-08-28T16:28:43.000Z", "2017-08-24T13:55:28.000Z", "2017-08-21T12:39:28.000Z", "2017-08-21T12:35:03.000Z", "2017-08-14T14:48:09.000Z", "2017-08-14T14:41:52.000Z", "2017-08-14T14:35:40.000Z", "2017-08-11T13:02:28.000Z", "2017-08-11T12:56:24.000Z", "2017-08-11T12:34:47.000Z", "2017-08-10T16:40:23.000Z", "2017-08-10T16:35:42.000Z", "2017-08-10T15:02:56.000Z", "2017-08-10T14:57:39.000Z", "2017-07-27T12:39:05.000Z", "2017-07-21T11:08:58.000Z", "2017-07-18T11:58:43.000Z", "2017-07-17T12:54:50.000Z", "2017-07-14T13:06:27.000Z", "2017-07-13T13:31:35.000Z", "2017-07-13T13:26:57.000Z", "2017-07-07T11:49:26.000Z", "2017-07-06T17:40:53.000Z", "2017-07-06T17:36:09.000Z", "2017-07-06T14:32:56.000Z", "2017-07-06T14:26:48.000Z", "2017-07-06T14:22:08.000Z", "2017-07-06T14:16:42.000Z", "2017-07-06T14:11:41.000Z", "2017-07-06T14:03:57.000Z", "2017-07-06T13:59:27.000Z", "2017-07-06T13:55:00.000Z", "2017-07-06T13:48:21.000Z", "2017-07-06T13:44:19.000Z", "2017-07-06T13:40:10.000Z", "2017-07-06T13:36:01.000Z", "2017-07-06T13:32:05.000Z", "2017-07-06T13:28:21.000Z", "2017-07-06T12:17:40.000Z", "2017-07-06T12:14:01.000Z", "2017-07-06T12:09:23.000Z", "2017-07-06T12:05:25.000Z", "2017-07-06T12:00:17.000Z", "2017-07-06T11:54:46.000Z", "2017-07-06T11:49:57.000Z", "2017-07-06T11:46:10.000Z", "2017-07-06T11:42:19.000Z", "2017-07-06T11:38:26.000Z", "2017-07-06T11:33:16.000Z", "2017-07-06T11:27:15.000Z", "2017-07-06T11:21:50.000Z", "2017-07-05T18:09:09.000Z", "2017-06-28T12:48:10.000Z", "2017-06-28T12:11:02.000Z", "2017-06-28T12:03:54.000Z", "2017-06-27T13:40:21.000Z", "2017-06-23T16:35:21.000Z", "2017-06-23T14:01:00.000Z", "2017-06-23T12:07:10.000Z", "2017-06-23T11:34:41.000Z", "2017-06-22T18:57:16.000Z", "2017-06-22T15:02:06.000Z", "2017-06-22T14:55:20.000Z", "2017-06-22T14:50:04.000Z", "2017-06-22T14:06:23.000Z", "2017-06-19T14:12:52.000Z", "2017-06-19T14:09:44.000Z", "2017-06-19T13:57:00.000Z", "2017-06-19T13:49:38.000Z", "2017-06-19T13:12:34.000Z", "2017-06-19T13:07:57.000Z", "2017-06-19T13:03:41.000Z", "2017-06-19T12:58:07.000Z", "2017-06-16T13:35:56.000Z", "2017-06-16T13:31:28.000Z", "2017-06-16T13:02:29.000Z", "2017-06-15T17:57:09.000Z", "2017-06-14T12:25:24.000Z", "2017-06-14T12:20:25.000Z", "2017-06-14T12:15:51.000Z", "2017-06-14T12:11:21.000Z", "2017-06-14T12:07:06.000Z", "2017-06-13T11:29:55.000Z", "2017-06-12T12:28:52.000Z", "2017-06-12T12:24:13.000Z", "2017-05-22T19:01:04.000Z", "2017-05-22T18:56:11.000Z", "2017-05-22T18:52:05.000Z", "2017-05-22T18:47:42.000Z", "2017-05-22T18:40:42.000Z", "2017-05-22T16:29:56.000Z", "2017-05-22T12:59:14.000Z", "2017-05-22T12:54:51.000Z", "2017-05-22T12:44:46.000Z", "2017-05-22T12:39:20.000Z", "2017-05-22T12:34:36.000Z", "2017-05-22T12:28:57.000Z", "2017-05-22T12:22:17.000Z", "2017-05-22T12:18:39.000Z", "2017-05-22T12:13:23.000Z", "2017-05-22T12:08:37.000Z", "2017-05-22T12:04:26.000Z", "2017-05-22T11:59:34.000Z", "2017-05-22T11:55:30.000Z", "2017-05-18T13:08:25.000Z", "2017-05-16T18:02:14.000Z", "2017-05-16T17:54:06.000Z", "2017-05-12T11:22:49.000Z", "2017-05-11T12:28:48.000Z", "2017-05-11T12:11:13.000Z", "2017-05-09T17:32:17.000Z", "2017-05-09T12:53:36.000Z", "2017-05-09T11:39:29.000Z", "2017-05-01T13:23:54.000Z", "2017-05-01T13:20:27.000Z", "2017-04-25T14:53:39.000Z", "2017-04-25T14:48:51.000Z", "2017-04-25T14:43:16.000Z", "2017-04-25T14:39:07.000Z", "2017-04-25T14:28:46.000Z", "2017-04-25T14:22:09.000Z", "2017-04-25T14:18:01.000Z", "2017-04-25T14:11:34.000Z", "2017-04-25T14:07:15.000Z", "2017-04-21T12:48:14.000Z", "2017-04-21T12:43:04.000Z", "2017-04-21T12:38:25.000Z", "2017-04-19T14:26:33.000Z", "2017-04-19T12:53:07.000Z", "2017-04-19T12:48:42.000Z", "2017-04-17T18:18:16.000Z", "2017-04-17T18:13:22.000Z", "2017-04-17T12:22:15.000Z", "2017-04-17T12:12:49.000Z", "2017-04-17T12:08:59.000Z", "2017-04-17T12:03:04.000Z", "2017-04-14T13:12:37.000Z", "2017-04-13T16:28:57.000Z", "2017-04-13T13:18:28.000Z", "2017-04-12T13:22:00.000Z", "2017-04-12T13:15:37.000Z", "2017-04-11T18:13:40.000Z", "2017-04-11T17:11:01.000Z", "2017-04-11T17:06:12.000Z", "2017-04-11T13:47:05.000Z", "2017-04-11T13:22:11.000Z", "2017-04-10T12:55:27.000Z", "2017-04-10T12:50:18.000Z", "2017-04-07T18:41:44.000Z", "2017-04-07T18:37:14.000Z", "2017-04-07T18:32:12.000Z", "2017-04-07T18:26:20.000Z", "2017-04-07T18:21:22.000Z", "2017-04-07T18:12:14.000Z", "2017-04-07T14:40:44.000Z", "2017-04-06T18:01:20.000Z", "2017-04-06T17:57:01.000Z", "2017-04-06T17:45:50.000Z", "2017-04-06T17:39:37.000Z", "2017-04-06T17:35:11.000Z", "2017-04-06T16:47:50.000Z", "2017-04-06T16:42:53.000Z", "2017-04-06T13:41:32.000Z", "2017-04-06T13:30:47.000Z", "2017-04-06T13:10:10.000Z", "2017-04-04T12:38:22.000Z", "2017-04-04T12:32:41.000Z", "2017-04-04T12:27:17.000Z", "2017-04-04T12:22:26.000Z", "2017-03-31T13:20:33.000Z", "2017-03-31T12:25:09.000Z", "2017-03-30T12:10:55.000Z", "2017-03-30T12:06:42.000Z", "2017-03-29T16:05:25.000Z", "2017-03-28T13:48:48.000Z", "2017-03-28T13:40:35.000Z", "2017-03-28T13:35:34.000Z", "2017-03-28T12:28:26.000Z", "2017-03-28T11:27:45.000Z", "2017-03-28T11:17:05.000Z", "2017-03-27T13:17:08.000Z", "2017-03-24T17:22:01.000Z", "2017-03-23T11:29:44.000Z", "2017-03-23T11:24:41.000Z", "2017-03-23T11:19:09.000Z", "2017-03-21T12:37:33.000Z", "2017-03-21T12:30:12.000Z", "2017-03-21T12:25:08.000Z", "2017-03-21T12:18:13.000Z", "2017-03-21T12:09:02.000Z", "2017-03-20T16:35:09.000Z", "2017-03-16T11:31:48.000Z", "2017-03-14T18:42:03.000Z", "2017-03-13T12:44:42.000Z", "2017-03-13T12:40:13.000Z", "2017-03-13T12:32:05.000Z", "2017-03-08T13:38:41.000Z", "2017-03-08T13:28:55.000Z", "2017-02-24T18:10:25.000Z", "2017-02-24T14:19:19.000Z", "2017-02-24T14:14:24.000Z", "2017-02-23T18:20:57.000Z", "2017-02-21T12:53:21.000Z", "2017-02-21T12:48:25.000Z", "2017-02-21T12:44:33.000Z", "2017-02-17T12:15:59.000Z", "2017-02-14T17:55:29.000Z", "2017-02-13T12:26:14.000Z", "2017-02-13T12:23:02.000Z", "2017-02-06T13:00:49.000Z", "2017-02-06T12:53:43.000Z", "2017-02-06T12:48:05.000Z", "2017-02-02T18:52:27.000Z", "2017-02-02T18:47:32.000Z", "2017-02-02T18:41:10.000Z", "2017-02-02T18:34:34.000Z", "2017-02-02T18:26:25.000Z", "2017-01-27T12:31:44.000Z", "2017-01-27T12:20:00.000Z", "2017-01-27T12:14:37.000Z", "2017-01-27T12:10:40.000Z", "2017-01-27T12:06:22.000Z", "2017-01-26T19:19:26.000Z", "2017-01-24T12:13:34.000Z", "2017-01-24T12:09:59.000Z", "2017-01-23T15:39:35.000Z", "2017-01-23T15:26:26.000Z", "2017-01-23T15:10:20.000Z", "2017-01-23T15:05:05.000Z", "2017-01-23T15:00:34.000Z", "2017-01-23T14:56:02.000Z", "2017-01-23T12:45:06.000Z", "2017-01-23T12:41:00.000Z", "2017-01-23T12:34:47.000Z", "2017-01-23T12:25:30.000Z", "2017-01-19T13:30:19.000Z", "2017-01-17T13:22:39.000Z", "2017-01-17T13:02:55.000Z", "2017-01-13T15:11:48.000Z", "2017-01-13T14:30:00.000Z", "2017-01-13T14:20:57.000Z", "2017-01-13T14:13:52.000Z", "2017-01-12T14:36:58.000Z", "2017-01-10T19:39:51.000Z", "2017-01-10T12:51:05.000Z", "2017-01-09T12:56:18.000Z", "2017-01-09T12:52:00.000Z", "2017-01-09T12:46:20.000Z", "2017-01-09T12:24:51.000Z", "2017-01-09T12:20:43.000Z", "2017-01-09T12:16:21.000Z", "2017-01-06T12:47:00.000Z", "2017-01-06T12:40:57.000Z", "2017-01-06T12:36:19.000Z", "2017-01-06T12:32:08.000Z", "2017-01-06T12:24:41.000Z", "2017-01-06T12:20:41.000Z", "2017-01-06T12:12:25.000Z", "2017-01-05T19:40:20.000Z", "2017-01-05T18:07:56.000Z", "2017-01-03T15:01:26.000Z", "2017-01-03T14:50:49.000Z", "2017-01-03T14:38:10.000Z", "2017-01-03T14:32:33.000Z", "2017-01-03T14:22:50.000Z", "2016-12-21T14:24:16.000Z", "2016-12-21T14:18:57.000Z", "2016-12-21T14:13:44.000Z", "2016-12-21T14:08:51.000Z", "2016-12-21T13:52:29.000Z", "2016-12-21T13:34:09.000Z", "2016-12-12T16:09:33.000Z", "2016-12-12T15:56:36.000Z", "2016-12-12T14:29:02.000Z", "2016-12-12T14:16:32.000Z", "2016-12-12T13:57:33.000Z", "2016-12-12T13:53:44.000Z", "2016-12-12T12:12:37.000Z", "2016-12-08T16:01:42.000Z", "2016-12-08T14:13:07.000Z", "2016-12-08T14:08:04.000Z", "2016-12-08T12:29:50.000Z", "2016-12-08T12:23:33.000Z", "2016-12-08T12:11:41.000Z", "2016-12-08T12:04:41.000Z", "2016-12-08T12:01:44.000Z", "2016-12-07T19:00:42.000Z", "2016-12-07T18:56:09.000Z", "2016-12-07T18:52:20.000Z", "2016-12-07T18:47:51.000Z", "2016-12-07T18:41:35.000Z", "2016-12-07T18:32:11.000Z", "2016-12-07T18:25:40.000Z", "2016-12-07T18:21:09.000Z", "2016-12-07T18:15:17.000Z", "2016-12-07T18:09:49.000Z", "2016-12-07T18:01:55.000Z", "2016-12-07T17:56:56.000Z", "2016-12-07T17:52:25.000Z", "2016-12-07T17:48:35.000Z", "2016-12-07T17:40:27.000Z", "2016-12-07T17:36:31.000Z", "2016-12-07T17:32:59.000Z", "2016-12-07T17:26:26.000Z", "2016-12-06T20:04:31.000Z", "2016-12-06T19:56:16.000Z", "2016-12-06T14:40:58.000Z", "2016-12-06T14:30:09.000Z", "2016-12-06T14:26:06.000Z", "2016-12-06T14:20:56.000Z", "2016-12-06T14:14:30.000Z", "2016-12-06T12:48:29.000Z", "2016-12-06T12:42:20.000Z", "2016-12-06T12:37:49.000Z", "2016-12-06T12:32:24.000Z", "2016-12-06T12:26:42.000Z", "2016-12-06T12:19:53.000Z", "2016-12-06T12:04:28.000Z", "2016-12-05T18:20:34.000Z", "2016-12-05T18:17:10.000Z", "2016-12-05T12:51:32.000Z", "2016-12-05T12:46:29.000Z", "2016-11-23T12:44:18.000Z", "2016-11-22T18:28:36.000Z", "2016-11-22T12:54:35.000Z", "2016-11-22T12:49:25.000Z", "2016-11-22T12:40:58.000Z", "2016-11-22T12:34:37.000Z", "2016-11-22T12:30:05.000Z", "2016-11-21T17:45:49.000Z", "2016-11-21T17:28:44.000Z", "2016-11-21T14:32:15.000Z", "2016-11-21T12:34:26.000Z", "2016-11-21T12:16:59.000Z", "2016-11-16T13:29:02.000Z", "2016-11-16T13:25:11.000Z", "2016-11-16T13:20:12.000Z", "2016-11-16T13:03:57.000Z", "2016-11-15T18:32:35.000Z", "2016-11-15T18:27:58.000Z", "2016-11-15T18:11:36.000Z", "2016-11-15T13:45:53.000Z", "2016-11-15T13:41:18.000Z", "2016-11-15T12:31:41.000Z", "2016-11-15T12:22:33.000Z", "2016-11-15T12:17:13.000Z", "2016-11-09T15:58:22.000Z", "2016-11-09T15:39:57.000Z", "2016-11-07T13:44:49.000Z", "2016-11-04T12:56:18.000Z", "2016-11-03T17:24:25.000Z", "2016-11-03T17:18:45.000Z", "2016-10-25T17:01:04.000Z", "2016-10-25T16:58:28.000Z", "2016-10-24T16:40:21.000Z", "2016-10-19T11:35:19.000Z", "2016-10-18T13:36:37.000Z", "2016-10-18T13:30:54.000Z", "2016-10-18T13:21:45.000Z", "2016-10-18T13:16:27.000Z", "2016-10-18T13:12:17.000Z", "2016-10-18T13:07:29.000Z", "2016-10-18T13:03:15.000Z", "2016-10-18T12:55:18.000Z", "2016-10-13T14:48:07.000Z", "2016-10-13T14:41:04.000Z", "2016-10-13T13:53:54.000Z", "2016-10-13T11:43:16.000Z", "2016-10-13T11:24:38.000Z", "2016-10-11T17:31:58.000Z", "2016-10-06T13:14:46.000Z", "2016-10-06T13:10:45.000Z", "2016-10-06T13:05:47.000Z", "2016-10-06T13:01:50.000Z", "2016-10-04T13:02:50.000Z", "2016-10-04T12:54:04.000Z", "2016-09-26T14:21:47.000Z", "2016-09-26T12:21:15.000Z", "2016-09-26T11:43:08.000Z", "2016-09-26T11:38:43.000Z", "2016-09-26T11:23:07.000Z", "2016-09-26T11:01:13.000Z", "2016-09-22T11:58:17.000Z", "2016-09-22T11:53:49.000Z", "2016-09-19T13:08:05.000Z", "2016-09-13T12:11:23.000Z", "2016-09-13T12:05:47.000Z", "2016-09-12T12:54:35.000Z", "2016-09-09T14:44:28.000Z", "2016-09-09T14:39:32.000Z", "2016-09-08T18:36:56.000Z", "2016-09-06T13:10:16.000Z", "2016-09-06T13:06:18.000Z", "2016-09-02T14:40:10.000Z", "2016-09-02T12:37:19.000Z", "2016-08-30T17:02:57.000Z", "2016-08-24T13:32:14.000Z", "2016-08-17T14:35:20.000Z", "2016-08-17T14:06:34.000Z", "2016-08-12T14:58:16.000Z", "2016-08-11T11:59:00.000Z", "2016-08-09T13:01:33.000Z", "2016-08-08T13:11:24.000Z", "2016-08-08T13:01:11.000Z", "2016-08-08T12:52:30.000Z", "2016-08-05T11:15:52.000Z", "2016-08-05T11:11:16.000Z", "2016-08-04T13:44:28.000Z", "2016-08-04T13:39:57.000Z", "2016-08-04T13:30:46.000Z", "2016-08-04T13:20:09.000Z", "2016-08-04T13:01:47.000Z", "2016-08-03T18:59:12.000Z", "2016-08-01T11:50:08.000Z", "2016-08-01T11:44:33.000Z", "2016-07-26T17:22:27.000Z", "2016-07-21T14:17:55.000Z", "2016-07-21T14:13:11.000Z", "2016-07-21T13:06:22.000Z", "2016-07-21T11:58:50.000Z", "2016-07-21T11:55:02.000Z", "2016-07-18T12:51:29.000Z", "2016-07-14T18:32:34.000Z", "2016-07-14T17:08:07.000Z", "2016-07-14T17:03:24.000Z", "2016-07-12T14:14:37.000Z", "2016-07-12T13:06:10.000Z", "2016-07-12T12:57:00.000Z", "2016-07-12T12:50:53.000Z", "2016-07-12T11:14:33.000Z", "2016-07-06T11:08:42.000Z", "2016-07-05T17:12:26.000Z", "2016-07-05T17:07:52.000Z", "2016-07-05T11:19:38.000Z", "2016-06-24T15:41:42.000Z", "2016-06-24T15:33:56.000Z", "2016-06-24T14:18:16.000Z", "2016-06-24T14:14:20.000Z", "2016-06-24T14:10:35.000Z", "2016-06-24T14:06:39.000Z", "2016-06-24T14:03:13.000Z", "2016-06-24T13:59:09.000Z", "2016-06-24T13:55:16.000Z", "2016-06-24T13:14:49.000Z", "2016-06-24T12:22:50.000Z", "2016-06-24T12:17:21.000Z", "2016-06-24T12:02:10.000Z", "2016-06-24T11:50:33.000Z", "2016-06-23T12:23:29.000Z", "2016-06-23T12:16:18.000Z", "2016-06-23T12:11:33.000Z", "2016-06-21T14:46:50.000Z", "2016-06-21T14:21:44.000Z", "2016-06-21T12:54:35.000Z", "2016-06-21T12:29:51.000Z", "2016-06-21T12:23:56.000Z", "2016-06-21T11:59:43.000Z", "2016-06-14T12:05:20.000Z", "2016-06-14T11:57:18.000Z", "2016-06-14T11:48:06.000Z", "2016-06-10T14:01:25.000Z", "2016-06-07T11:55:16.000Z", "2016-06-03T18:31:42.000Z", "2016-06-03T18:27:12.000Z", "2016-06-03T18:20:51.000Z", "2016-06-03T13:09:16.000Z", "2016-05-26T11:13:59.000Z", "2016-05-26T11:09:39.000Z", "2016-05-24T18:37:39.000Z", "2016-05-24T13:33:08.000Z", "2016-05-24T13:27:11.000Z", "2016-05-24T13:22:12.000Z", "2016-05-19T17:15:11.000Z", "2016-05-19T12:39:40.000Z", "2016-05-19T12:34:25.000Z", "2016-05-19T12:29:55.000Z", "2016-05-19T12:25:36.000Z", "2016-05-19T12:17:20.000Z", "2016-05-19T12:13:21.000Z", "2016-05-16T11:47:01.000Z", "2016-05-10T16:51:40.000Z", "2016-05-10T16:47:30.000Z", "2016-05-10T14:42:31.000Z", "2016-05-10T14:24:35.000Z", "2016-05-10T14:19:58.000Z", "2016-05-10T14:13:10.000Z", "2016-04-29T17:41:56.000Z", "2016-04-29T17:32:52.000Z", "2016-04-29T17:25:40.000Z", "2016-04-29T11:41:29.000Z", "2016-04-28T18:49:34.000Z", "2016-04-28T18:43:45.000Z", "2016-04-28T18:38:55.000Z", "2016-04-28T18:33:16.000Z", "2016-04-28T18:29:14.000Z", "2016-04-26T17:46:29.000Z", "2016-04-26T17:36:40.000Z", "2016-04-26T11:28:27.000Z", "2016-04-22T14:03:17.000Z", "2016-04-22T13:48:17.000Z", "2016-04-20T12:47:19.000Z", "2016-04-20T11:56:40.000Z", "2016-04-20T11:51:17.000Z", "2016-04-19T13:51:26.000Z", "2016-04-15T11:48:46.000Z", "2016-04-15T11:43:46.000Z", "2016-04-15T11:39:26.000Z", "2016-04-15T11:34:52.000Z", "2016-04-15T11:30:28.000Z", "2016-04-15T11:26:07.000Z", "2016-04-13T17:54:50.000Z", "2016-04-13T17:49:57.000Z", "2016-04-13T13:12:02.000Z", "2016-04-13T13:02:57.000Z", "2016-04-12T13:43:25.000Z", "2016-04-08T12:36:07.000Z", "2016-04-08T11:44:41.000Z", "2016-04-08T11:38:23.000Z", "2016-04-08T11:32:43.000Z", "2016-04-08T11:20:13.000Z", "2016-04-07T18:41:20.000Z", "2016-04-07T18:36:53.000Z", "2016-04-07T18:04:31.000Z", "2016-04-07T17:50:15.000Z", "2016-04-07T17:45:46.000Z", "2016-04-07T14:54:17.000Z", "2016-04-05T15:00:24.000Z", "2016-04-05T14:39:28.000Z", "2016-03-31T18:35:42.000Z", "2016-03-31T18:31:32.000Z", "2016-03-31T17:49:07.000Z", "2016-03-31T17:43:16.000Z", "2016-03-31T17:35:53.000Z", "2016-03-31T17:30:06.000Z", "2016-03-29T11:57:28.000Z", "2016-03-29T11:43:57.000Z", "2016-03-29T11:24:13.000Z", "2016-03-29T11:16:01.000Z", "2016-03-28T18:08:23.000Z", "2016-03-28T17:59:32.000Z", "2016-03-23T13:10:24.000Z", "2016-03-23T13:06:08.000Z", "2016-03-23T12:13:39.000Z", "2016-03-23T12:08:30.000Z", "2016-03-23T12:03:27.000Z", "2016-03-23T11:58:53.000Z", "2016-03-23T11:30:49.000Z", "2016-03-22T16:21:26.000Z", "2016-03-21T11:33:07.000Z", "2016-03-18T13:58:38.000Z", "2016-03-18T12:00:33.000Z", "2016-03-18T11:55:22.000Z", "2016-03-18T11:48:51.000Z", "2016-03-17T11:49:00.000Z", "2016-03-17T11:39:41.000Z", "2016-03-17T11:14:25.000Z", "2016-03-17T11:09:30.000Z", "2016-03-17T10:58:15.000Z", "2016-03-16T17:34:12.000Z", "2016-03-16T13:03:24.000Z", "2016-03-16T12:56:33.000Z", "2016-03-16T12:52:15.000Z", "2016-03-16T12:47:01.000Z", "2016-03-16T12:42:47.000Z", "2016-03-16T12:37:41.000Z", "2016-03-16T12:32:25.000Z", "2016-03-16T12:27:37.000Z", "2016-03-16T12:18:33.000Z", "2016-03-16T11:16:20.000Z", "2016-03-16T11:10:52.000Z", "2016-03-16T11:05:42.000Z", "2016-03-11T12:56:05.000Z", "2016-03-11T12:49:56.000Z", "2016-03-11T12:45:45.000Z", "2016-03-11T12:41:15.000Z", "2016-03-11T12:36:11.000Z", "2016-03-11T12:31:38.000Z", "2016-03-11T12:26:44.000Z", "2016-03-11T12:21:29.000Z", "2016-03-11T12:15:50.000Z", "2016-03-10T14:09:57.000Z", "2016-03-10T14:05:27.000Z", "2016-03-10T13:58:17.000Z", "2016-03-10T13:53:46.000Z", "2016-03-10T13:45:13.000Z", "2016-03-10T13:31:16.000Z", "2016-03-08T13:36:40.000Z", "2016-03-08T13:19:57.000Z", "2016-03-02T14:13:41.000Z", "2016-03-02T14:08:51.000Z", "2016-03-02T14:04:04.000Z", "2016-03-02T13:59:42.000Z", "2016-03-02T13:54:58.000Z", "2016-02-23T12:04:17.000Z", "2016-02-19T12:46:46.000Z", "2016-02-19T12:35:12.000Z", "2016-02-19T12:25:21.000Z", "2016-02-19T12:19:23.000Z", "2016-02-17T12:17:25.000Z", "2016-02-12T14:19:51.000Z", "2016-02-12T13:15:29.000Z", "2016-02-05T12:55:26.000Z", "2016-02-05T12:42:16.000Z", "2016-02-05T12:37:15.000Z", "2016-02-05T12:32:36.000Z", "2016-02-05T12:22:47.000Z", "2016-01-27T12:52:51.000Z", "2016-01-27T12:44:02.000Z", "2016-01-27T12:32:35.000Z", "2016-01-27T12:23:12.000Z", "2016-01-22T20:26:21.000Z", "2016-01-22T20:21:03.000Z", "2016-01-22T20:14:12.000Z", "2016-01-22T12:33:30.000Z", "2016-01-22T12:28:35.000Z", "2016-01-22T12:24:42.000Z", "2016-01-22T12:18:34.000Z", "2016-01-21T13:02:42.000Z", "2016-01-21T12:58:23.000Z", "2016-01-21T12:53:45.000Z", "2016-01-21T12:48:52.000Z", "2016-01-21T12:43:27.000Z", "2016-01-21T12:36:46.000Z", "2016-01-21T12:32:07.000Z", "2016-01-21T12:26:12.000Z", "2016-01-21T12:21:30.000Z", "2016-01-21T12:15:13.000Z", "2016-01-21T12:09:55.000Z", "2016-01-21T12:05:25.000Z", "2016-01-19T14:06:08.000Z", "2016-01-14T12:10:22.000Z", "2016-01-13T12:20:17.000Z", "2016-01-12T13:03:40.000Z", "2016-01-12T12:39:52.000Z", "2016-01-12T12:14:30.000Z", "2016-01-12T12:09:58.000Z", "2016-01-12T12:05:20.000Z", "2016-01-11T20:09:08.000Z", "2016-01-11T12:36:55.000Z", "2016-01-11T12:26:25.000Z", "2016-01-11T12:21:56.000Z", "2016-01-11T12:16:28.000Z", "2016-01-08T15:45:33.000Z", "2016-01-08T15:18:15.000Z", "2016-01-08T15:12:20.000Z", "2016-01-08T14:34:38.000Z", "2016-01-08T14:21:42.000Z", "2016-01-08T14:14:27.000Z", "2016-01-08T14:05:24.000Z", "2016-01-08T13:36:40.000Z", "2016-01-08T13:13:47.000Z", "2016-01-08T13:09:26.000Z", "2016-01-08T12:52:24.000Z", "2016-01-08T12:47:22.000Z", "2016-01-08T12:42:20.000Z", "2016-01-08T12:38:10.000Z", "2016-01-08T12:32:54.000Z", "2016-01-07T13:44:27.000Z", "2016-01-07T13:36:20.000Z", "2016-01-07T13:27:58.000Z", "2016-01-07T12:51:52.000Z", "2016-01-07T12:34:03.000Z", "2016-01-06T14:08:03.000Z", "2016-01-06T13:57:34.000Z", "2015-12-21T18:07:58.000Z", "2015-12-21T17:33:22.000Z", "2015-12-16T14:00:59.000Z", "2015-12-16T12:18:41.000Z", "2015-12-16T12:14:05.000Z", "2015-12-15T19:24:14.000Z", "2015-12-15T19:13:52.000Z", "2015-12-15T12:55:00.000Z", "2015-12-15T12:38:40.000Z", "2015-12-15T12:33:37.000Z", "2015-12-15T12:28:54.000Z", "2015-12-14T13:48:33.000Z", "2015-12-14T13:37:38.000Z", "2015-12-14T13:32:11.000Z", "2015-12-14T13:28:19.000Z", "2015-12-14T12:51:47.000Z", "2015-12-14T12:43:46.000Z", "2015-12-14T12:37:46.000Z", "2015-12-14T12:30:59.000Z", "2015-12-14T12:09:22.000Z", "2015-12-11T19:03:42.000Z", "2015-12-11T18:56:52.000Z", "2015-12-11T18:51:17.000Z", "2015-12-11T18:46:40.000Z", "2015-12-11T18:40:52.000Z", "2015-12-11T18:30:01.000Z", "2015-12-11T18:25:53.000Z", "2015-12-09T14:08:32.000Z", "2015-12-09T14:02:56.000Z", "2015-12-08T19:06:26.000Z", "2015-12-08T19:00:35.000Z", "2015-12-08T18:51:37.000Z", "2015-12-08T18:47:32.000Z", "2015-12-08T18:36:16.000Z", "2015-12-08T18:12:55.000Z", "2015-12-08T18:07:20.000Z", "2015-12-08T18:01:26.000Z", "2015-12-08T17:53:32.000Z", "2015-12-08T13:44:08.000Z", "2015-12-08T13:39:17.000Z", "2015-12-08T13:12:52.000Z", "2015-12-08T13:06:46.000Z", "2015-12-08T13:02:55.000Z", "2015-12-08T12:11:02.000Z", "2015-12-07T18:52:47.000Z", "2015-12-04T20:30:56.000Z", "2015-12-04T20:24:42.000Z", "2015-12-04T20:20:17.000Z", "2015-12-04T20:15:41.000Z", "2015-12-04T20:11:49.000Z", "2015-12-04T20:05:19.000Z", "2015-12-03T19:42:39.000Z", "2015-12-03T19:37:33.000Z", "2015-12-03T19:21:06.000Z", "2015-12-03T18:25:27.000Z", "2015-12-03T18:20:46.000Z", "2015-12-03T17:48:41.000Z", "2015-12-01T13:04:06.000Z", "2015-11-30T13:43:27.000Z", "2015-11-30T13:12:29.000Z", "2015-11-30T12:55:31.000Z", "2015-11-25T15:43:49.000Z", "2015-11-25T15:21:16.000Z", "2015-11-25T15:05:11.000Z", "2015-11-25T15:00:50.000Z", "2015-11-25T13:55:19.000Z", "2015-11-25T12:28:34.000Z", "2015-11-25T12:04:22.000Z", "2015-11-24T20:12:10.000Z", "2015-11-24T19:23:43.000Z", "2015-11-24T16:07:27.000Z", "2015-11-24T16:01:17.000Z", "2015-11-24T15:45:27.000Z", "2015-11-24T15:35:50.000Z", "2015-11-24T15:00:28.000Z", "2015-11-24T14:26:44.000Z", "2015-11-24T13:56:46.000Z", "2015-11-18T20:03:29.000Z", "2015-11-18T19:57:26.000Z", "2015-11-13T20:53:12.000Z", "2015-11-12T14:34:53.000Z", "2015-11-12T14:28:39.000Z", "2015-11-12T14:22:48.000Z", "2015-11-12T14:17:57.000Z", "2015-11-12T14:11:02.000Z", "2015-11-10T14:43:17.000Z", "2015-11-10T14:36:53.000Z", "2015-10-14T17:20:13.000Z", "2015-10-14T15:14:01.000Z", "2015-10-14T15:09:39.000Z", "2015-10-14T14:56:44.000Z", "2015-10-14T14:36:47.000Z", "2015-10-14T14:30:11.000Z", "2015-10-14T14:24:12.000Z", "2015-10-14T14:15:06.000Z", "2015-10-14T14:06:25.000Z", "2015-10-14T14:01:46.000Z", "2015-10-14T13:56:30.000Z", "2015-10-14T13:51:42.000Z", "2015-10-14T13:43:21.000Z", "2015-10-14T13:10:36.000Z", "2015-10-14T13:05:40.000Z", "2015-10-14T12:58:47.000Z", "2015-10-05T12:40:17.000Z", "2015-09-16T11:52:39.000Z", "2015-09-16T11:46:58.000Z", "2015-09-16T11:42:20.000Z", "2015-09-16T11:37:12.000Z", "2015-09-16T11:31:44.000Z", "2015-09-16T11:25:12.000Z", "2015-09-16T11:19:01.000Z", "2015-09-03T12:06:16.000Z", "2015-09-03T11:58:28.000Z", "2015-09-03T11:53:48.000Z", "2015-08-26T12:45:41.000Z", "2015-08-26T12:40:35.000Z", "2015-08-26T12:35:23.000Z", "2015-08-26T12:05:20.000Z", "2015-08-26T12:00:33.000Z"], "act": ["33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33"], "form": ["D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D", "D/A", "D/A", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D", "D", "D", "D", "D/A", "D/A", "D/A", "D", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D", "D", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D", "D", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D", "D/A", "D", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A"], "fileNumber": ["021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-245023", "021-41398", "021-41398", "021-41398", "021-41398", "021-41398", "021-209020", "021-209026", "021-153104", "021-41398", "021-172670", "021-41398", "021-190116", "021-190177", "021-171952", "021-302322", "021-302320", "021-302066", "021-183317", "021-41398", "021-209021", "021-171944", "021-41398", "021-172669", "021-41398", "021-171949", "021-171949", "021-152188", "021-171417", "021-171421", "021-152185", "021-153104", "021-41398", "021-190521", "021-152183", "021-171949", "021-41398", "021-41398", "021-190174", "021-41398", "021-41398", "021-231360", "021-41398", "021-256034", "021-300044", "021-256033", "021-228547", "021-207858", "021-229357", "021-41398", "021-41398", "021-251898", "021-228905", "021-187897", "021-275043", "021-228932", "021-251904", "021-206735", "021-41398", "021-274846", "021-252131", "021-251896", "021-206746", "021-206748", "021-41398", "021-41398", "021-41398", "021-41398", "021-206743", "021-274461", "021-41398", "021-206030", "021-228216", "021-298196", "021-206741", "021-228207", "021-41398", "021-228434", "021-41398", "021-250940", "021-41398", "021-41398", "021-41398", "021-167971", "021-206735", "021-206739", "021-185671", "021-296765", "021-185684", "021-41398", "021-296758", "021-296755", "021-296751", "021-226941", "021-41398", "021-272809", "021-206735", "021-273172", "021-273170", "021-227374", "021-205418", "021-204783", "021-41398", "021-204323", "021-272177", "021-271244", "021-41398", "021-295317", "021-295116", "021-41398", "021-226561", "021-41398", "021-41398", "021-41398", "021-226191", "021-41398", "021-271248", "021-203373", "021-206768", "021-271093", "021-225901", "021-294282", "021-202321", "021-271094", "021-202945", "021-41398", "021-41398", "021-203628", "021-270868", "021-185573", "021-185575", "021-225381", "021-224593", "021-41398", "021-293563", "021-148235", "021-184497", "021-41398", "021-152186", "021-41398", "021-223395", "021-41398", "021-41398", "021-211403", "021-183322", "021-183317", "021-245373", "021-292528", "021-245372", "021-41398", "021-164124", "021-164124", "021-222727", "021-245187", "021-273072", "021-183319", "021-41398", "021-183326", "021-245282", "021-268042", "021-163931", "021-260909", "021-266979", "021-245024", "021-245021", "021-245023", "021-244390", "021-244191", "021-266390", "021-208829", "021-221028", "021-206735", "021-221466", "021-41398", "021-221450", "021-41398", "021-41398", "021-208821", "021-202321", "021-221173", "021-41398", "021-41398", "021-41398", "021-41398", "021-190174", "021-41398", "021-41398", "021-41398", "021-41398", "021-41398", "021-41398", "021-223390", "021-41398", "021-289323", "021-289279", "021-163931", "021-41398", "021-221172", "021-159208", "021-41398", "021-41398", "021-41398", "021-41398", "021-41398", "021-219654", "021-180791", "021-154777", "021-41398", "021-41398", "021-265436", "021-288842", "021-41398", "021-219556", "021-41398", "021-202321", "021-41398", "021-41398", "021-264761", "021-198916", "021-241138", "021-198172", "021-198917", "021-202321", "021-141729", "021-287205", "021-41398", "021-41398", "021-41398", "021-41398", "021-238356", "021-263756", "021-219555", "021-240434", "021-263514", "021-41398", "021-206735", "021-41398", "021-41398", "021-239890", "021-41398", "021-41398", "021-263205", "021-202709", "021-262849", "021-286310", "021-219919", "021-239873", "021-219917", "021-218360", "021-41398", "021-41398", "021-211404", "021-41398", "021-196625", "021-196633", "021-41398", "021-239616", "021-187897", "021-41398", "021-196617", "021-41398", "021-202195", "021-41398", "021-262315", "021-177708", "021-198913", "021-41398", "021-41398", "021-41398", "021-41398", "021-262020", "021-284724", "021-159208", "021-41398", "021-262319", "021-261854", "021-177496", "021-202321", "021-41398", "021-215986", "021-41398", "021-41398", "021-238105", "021-236889", "021-41398", "021-41398", "021-41398", "021-284335", "021-284300", "021-41398", "021-257781", "021-261853", "021-196628", "021-202140", "021-202138", "021-284200", "021-41398", "021-41398", "021-41398", "021-261190", "021-260909", "021-41398", "021-41398", "021-41398", "021-41398", "021-41398", "021-257782", "021-283530", "021-41398", "021-41398", "021-41398", "021-202321", "021-41398", "021-215027", "021-157570", "021-283289", "021-41398", "021-41398", "021-41398", "021-214160", "021-236494", "021-41398", "021-236499", "021-236140", "021-282914", "021-214536", "021-282513", "021-259378", "021-41398", "021-41398", "021-41398", "021-41398", "021-281284", "021-175626", "021-258969", "021-281198", "021-258764", "021-41398", "021-41398", "021-41398", "021-280650", "021-41398", "021-41398", "021-41398", "021-41398", "021-41398", "021-41398", "021-41398", "021-41398", "021-41398", "021-279901", "021-174521", "021-234070", "021-212379", "021-41398", "021-41398", "021-279358", "021-236890", "021-173695", "021-41398", "021-41398", "021-173692", "021-41398", "021-257377", "021-256809", "021-211690", "021-41398", "021-41398", "021-41398", "021-41398", "021-41398", "021-154777", "021-278354", "021-278352", "021-41398", "021-41398", "021-278235", "021-278063", "021-211768", "021-172668", "021-201756", "021-41398", "021-41398", "021-236143", "021-191650", "021-211760", "021-41398", "021-232145", "021-41398", "021-41398", "021-41398", "021-41398", "021-211688", "021-277699", "021-172982", "021-277467", "021-277466", "021-277465", "021-277464", "021-190877", "021-41398", "021-41398", "021-276717", "021-276715", "021-276714", "021-41398", "021-41398", "021-41398", "021-41398", "021-41398", "021-41398", "021-41398", "021-41398", "021-41398", "021-190526", "021-41398", "021-41398", "021-41398", "021-41398", "021-41398", "021-172665", "021-172666", "021-209026", "021-209020", "021-41398", "021-41398", "021-41398", "021-209021", "021-171944", "021-41398", "021-172669", "021-190177", "021-190116", "021-41398", "021-171949", "021-171949", "021-231360", "021-171421", "021-171417", "021-190521", "021-41398", "021-190174", "021-152188", "021-41398", "021-152183", "021-41398", "021-41398", "021-41398", "021-171949", "021-152185", "021-256034", "021-256033", "021-207858", "021-228547", "021-229357", "021-41398", "021-275043", "021-41398", "021-228905", "021-251898", "021-252131", "021-251904", "021-187897", "021-228932", "021-206735", "021-251896", "021-206746", "021-274846", "021-41398", "021-41398", "021-206748", "021-41398", "021-41398", "021-41398", "021-274496", "021-206743", "021-41398", "021-206030", "021-228216", "021-274461", "021-250940", "021-41398", "021-206741", "021-228207", "021-41398", "021-41398", "021-273172", "021-273170", "021-273072", "021-272809", "021-185671", "021-185684", "021-41398", "021-205418", "021-167971", "021-41398", "021-227374", "021-41398", "021-206735", "021-41398", "021-226941", "021-228434", "021-41398", "021-272177", "021-204783", "021-206735", "021-167974", "021-206739", "021-41398", "021-41398", "021-271248", "021-202321", "021-41398", "021-226561", "021-41398", "021-271244", "021-271094", "021-271093", "021-270868", "021-41398", "021-41398", "021-226191", "021-41398", "021-206768", "021-41398", "021-185575", "021-185573", "021-185575", "021-203373", "021-269778", "021-148235", "021-225381", "021-202945", "021-225901", "021-184497", "021-41398", "021-41398", "021-41398", "021-224593", "021-211403", "021-41398", "021-245282", "021-245373", "021-223395", "021-41398", "021-152186", "021-245187", "021-245372", "021-268042", "021-260909", "021-41398", "021-41398", "021-245024", "021-245021", "021-41398", "021-267190", "021-245023", "021-163931", "021-266979", "021-222727", "021-183322", "021-183317", "021-164124", "021-164124", "021-266390", "021-244390", "021-244191", "021-183319", "021-183326", "021-41398", "021-41398", "021-41398", "021-41398", "021-41398", "021-223390", "021-41398", "021-221028", "021-208829", "021-41398", "021-190174", "021-41398", "021-41398", "021-41398", "021-206735", "021-41398", "021-41398", "021-41398", "021-41398", "021-163931", "021-221466", "021-265436", "021-221450", "021-41398", "021-41398", "021-264761", "021-41398", "021-208821", "021-154777", "021-221172", "021-241138", "021-240434", "021-41398", "021-263756", "021-41398", "021-41398", "021-41398", "021-263514", "021-41398", "021-239873", "021-41398", "021-41398", "021-202321", "021-221173", "021-263205", "021-239890", "021-262849", "021-41398", "021-239616", "021-41398", "021-41398", "021-262321", "021-262319", "021-262315", "021-218360", "021-41398", "021-41398", "021-41398", "021-41398", "021-41398", "021-262046", "021-238356", "021-262020", "021-261854", "021-261853", "021-141729", "021-219654", "021-219917", "021-219556", "021-198172", "021-211404", "021-41398", "021-196633", "021-41398", "021-41398", "021-41398", "021-198916", "021-198917", "021-261190", "021-206735", "021-41398", "021-41398", "021-196617", "021-260909", "021-41398", "021-159208", "021-41398", "021-238105", "021-198841", "021-41398", "021-41398", "021-260650", "021-260646", "021-219555", "021-177708", "021-202321", "021-41398", "021-41398", "021-41398", "021-202195", "021-41398", "021-41398", "021-202138", "021-202140", "021-196628", "021-159207", "021-202709", "021-41398", "021-41398", "021-41398", "021-219919", "021-41398", "021-177496", "021-237005", "021-41398", "021-236890", "021-198913", "021-41398", "021-236889", "021-41398", "021-236782", "021-236499", "021-41398", "021-259378", "021-202321", "021-41398", "021-41398", "021-159208", "021-41398", "021-215986", "021-196625", "021-41398", "021-41398", "021-41398", "021-258969", "021-41398", "021-202321", "021-41398", "021-41398", "021-41398", "021-41398", "021-236140", "021-202321", "021-41398", "021-214160", "021-215027", "021-41398", "021-41398", "021-175626", "021-258969", "021-41398", "021-258764", "021-214536", "021-41398", "021-41398", "021-41398", "021-41398", "021-157570", "021-257933", "021-257782", "021-257781", "021-41398", "021-41398", "021-157568", "021-41398", "021-257377", "021-41398", "021-41398", "021-41398", "021-256809", "021-41398", "021-256034", "021-256033", "021-256032", "021-41398", "021-41398", "021-41398", "021-41398", "021-234070", "021-41398", "021-174521", "021-41398", "021-41398", "021-41398", "021-41398", "021-41398", "021-212379", "021-41398", "021-41398", "021-41398", "021-41398", "021-208823", "021-173695", "021-174521", "021-255394", "021-236494", "021-41398", "021-41398", "021-41398", "021-41398", "021-173692", "021-41398", "021-211690", "021-41398", "021-41398", "021-41398", "021-236143", "021-41398", "021-41398", "021-41398", "021-154777", "021-41398", "021-41398", "021-190877", "021-41398", "021-41398", "021-172668", "021-41398", "021-191650", "021-41398", "021-173690", "021-211768", "021-41398", "021-41398", "021-41398", "021-232145", "021-41398", "021-211760", "021-41398", "021-41398", "021-172456", "021-41398", "021-41398", "021-41398", "021-41398", "021-41398", "021-211688", "021-172982", "021-201756", "021-172455", "021-41398", "021-41398", "021-41398", "021-41398", "021-41398", "021-41398", "021-190526", "021-172665", "021-153104", "021-41398", "021-172666", "021-41398", "021-41398", "021-209026", "021-209020", "021-41398", "021-209021", "021-171944", "021-172670", "021-41398", "021-41398", "021-172669", "021-41398", "021-190116", "021-190177", "021-171949", "021-171949", "021-171952", "021-171421", "021-171417", "021-41398", "021-190521", "021-41398", "021-41398", "021-190174", "021-41398", "021-152188", "021-171949", "021-152183", "021-207858", "021-41398", "021-41398", "021-41398", "021-228547", "021-229357", "021-41398", "021-252131", "021-41398", "021-41398", "021-187897", "021-41398", "021-228932", "021-228905", "021-251904", "021-251898", "021-251896", "021-206743", "021-206735", "021-206746", "021-206748", "021-41398", "021-41398", "021-41398", "021-228434", "021-41398", "021-152185", "021-41398", "021-41398", "021-206030", "021-206741", "021-228207", "021-41398", "021-41398", "021-41398", "021-41398", "021-250940", "021-227374", "021-205418", "021-167974", "021-206735", "021-167971", "021-41398", "021-228216", "021-41398", "021-204783", "021-206735", "021-206739", "021-185684", "021-41398", "021-41398", "021-204323", "021-41398", "021-226561", "021-41398", "021-41398", "021-41398", "021-226191", "021-206768", "021-202321", "021-202709", "021-41398", "021-41398", "021-41398", "021-225381", "021-203628", "021-185575", "021-185573", "021-203373"], "filmNumber": ["25962713", "25849320", "25751149", "25639967", "25533283", "241577193", "241480751", "241371369", "241307317", "241216356", "241126187", "241050501", "24960566", "24850648", "24766724", "24647607", "24539019", "231495944", "231413660", "231344239", "231263319", "231191105", "231101262", "231023665", "23943690", "23835293", "23745457", "23656019", "23542610", "221467342", "221392982", "221344099", "221255014", "221182294", "221090338", "221042269", "22976428", "22847724", "22758251", "22662244", "22551111", "211507829", "211431228", "211351981", "211259404", "211212185", "211109149", "211041170", "21963469", "21839869", "21767782", "21663367", "21534523", "201398528", "201331751", "201250393", "201182906", "201124792", "201033064", "20978070", "20897439", "20802207", "20730829", "20650709", "20571251", "191304198", "191279181", "191172328", "191107393", "191045369", "19956503", "19905349", "19830730", "19750036", "19684426", "19631599", "19528433", "181240047", "181191421", "181123695", "181075173", "181029535", "18957665", "18906207", "18845392", "18762592", "18694282", "18623183", "18523578", "18508846", "18508839", "18508827", "18508815", "18508802", "18508782", "18508769", "18508705", "18508679", "18508665", "18508657", "18508644", "18508623", "18508614", "18508599", "18508589", "18500255", "171272279", "171268882", "171266500", "171266491", "171266483", "171266467", "171266452", "171266433", "171266420", "171266404", "171266398", "171266390", "171266348", "171266339", "171263559", "171263548", "171263535", "171263523", "171263507", "171249213", "171241275", "171241244", "171241241", "171241240", "171241170", "171241164", "171238688", "171218596", "171218588", "171218571", "171218558", "171218516", "171218511", "171218490", "171218481", "171218475", "171218408", "171218399", "171218395", "171218393", "171218390", "171218375", "171218353", "171212765", "171212638", "171212633", "171209763", "171209758", "171209746", "171188562", "171188535", "171182021", "171182012", "171181996", "171181989", "171181978", "171174871", "171174856", "171152078", "171152074", "171152065", "171146890", "171146864", "171146857", "171146562", "171138538", "171138496", "171138475", "171138452", "171138437", "171138407", "171138362", "171138352", "171138320", "171138258", "171131905", "171131903", "171123478", "171120609", "171117408", "171117400", "171117384", "171117355", "171117337", "171117313", "171117298", "171116436", "171116419", "171113116", "171099400", "171095095", "171091344", "171091336", "171091332", "171091324", "171091317", "171091308", "171091285", "171089181", "171089005", "171082437", "171075118", "171075115", "171075104", "171072982", "171060164", "171060142", "171060132", "171060119", "171060113", "171059419", "171059410", "171059376", "171059370", "171059356", "171059337", "171053871", "171048356", "171042184", "171042179", "171028139", "171028107", "171028081", "171023136", "171023110", "171023054", "171020623", "171020608", "171020255", "171020235", "17984611", "17975454", "17968514", "17966716", "17964628", "17962773", "17962763", "17953816", "17951685", "17951673", "17951128", "17951116", "17951104", "17951091", "17951086", "17951070", "17951064", "17951055", "17951032", "17951021", "17951010", "17951004", "17950997", "17950990", "17950868", "17950866", "17950862", "17950856", "17950849", "17950840", "17950834", "17950832", "17950829", "17950827", "17950823", "17950819", "17950816", "17947284", "17933954", "17933932", "17933929", "17931289", "17926983", "17926622", "17926367", "17926342", "17924910", "17924342", "17924328", "17924314", "17924218", "17917753", "17917746", "17917735", "17917725", "17917656", "17917647", "17917639", "17917623", "17914954", "17914947", "17914868", "17913102", "17910320", "17910317", "17910316", "17910314", "17910306", "17907930", "17904645", "17904637", "17860677", "17860655", "17860637", "17860615", "17860601", "17860305", "17859780", "17859766", "17859753", "17859747", "17859743", "17859734", "17859725", "17859718", "17859710", "17859707", "17859698", "17859686", "17859681", "17853569", "17848073", "17848047", "17836401", "17832612", "17832590", "17825644", "17824780", "17824450", "17798819", "17798811", "17780101", "17780094", "17780080", "17780067", "17780042", "17780028", "17780017", "17780007", "17779991", "17774282", "17774276", "17774271", "17768997", "17768812", "17768804", "17764233", "17764217", "17763604", "17763594", "17763589", "17763584", "17762227", "17760009", "17759521", "17757133", "17757123", "17755670", "17755545", "17755529", "17755059", "17754998", "17751810", "17751787", "17749160", "17749138", "17749106", "17749071", "17749029", "17748992", "17748198", "17745382", "17745367", "17745350", "17745327", "17745304", "17745119", "17745105", "17744578", "17744554", "17744531", "17736263", "17736254", "17736240", "17736233", "17728232", "17728052", "17723814", "17723784", "17721415", "17717618", "17717608", "17717597", "17716872", "17716813", "17716809", "17714282", "17712082", "17708140", "17708138", "17708136", "17703066", "17703057", "17703053", "17703045", "17703037", "17700846", "17692890", "17687849", "17684088", "17684083", "17684070", "17673956", "17673947", "17636024", "17634959", "17634947", "17631700", "17623500", "17623497", "17623493", "17619731", "17606086", "17595815", "17595809", "17574077", "17574070", "17574065", "17567761", "17567737", "17567707", "17567675", "17567655", "17551591", "17551585", "17551581", "17551578", "17551572", "17549177", "17542514", "17542509", "17540140", "17540077", "17540058", "17540046", "17540027", "17540011", "17539851", "17539848", "17539841", "17539825", "17534868", "17529256", "17529229", "17526719", "17526637", "17526625", "17526610", "17524065", "17520306", "17519192", "17515820", "17515814", "17515812", "17515792", "17515788", "17515785", "17512892", "17512889", "17512888", "17512886", "17512881", "17512878", "17512874", "17510157", "17509800", "17500613", "17500583", "17500564", "17500548", "17500527", "162062889", "162062873", "162062857", "162062848", "162062827", "162062812", "162045667", "162045646", "162045478", "162045458", "162045417", "162045408", "162045351", "162040670", "162040453", "162040449", "162040365", "162040363", "162040356", "162040351", "162040349", "162038560", "162038511", "162038503", "162038489", "162038456", "162038429", "162038404", "162038393", "162038373", "162038364", "162038346", "162038333", "162038319", "162038313", "162038280", "162038265", "162038257", "162038243", "162036147", "162036120", "162035354", "162035341", "162035332", "162035325", "162035303", "162035188", "162035184", "162035178", "162035168", "162035161", "162035159", "162035151", "162033288", "162033274", "162032677", "162032674", "162014870", "162012621", "162011726", "162011724", "162011721", "162011718", "162011713", "162009722", "162009676", "162009226", "162009063", "162009054", "162001369", "162001364", "162001361", "162001353", "161998966", "161998944", "161998888", "161997910", "161997903", "161997828", "161997820", "161997818", "161983115", "161983081", "161976671", "161973767", "161970953", "161970943", "161949890", "161949874", "161947724", "161941845", "161939985", "161939976", "161939960", "161939952", "161939943", "161939938", "161939933", "161939918", "161934351", "161934339", "161934234", "161934070", "161934060", "161930626", "161923983", "161923969", "161923954", "161923948", "161917277", "161917258", "161901415", "161901225", "161901199", "161901194", "161901181", "161901172", "161896692", "161896690", "161890868", "161882022", "161882011", "161880147", "161878083", "161878067", "161875811", "161869959", "161869952", "161867455", "161867153", "161860060", "161848348", "161837774", "161837671", "161826823", "161823032", "161816325", "161812940", "161812920", "161812895", "161809094", "161809087", "161806108", "161806095", "161806075", "161806050", "161805989", "161803506", "161795834", "161795822", "161783899", "161776699", "161776693", "161776600", "161776526", "161776523", "161770838", "161767279", "161767092", "161767083", "161763312", "161763229", "161763213", "161763205", "161763149", "161752475", "161749638", "161749630", "161748187", "161730277", "161730258", "161730105", "161730090", "161730083", "161730066", "161730062", "161730056", "161730049", "161729985", "161729935", "161729923", "161729903", "161729890", "161727625", "161727618", "161727610", "161723575", "161723535", "161723428", "161723400", "161723397", "161723364", "161712078", "161712064", "161712058", "161707598", "161699992", "161695181", "161695165", "161695133", "161694316", "161676208", "161676205", "161671551", "161670887", "161670876", "161670865", "161662603", "161662010", "161662002", "161661996", "161661994", "161661988", "161661984", "161650699", "161634739", "161634718", "161634148", "161634083", "161634073", "161634063", "161604965", "161604902", "161604831", "161602508", "161599264", "161599228", "161599206", "161599176", "161599151", "161591469", "161591440", "161590413", "161585541", "161585504", "161580432", "161580389", "161580387", "161578144", "161573175", "161573172", "161573167", "161573161", "161573157", "161573154", "161569003", "161568993", "161568472", "161568465", "161566453", "161561378", "161561320", "161561316", "161561313", "161561312", "161559755", "161559736", "161559654", "161559614", "161559603", "161559128", "161553342", "161553301", "161542910", "161542891", "161542697", "161542667", "161542642", "161542621", "161533649", "161533603", "161533593", "161533587", "161531839", "161531813", "161522696", "161522601", "161522523", "161522515", "161522507", "161522412", "161522398", "161520548", "161517544", "161514928", "161514511", "161514499", "161514492", "161511251", "161511249", "161511237", "161511229", "161511224", "161509219", "161508626", "161508614", "161508610", "161508607", "161508603", "161508601", "161508595", "161508582", "161508567", "161508539", "161508538", "161508537", "161499369", "161499359", "161499348", "161499340", "161499332", "161499324", "161499318", "161499314", "161499307", "161496329", "161496312", "161496282", "161496273", "161496256", "161496230", "161490368", "161490342", "161475694", "161475693", "161475682", "161475673", "161475669", "161446790", "161440154", "161440146", "161440123", "161440112", "161431590", "161414982", "161414324", "161390196", "161390187", "161390182", "161390180", "161390174", "161363017", "161363011", "161363001", "161362992", "161356110", "161356091", "161356066", "161354713", "161354709", "161354708", "161354704", "161352481", "161352479", "161352476", "161352473", "161352467", "161352466", "161352461", "161352452", "161352450", "161352446", "161352441", "161352436", "161347091", "161341849", "161339905", "161337656", "161337636", "161337616", "161337613", "161337608", "161336217", "161335064", "161335052", "161335049", "161335048", "161331929", "161331838", "161331817", "161331623", "161331600", "161331586", "161331560", "161331510", "161331502", "161331496", "161331477", "161331475", "161331470", "161331467", "161331464", "161328952", "161328934", "161328915", "161328859", "161328842", "161325432", "161325407", "151298985", "151298823", "151289964", "151289874", "151289870", "151288165", "151288132", "151287324", "151287316", "151287313", "151287305", "151284853", "151284838", "151284833", "151284820", "151284760", "151284752", "151284750", "151284746", "151284731", "151282861", "151282848", "151282837", "151282827", "151282809", "151282788", "151282782", "151277562", "151277549", "151274818", "151274801", "151274780", "151274771", "151274732", "151274685", "151274675", "151274666", "151274644", "151274108", "151274106", "151274076", "151274072", "151274067", "151274049", "151272258", "151269967", "151269949", "151269942", "151269931", "151269922", "151269900", "151267102", "151267080", "151267029", "151266874", "151266860", "151266799", "151261222", "151258587", "151258548", "151258531", "151254774", "151254697", "151254658", "151254652", "151254506", "151254428", "151254424", "151252472", "151252134", "151251506", "151251487", "151251436", "151251405", "151251324", "151251243", "151251193", "151240525", "151240506", "151229047", "151222025", "151222005", "151221980", "151221964", "151221927", "151217587", "151217573", "151157901", "151157619", "151157612", "151157583", "151157547", "151157539", "151157533", "151157508", "151157488", "151157482", "151157471", "151157461", "151157448", "151157327", "151157322", "151157307", "151142125", "151108969", "151108967", "151108963", "151108960", "151108958", "151108954", "151108952", "151090742", "151090732", "151090728", "151074686", "151074683", "151074681", "151074660", "151074651"], "items": ["06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7,4a5", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7,4a5", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7", "06b,3C,3C.7"], "core_type": ["D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D", "D/A", "D/A", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D", "D", "D", "D", "D/A", "D/A", "D/A", "D", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D", "D", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D", "D", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D", "D/A", "D", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A"], "size": [9924, 9924, 9931, 9931, 9931, 9932, 9932, 9932, 9932, 9932, 9932, 9932, 9931, 9931, 9931, 9931, 9931, 9932, 9899, 9899, 9899, 9899, 9899, 9899, 9898, 9898, 9898, 9898, 9898, 9993, 9993, 9993, 9993, 9993, 9993, 9993, 9992, 9992, 9992, 9992, 9992, 9993, 9993, 9993, 9993, 9993, 9993, 9993, 9992, 9992, 9992, 9992, 9992, 9993, 9995, 9993, 9993, 9993, 9899, 9898, 9898, 9898, 9898, 9899, 9894, 9707, 9707, 9707, 9707, 9707, 9990, 9990, 9990, 9990, 9990, 9990, 9990, 9991, 9991, 9991, 9991, 9991, 9990, 9990, 9990, 9990, 9990, 9990, 9916, 11423, 11427, 11407, 11406, 11406, 12412, 13285, 13344, 14246, 11425, 12389, 12376, 12379, 11424, 11302, 11310, 12265, 12369, 15195, 12424, 11429, 12372, 11438, 18277, 12381, 12408, 13367, 11442, 11410, 12382, 13351, 15266, 14359, 14421, 11429, 14435, 12335, 13363, 14337, 14333, 13269, 14359, 12370, 12294, 12360, 12397, 11394, 13307, 13389, 13322, 14320, 14334, 13362, 12396, 11420, 14338, 11412, 13332, 14323, 10205, 11428, 11412, 11411, 11402, 12398, 12418, 16289, 11455, 11415, 13330, 12356, 16249, 12283, 13474, 12370, 12398, 12360, 13285, 12397, 15385, 11426, 11421, 13342, 12386, 12385, 13418, 12264, 13382, 11374, 13213, 12306, 12298, 11424, 11420, 12386, 11419, 11416, 11416, 11428, 12347, 11401, 12295, 11301, 13275, 16250, 11414, 13242, 12286, 13333, 12391, 15423, 12397, 11404, 13316, 11416, 12331, 12313, 14210, 11414, 12371, 11308, 12385, 11413, 12332, 11411, 15292, 12357, 11416, 11386, 11448, 12407, 12365, 11406, 13205, 11403, 12364, 12304, 12392, 12400, 16259, 13407, 14344, 13418, 15202, 15259, 13385, 12287, 13270, 11427, 12389, 12390, 11428, 12385, 12380, 15182, 11421, 15275, 12373, 11426, 15239, 12355, 12339, 13361, 12393, 14286, 11418, 13293, 14268, 13314, 13342, 11415, 11421, 12393, 13332, 11407, 11403, 11415, 11413, 10623, 13277, 13287, 15374, 16144, 13370, 13353, 13324, 13354, 13339, 13345, 11411, 11413, 11423, 16301, 13249, 14270, 12399, 11428, 13307, 14340, 11427, 11419, 11413, 14318, 13256, 12381, 12359, 14223, 14222, 12406, 12256, 15329, 13356, 11424, 12372, 11407, 11434, 11416, 12395, 12394, 14119, 12392, 12382, 15159, 13197, 11405, 11425, 12405, 11427, 12343, 12355, 11424, 13398, 13406, 11405, 11421, 12389, 12389, 12363, 13322, 13322, 12362, 14340, 11406, 11323, 13360, 13369, 11413, 12408, 13374, 13383, 13330, 14349, 11390, 13330, 13327, 13331, 13361, 12381, 14258, 15272, 14331, 12397, 11417, 12318, 11414, 15127, 14392, 14384, 11427, 11420, 11323, 13314, 11419, 13339, 13343, 13313, 11411, 13370, 13467, 15369, 11428, 12375, 12386, 13322, 13282, 14366, 11321, 12262, 13389, 13226, 11413, 11390, 11406, 11391, 12241, 17190, 13318, 13338, 12397, 12396, 11431, 12396, 14235, 12337, 12398, 14276, 12295, 15272, 14283, 13319, 11417, 12418, 11402, 13237, 12310, 11418, 12388, 13363, 13386, 14368, 12383, 14367, 14364, 13296, 11405, 12263, 12354, 14323, 14291, 13307, 13368, 14216, 12373, 14282, 14213, 14310, 11430, 13399, 11426, 16154, 11436, 13278, 16218, 13370, 14376, 12375, 12369, 12404, 13370, 14192, 11418, 12377, 12386, 12338, 13352, 14254, 11406, 11407, 15246, 14337, 14203, 14353, 12398, 12360, 14353, 15306, 13280, 12358, 11411, 11429, 12357, 12282, 12273, 14360, 13318, 15181, 11317, 12371, 12379, 14305, 11416, 11429, 11421, 11417, 11412, 13286, 12366, 11403, 14320, 11424, 11421, 11446, 12291, 11449, 16271, 12300, 12299, 13274, 14322, 16256, 15087, 14148, 11321, 11328, 13352, 11406, 11408, 11412, 11421, 11407, 13383, 11422, 11442, 14273, 11424, 14247, 14243, 11425, 14433, 13312, 13311, 13285, 12404, 15195, 15266, 11441, 12424, 11429, 12372, 11438, 12380, 12377, 17303, 12407, 12381, 13268, 11410, 11414, 14359, 12339, 13363, 13367, 14435, 14421, 14333, 14337, 14359, 11429, 12381, 12370, 12359, 11394, 12397, 13307, 13389, 12299, 16300, 14399, 14314, 10205, 14338, 13320, 11420, 11412, 11427, 11412, 14222, 13367, 13332, 11411, 12418, 12398, 11402, 13243, 11455, 12348, 12356, 16248, 11320, 11420, 15385, 13474, 12370, 11421, 11426, 11321, 11321, 12282, 12292, 13399, 13382, 12387, 12347, 13341, 11395, 11428, 11379, 12386, 11423, 11423, 12359, 13285, 13185, 11401, 11419, 11413, 12385, 13333, 12293, 12236, 12385, 11414, 12391, 11404, 16154, 11314, 11315, 11267, 15423, 12397, 13323, 15297, 14210, 11416, 11448, 11386, 11448, 12313, 12275, 11403, 12407, 12326, 12371, 12364, 11410, 11406, 12302, 12364, 13418, 13407, 12373, 13385, 16259, 11418, 11418, 12372, 13206, 11267, 12291, 11363, 14278, 13297, 12328, 11294, 11255, 13270, 15175, 12177, 11363, 15115, 15194, 12325, 12326, 14109, 11354, 13227, 15117, 15211, 12281, 13280, 13295, 13286, 13294, 11348, 13264, 13278, 13250, 11356, 13311, 12335, 11348, 11359, 11351, 16080, 11339, 11355, 11363, 14205, 11357, 12246, 13268, 12329, 11343, 11253, 14276, 11354, 12296, 11365, 12331, 13334, 14254, 12196, 13223, 11339, 15266, 13247, 15328, 13305, 11349, 11301, 11350, 10559, 12203, 12299, 11247, 13317, 13265, 13310, 13213, 13165, 13183, 11258, 12344, 14205, 14205, 14203, 11370, 11367, 13171, 12275, 11261, 13168, 11193, 12310, 13192, 11348, 13292, 14055, 13266, 13263, 13266, 11341, 11341, 11361, 12331, 12328, 12234, 11357, 12351, 13306, 14194, 12237, 11360, 13243, 12311, 12309, 12282, 12325, 12325, 13165, 12224, 11360, 12241, 12308, 12306, 12304, 14284, 14257, 12340, 14307, 11328, 11341, 11326, 12280, 14250, 11353, 14265, 12311, 13278, 13218, 13249, 12289, 14234, 11341, 11333, 11355, 12316, 13233, 12309, 13325, 14121, 12186, 12321, 13325, 14320, 13250, 14328, 13403, 11324, 15305, 13301, 11362, 14276, 12273, 11351, 12329, 11364, 13257, 12334, 14297, 11357, 12303, 12335, 11339, 12319, 12356, 12310, 14183, 14302, 14150, 11342, 14225, 11355, 13305, 11374, 13129, 11259, 14117, 12164, 12325, 13288, 14102, 13316, 12240, 13335, 11366, 11372, 12201, 12348, 12197, 12198, 11265, 13270, 12294, 12299, 12301, 12295, 11360, 11344, 11302, 14292, 16154, 14312, 12274, 12322, 11347, 11368, 11380, 11362, 11362, 11343, 11347, 14164, 14302, 13223, 15021, 13243, 14273, 14139, 15242, 14289, 13226, 15198, 11339, 11356, 15199, 16190, 12294, 12294, 11368, 14256, 14256, 14296, 13223, 12315, 11352, 11353, 11365, 11342, 11355, 11360, 11376, 11366, 12301, 11345, 11348, 11357, 13288, 12345, 13310, 11347, 11376, 11360, 11381, 11381, 11385, 13270, 12351, 11345, 11341, 11344, 11356, 14178, 16245, 14208, 13246, 13280, 14182, 13246, 14368, 15130, 13212, 12337, 12282, 11377, 11365, 11364, 11353, 13294, 11346, 12307, 12313, 12315, 12317, 12316, 11360, 11334, 11332, 11353, 14294, 12274, 11359, 13299, 14371, 13303, 11365, 14357, 11329, 14268, 14270, 14294, 12332, 13242, 13359, 7161, 12313, 13302, 13255, 11566, 11342, 14338, 14176, 10086, 11256, 11390, 11348, 11348, 11347, 13268, 12334, 11338, 12295, 11361, 12316, 12283, 11362, 12292, 13409, 12305, 13228, 11359, 11349, 15320, 11260, 11363, 12282, 11348, 12320, 13276, 12353, 16184, 11358, 11336, 11353, 12320, 13301, 11330, 12324, 11300, 11338, 12326, 11347, 11339, 15358, 13257, 14144, 12323, 14220, 12331, 11345, 14271, 12341, 12286, 11383, 11321, 12247], "isXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "isInlineXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "primaryDocument": ["xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml"], "primaryDocDescription": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]}, "files": [{"name": "CIK**********-submissions-001.json", "filingCount": 1851, "filingFrom": "2002-03-08", "filingTo": "2015-08-24"}]}}