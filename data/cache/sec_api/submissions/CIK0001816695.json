{"cik": "**********", "entityType": "other", "sic": "", "sicDescription": "", "ownerOrg": "", "insiderTransactionForOwnerExists": 0, "insiderTransactionForIssuerExists": 0, "name": "Nationwide PPVUL Separate Account - 5", "tickers": [], "exchanges": [], "ein": "*********", "lei": null, "description": "", "website": "", "investorWebsite": "", "category": "", "fiscalYearEnd": "1231", "stateOfIncorporation": "OH", "stateOfIncorporationDescription": "OH", "addresses": {"mailing": {"street1": "ONE NATIONWIDE PLAZA", "street2": null, "city": "COLUMBUS", "stateOrCountry": "OH", "zipCode": "43215", "stateOrCountryDescription": "OH", "isForeignLocation": 0, "foreignStateTerritory": null, "country": null, "countryCode": null}, "business": {"street1": "ONE NATIONWIDE PLAZA", "street2": null, "city": "COLUMBUS", "stateOrCountry": "OH", "zipCode": "43215", "stateOrCountryDescription": "OH", "isForeignLocation": null, "foreignStateTerritory": null, "country": null, "countryCode": null}}, "phone": "************", "flags": "", "formerNames": [{"name": "Nationwide Private Placement Variable Account PP5", "from": "2020-07-06T00:00:00.000Z", "to": "2021-02-23T00:00:00.000Z"}], "filings": {"recent": {"accessionNumber": ["**********-25-000006", "**********-25-000005", "**********-25-000003", "**********-25-000002", "**********-25-000001", "**********-24-000012", "**********-24-000011", "**********-24-000010", "**********-24-000009", "**********-24-000008", "**********-24-000007", "**********-24-000006", "**********-24-000005", "**********-24-000004", "**********-24-000003", "**********-24-000002", "**********-24-000001", "**********-23-000003", "**********-23-000002", "**********-23-000001", "**********-23-000062", "**********-23-000054", "**********-23-000003", "**********-23-000042", "**********-23-000034", "**********-23-000002", "**********-23-000022", "**********-23-000014", "**********-23-000006", "**********-22-000008", "**********-22-000009", "**********-22-000031", "**********-22-000024", "**********-22-000006", "**********-22-000012", "**********-22-000007", "**********-22-000005", "**********-22-000004", "**********-22-000003", "**********-22-000002", "**********-22-000001", "**********-21-000013", "**********-21-000012", "**********-21-000011", "**********-21-000010", "**********-21-000009", "**********-21-000008", "**********-21-000007", "**********-21-000006", "**********-21-000005", "**********-21-000004", "**********-21-000002", "**********-21-000001", "**********-20-000007", "**********-20-000006", "**********-20-000005", "**********-20-000004", "**********-20-000003", "**********-20-000002", "**********-20-000001"], "filingDate": ["2025-05-19", "2025-04-18", "2025-03-19", "2025-02-19", "2025-01-15", "2024-12-26", "2024-11-20", "2024-10-15", "2024-09-18", "2024-08-16", "2024-07-19", "2024-06-18", "2024-05-17", "2024-04-17", "2024-03-20", "2024-02-16", "2024-01-17", "2023-12-19", "2023-11-16", "2023-10-25", "2023-09-19", "2023-08-22", "2023-07-21", "2023-06-20", "2023-05-22", "2023-04-21", "2023-03-20", "2023-02-23", "2023-01-23", "2022-12-16", "2022-11-16", "2022-10-31", "2022-09-21", "2022-08-22", "2022-07-19", "2022-06-27", "2022-05-27", "2022-04-25", "2022-03-22", "2022-02-23", "2022-01-25", "2021-12-21", "2021-11-22", "2021-10-27", "2021-09-17", "2021-08-26", "2021-07-23", "2021-06-24", "2021-05-26", "2021-04-21", "2021-03-25", "2021-02-23", "2021-01-19", "2020-12-18", "2020-11-20", "2020-10-21", "2020-09-18", "2020-08-24", "2020-07-17", "2020-07-06"], "reportDate": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "acceptanceDateTime": ["2025-05-19T12:45:01.000Z", "2025-04-18T17:22:51.000Z", "2025-03-19T14:33:38.000Z", "2025-02-19T21:19:28.000Z", "2025-01-15T21:44:52.000Z", "2024-12-26T14:52:40.000Z", "2024-11-20T21:36:05.000Z", "2024-10-15T18:41:13.000Z", "2024-09-18T19:51:49.000Z", "2024-08-16T20:20:38.000Z", "2024-07-19T13:09:45.000Z", "2024-06-18T15:11:04.000Z", "2024-05-17T20:52:11.000Z", "2024-04-17T19:39:29.000Z", "2024-03-20T15:12:13.000Z", "2024-02-16T19:50:22.000Z", "2024-01-17T21:55:51.000Z", "2023-12-19T16:17:19.000Z", "2023-11-16T18:08:06.000Z", "2023-10-25T19:34:42.000Z", "2023-09-19T18:54:02.000Z", "2023-08-22T13:57:15.000Z", "2023-07-21T14:37:49.000Z", "2023-06-20T13:39:50.000Z", "2023-05-22T18:18:10.000Z", "2023-04-21T17:56:48.000Z", "2023-03-20T17:11:25.000Z", "2023-02-23T13:40:59.000Z", "2023-01-23T14:06:20.000Z", "2022-12-16T18:25:54.000Z", "2022-11-16T13:39:18.000Z", "2022-10-31T15:11:34.000Z", "2022-09-21T12:12:07.000Z", "2022-08-22T15:27:35.000Z", "2022-07-19T12:31:56.000Z", "2022-06-27T17:08:22.000Z", "2022-05-27T19:55:17.000Z", "2022-04-25T15:05:47.000Z", "2022-03-22T17:18:13.000Z", "2022-02-23T18:44:51.000Z", "2022-01-25T14:30:29.000Z", "2021-12-21T17:00:18.000Z", "2021-11-22T18:43:56.000Z", "2021-10-27T17:33:40.000Z", "2021-09-17T14:38:01.000Z", "2021-08-26T20:42:21.000Z", "2021-07-23T13:25:10.000Z", "2021-06-24T15:45:26.000Z", "2021-05-26T15:21:50.000Z", "2021-04-21T17:33:46.000Z", "2021-03-25T20:01:11.000Z", "2021-02-23T15:54:36.000Z", "2021-01-19T16:23:31.000Z", "2020-12-18T16:59:58.000Z", "2020-11-20T20:23:45.000Z", "2020-10-21T19:43:06.000Z", "2020-09-18T15:10:06.000Z", "2020-08-24T14:01:03.000Z", "2020-07-17T15:52:56.000Z", "2020-07-06T13:09:30.000Z"], "act": ["33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33"], "form": ["D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D"], "fileNumber": ["021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222", "021-370222"], "filmNumber": ["25962770", "25849930", "25751167", "25640322", "25533426", "241577734", "241480894", "241371405", "241307355", "241216981", "241126306", "241050554", "24960761", "24850715", "24766849", "24647661", "24539112", "231496034", "231413713", "231345478", "231263402", "231191277", "231101425", "231023749", "23943934", "23835795", "23745610", "23656097", "23542671", "221467445", "221393064", "221344474", "221255043", "221182726", "221090449", "221043344", "22976861", "22847930", "22758351", "22662442", "22551289", "211507966", "211431391", "211352151", "211259463", "211212959", "211109255", "211041538", "21963863", "21840767", "21772267", "21663612", "21534603", "201398655", "201332151", "201250547", "201183040", "201125035", "201033180", "201012302"], "items": ["06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b", "06b"], "core_type": ["D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D"], "size": [7554, 7554, 7561, 7561, 7561, 7562, 7562, 7562, 7561, 7561, 7561, 7561, 7560, 7560, 7560, 7560, 7560, 7561, 7528, 7528, 7435, 7435, 7435, 7435, 7433, 7433, 7433, 7433, 7433, 7434, 7434, 7434, 7529, 7529, 7529, 7529, 7434, 7433, 7433, 7433, 7433, 7434, 7434, 7434, 7434, 7434, 7434, 7434, 7343, 7343, 7342, 7292, 7292, 7293, 7293, 7293, 7293, 7293, 7006, 6911], "isXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "isInlineXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "primaryDocument": ["xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml"], "primaryDocDescription": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]}, "files": []}}