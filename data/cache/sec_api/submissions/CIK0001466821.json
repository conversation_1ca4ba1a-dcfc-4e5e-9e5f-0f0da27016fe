{"cik": "0001466821", "entityType": "other", "sic": "", "sicDescription": "", "ownerOrg": "", "insiderTransactionForOwnerExists": 1, "insiderTransactionForIssuerExists": 0, "name": "SM Investors, L.P.", "tickers": [], "exchanges": [], "ein": "000000000", "lei": null, "description": "", "website": "", "investorWebsite": "", "category": "", "fiscalYearEnd": "1231", "stateOfIncorporation": "DE", "stateOfIncorporationDescription": "DE", "addresses": {"mailing": {"street1": "509 MADISON AVENUE", "street2": "SUITE 406", "city": "NEW YORK", "stateOrCountry": "NY", "zipCode": "10022", "stateOrCountryDescription": "NY", "isForeignLocation": 0, "foreignStateTerritory": null, "country": null, "countryCode": null}, "business": {"street1": "509 MADISON AVENUE", "street2": "SUITE 406", "city": "NEW YORK", "stateOrCountry": "NY", "zipCode": "10022", "stateOrCountryDescription": "NY", "isForeignLocation": null, "foreignStateTerritory": null, "country": null, "countryCode": null}}, "phone": "************", "flags": "", "formerNames": [], "filings": {"recent": {"accessionNumber": ["0001636587-25-000022", "0001636587-24-000012", "0000919574-23-003527", "0000919574-22-003673", "0000919574-21-003889", "0000919574-20-003963", "0000919574-19-004019", "0000919574-18-004099", "0000919574-17-004782", "0000919574-16-013622", "0000919574-15-004837", "0000919574-14-003679", "0000919574-13-004134", "0000919574-13-004080", "0000919574-13-003855", "0000919574-13-003783", "0000919574-12-005427", "0000919574-12-004041", "0000919574-11-003883", "0000919574-10-003942", "0000919574-09-012361"], "filingDate": ["2025-05-19", "2024-05-14", "2023-05-30", "2022-06-01", "2021-06-02", "2020-06-05", "2019-06-07", "2018-06-07", "2017-06-09", "2016-06-10", "2015-06-10", "2014-06-10", "2013-07-17", "2013-07-10", "2013-06-19", "2013-06-10", "2012-09-14", "2012-06-20", "2011-06-20", "2010-06-21", "2009-06-22"], "reportDate": ["", "", "", "", "", "", "", "", "", "", "", "", "2013-07-15", "2013-07-08", "", "2013-06-05", "2012-09-12", "", "", "", ""], "acceptanceDateTime": ["2025-05-19T10:22:08.000Z", "2024-05-14T14:49:39.000Z", "2023-05-30T15:08:47.000Z", "2022-06-01T09:49:29.000Z", "2021-06-02T11:34:47.000Z", "2020-06-05T10:38:53.000Z", "2019-06-07T12:36:23.000Z", "2018-06-07T17:04:26.000Z", "2017-06-09T11:43:53.000Z", "2016-06-10T15:09:47.000Z", "2015-06-10T16:45:03.000Z", "2014-06-10T14:24:11.000Z", "2013-07-17T17:30:52.000Z", "2013-07-10T18:34:49.000Z", "2013-06-19T11:25:57.000Z", "2013-06-10T17:12:43.000Z", "2012-09-14T17:36:19.000Z", "2012-06-20T14:19:34.000Z", "2011-06-20T15:50:29.000Z", "2010-06-21T12:01:13.000Z", "2009-06-22T10:03:06.000Z"], "act": ["33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "34", "34", "33", "34", "34", "33", "33", "33", "33"], "form": ["D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "4", "4", "D/A", "4", "3", "D/A", "D/A", "D/A", "D"], "fileNumber": ["021-132141", "021-132141", "021-132141", "021-132141", "021-132141", "021-132141", "021-132141", "021-132141", "021-132141", "021-132141", "021-132141", "021-132141", "000-21422", "000-21422", "021-132141", "000-21422", "000-21422", "021-132141", "021-132141", "021-132141", "021-132141"], "filmNumber": ["25962074", "24943582", "23975523", "22986204", "21988231", "20944952", "19884875", "18887210", "17901973", "161708454", "15923865", "14901696", "13973325", "13962695", "13921153", "13904188", "121093551", "12917070", "11921029", "10907453", "09902549"], "items": ["06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "", "", "06,3C,3C.1", "", "", "06,3C,3C.1", "06,3C,3C.1", "06,3C,3C.1", "06,3C,3C.1"], "core_type": ["D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "4", "4", "D/A", "4", "3", "D/A", "D/A", "D/A", "D"], "size": [6660, 7833, 7670, 7670, 7670, 7670, 7670, 7670, 7666, 7593, 7635, 7674, 19258, 19263, 7591, 18648, 3793, 7576, 7576, 8073, 7492], "isXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "isInlineXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "primaryDocument": ["xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslF345X03/p1399195.xml", "xslF345X03/p1397228.xml", "xslFormDX01/primary_doc.xml", "xslF345X03/p1389187.xml", "xslF345X02/p1319902.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml"], "primaryDocDescription": ["", "", "", "", "", "", "", "", "", "", "", "", "MUOIO / OPTI", "OWNERSHIP DOCUMENT", "", "MUOIO / OPTI", "OWNERSHIP DOCUMENT", "", "", "", ""]}, "files": []}}