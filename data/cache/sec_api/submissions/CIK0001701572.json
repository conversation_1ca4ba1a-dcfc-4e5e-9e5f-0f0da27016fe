{"cik": "0001701572", "entityType": "other", "sic": "", "sicDescription": "", "ownerOrg": "", "insiderTransactionForOwnerExists": 0, "insiderTransactionForIssuerExists": 0, "name": "GREEN MATTERS TECHNOLOGIES INC.", "tickers": [], "exchanges": [], "ein": "000000000", "lei": null, "description": "", "website": "", "investorWebsite": "", "category": "", "fiscalYearEnd": "1231", "stateOfIncorporation": "A1", "stateOfIncorporationDescription": "British Columbia, Canada", "addresses": {"mailing": {"street1": "105 - 26868 56TH AVE", "street2": null, "city": "LANGLEY", "stateOrCountry": "A1", "zipCode": "V4W 1N9", "stateOrCountryDescription": "British Columbia, Canada", "isForeignLocation": 0, "foreignStateTerritory": null, "country": null, "countryCode": null}, "business": {"street1": "105 - 26868 56TH AVE", "street2": null, "city": "LANGLEY", "stateOrCountry": "A1", "zipCode": "V4W 1N9", "stateOrCountryDescription": "British Columbia, Canada", "isForeignLocation": null, "foreignStateTerritory": null, "country": null, "countryCode": null}}, "phone": "************", "flags": "", "formerNames": [{"name": "GREEN MATTERS INC.", "from": "2017-03-23T00:00:00.000Z", "to": "2018-03-07T00:00:00.000Z"}], "filings": {"recent": {"accessionNumber": ["0001701572-25-000002", "0001701572-25-000001", "0001701572-24-000001", "0001085037-24-000011", "0001085037-24-000005", "0001085037-23-000123", "0001085037-23-000122", "0001085037-23-000119", "0001085037-23-000077", "0001085037-23-000052", "0001085037-23-000032", "0001085037-22-000092", "0001085037-22-000036", "0001085037-22-000003", "0001085037-21-000110", "0001085037-21-000053", "0001085037-21-000018", "0001085037-21-000008", "0001085037-20-000071", "0001085037-19-000136", "0001085037-19-000115", "0001085037-19-000074", "0001085037-19-000070", "0001085037-19-000064", "0001085037-19-000063", "0001085037-19-000060", "0001085037-19-000015", "0001085037-18-000123", "0001085037-18-000102", "0001085037-18-000091", "0001085037-18-000073", "0001085037-18-000064", "0001085037-18-000039", "0001085037-18-000028", "0001085037-18-000026", "0001085037-18-000019", "0001085037-17-000103", "0001085037-17-000081", "0001085037-17-000073", "0001085037-17-000027", "0001085037-17-000025", "0001085037-17-000023"], "filingDate": ["2025-06-20", "2025-01-28", "2024-11-04", "2024-05-24", "2024-01-29", "2023-11-27", "2023-11-27", "2023-11-07", "2023-07-03", "2023-04-11", "2023-03-15", "2022-12-20", "2022-06-15", "2022-02-02", "2021-12-27", "2021-07-28", "2021-04-01", "2021-02-12", "2020-10-02", "2019-12-31", "2019-11-08", "2019-08-30", "2019-08-20", "2019-07-30", "2019-07-26", "2019-07-12", "2019-02-25", "2018-11-13", "2018-10-01", "2018-09-04", "2018-07-30", "2018-07-02", "2018-04-09", "2018-03-07", "2018-02-28", "2018-02-09", "2017-09-19", "2017-07-24", "2017-07-06", "2017-03-27", "2017-03-23", "2017-03-23"], "reportDate": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "acceptanceDateTime": ["2025-06-20T16:55:52.000Z", "2025-01-27T17:49:20.000Z", "2024-11-04T15:22:36.000Z", "2024-05-24T12:20:37.000Z", "2024-01-29T14:31:18.000Z", "2023-11-27T14:15:55.000Z", "2023-11-27T14:05:47.000Z", "2023-11-07T15:11:48.000Z", "2023-06-30T18:01:44.000Z", "2023-04-11T14:36:53.000Z", "2023-03-15T13:13:54.000Z", "2022-12-19T20:27:38.000Z", "2022-06-14T19:45:53.000Z", "2022-02-02T13:08:19.000Z", "2021-12-23T18:38:25.000Z", "2021-07-27T17:42:51.000Z", "2021-03-31T19:02:07.000Z", "2021-02-11T21:10:46.000Z", "2020-10-02T13:43:49.000Z", "2019-12-31T16:47:55.000Z", "2019-11-08T15:38:16.000Z", "2019-08-29T21:08:27.000Z", "2019-08-20T13:15:50.000Z", "2019-07-30T17:21:36.000Z", "2019-07-26T15:20:12.000Z", "2019-07-12T15:06:37.000Z", "2019-02-22T19:29:27.000Z", "2018-11-09T18:39:12.000Z", "2018-09-28T19:18:48.000Z", "2018-09-04T12:15:32.000Z", "2018-07-27T18:19:27.000Z", "2018-06-29T17:56:43.000Z", "2018-04-09T13:59:19.000Z", "2018-03-07T17:01:10.000Z", "2018-02-27T20:13:32.000Z", "2018-02-09T13:20:56.000Z", "2017-09-18T19:12:08.000Z", "2017-07-21T19:18:56.000Z", "2017-07-06T13:55:29.000Z", "2017-03-24T17:38:38.000Z", "2017-03-23T13:30:35.000Z", "2017-03-23T13:25:53.000Z"], "act": ["33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33"], "form": ["D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D/A", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D"], "fileNumber": ["021-549499", "021-536389", "021-528592", "021-514242", "021-503788", "021-498110", "021-498107", "021-496709", "021-485771", "021-478494", "021-475933", "021-467976", "021-449224", "021-431893", "021-426681", "021-407957", "021-394317", "021-389276", "021-376998", "021-356852", "021-353003", "021-347727", "021-346978", "021-345173", "021-345173", "021-344186", "021-333431", "021-325540", "021-322458", "021-320559", "021-317986", "021-315862", "021-309645", "021-307172", "021-306594", "021-305290", "021-294942", "021-291329", "021-290108", "021-283323", "021-283190", "021-283189"], "filmNumber": ["251062115", "25560332", "241423080", "24982569", "24572785", "231439620", "231439559", "231383215", "231063037", "23813160", "23734373", "221472652", "221016322", "22582560", "211518501", "211119019", "21795285", "21622166", "201219234", "191319886", "191203895", "191067715", "191038627", "19986461", "19977804", "19952940", "19627775", "181174384", "181095515", "181051730", "18975773", "18930344", "18745238", "18674028", "18647698", "18590297", "171090765", "17977373", "17951724", "17713709", "17709273", "17709264"], "items": ["06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b"], "core_type": ["D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D/A", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D"], "size": [12490, 11481, 9095, 9297, 9278, 9059, 9262, 9057, 9057, 6768, 6772, 6597, 6600, 6601, 6600, 6604, 6600, 5796, 5835, 6587, 6587, 6587, 6533, 6681, 6586, 6586, 6586, 6586, 6585, 6582, 6581, 6581, 6591, 6510, 6510, 6510, 7358, 7288, 7289, 5871, 5866, 5866], "isXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "isInlineXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "primaryDocument": ["xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml"], "primaryDocDescription": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]}, "files": []}}