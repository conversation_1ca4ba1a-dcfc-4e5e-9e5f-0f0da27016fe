{"cik": "0001878313", "entityType": "operating", "sic": "2834", "sicDescription": "Pharmaceutical Preparations", "ownerOrg": "03 Life Sciences", "insiderTransactionForOwnerExists": 0, "insiderTransactionForIssuerExists": 1, "name": "MAIA Biotechnology, Inc.", "tickers": ["MAIA"], "exchanges": ["NYSE"], "ein": "*********", "lei": null, "description": "", "website": "", "investorWebsite": "", "category": "<br>Emerging growth company", "fiscalYearEnd": "1231", "stateOfIncorporation": "DE", "stateOfIncorporationDescription": "DE", "addresses": {"mailing": {"street1": "444 WEST LAKE STREET, SUITE 1700", "street2": null, "city": "CHICAGO", "stateOrCountry": "IL", "zipCode": "60606", "stateOrCountryDescription": "IL", "isForeignLocation": 0, "foreignStateTerritory": null, "country": null, "countryCode": null}, "business": {"street1": "444 WEST LAKE STREET, SUITE 1700", "street2": null, "city": "CHICAGO", "stateOrCountry": "IL", "zipCode": "60606", "stateOrCountryDescription": "IL", "isForeignLocation": null, "foreignStateTerritory": null, "country": null, "countryCode": null}}, "phone": "************", "flags": "", "formerNames": [], "filings": {"recent": {"accessionNumber": ["0001641172-25-014710", "0001127602-25-016869", "0001641172-25-013803", "0001641172-25-013176", "0001641172-25-012617", "0001641172-25-012192", "0001641172-25-012079", "0001127602-25-014670", "0001127602-25-014669", "0001127602-25-014667", "0001127602-25-013965", "0001127602-25-013963", "0000950170-25-068005", "0001641172-25-008750", "0001641172-25-005692", "0001127602-25-011819", "0001127602-25-011817", "0001127602-25-011815", "0001127602-25-011813", "0001127602-25-011811", "0001127602-25-011809", "0001140361-25-012692", "0001140361-25-012689", "0001641172-25-002001", "0001641172-25-001222", "0001140361-25-010968", "0001641172-25-001045", "0001641172-25-000788", "0001641172-25-000776", "0001641172-25-000154", "0000950170-25-043190", "0001493152-25-010925", "0001127602-25-008330", "0001127602-25-008327", "0001127602-25-008322", "0001127602-25-008317", "0001493152-25-009051", "0001493152-25-008928", "0001493152-25-008540", "0001127602-25-006578", "0001127602-25-006574", "0001493152-25-008137", "0001493152-25-007265", "0000950170-25-021496", "0001127602-25-004350", "0001127602-25-004348", "0001127602-25-004347", "0001493152-25-005205", "0001493152-25-004945", "0001493152-25-003745", "*********0-25-000669", "0001127602-25-000411", "0001127602-25-000406", "0001127602-25-000396", "0001127602-25-000391", "0001127602-25-000383", "0001127602-25-000375", "0001193125-24-284263", "0001193125-24-284258", "0001493152-24-050964", "0001127602-24-029383", "0001127602-24-029380", "0001193125-24-273921", "0000950170-24-125474", "0001493152-24-044652", "0001493152-24-044218", "0001127602-24-026731", "0001493152-24-043681", "0001127602-24-026386", "0001127602-24-026383", "0001127602-24-026380", "0001127602-24-026376", "0001193125-24-246134", "0001193125-24-244324", "0001193125-24-244217", "0001193125-24-244214", "0001127602-24-024955", "0001127602-24-024953", "0001127602-24-024949", "0001127602-24-024944", "0001127602-24-024938", "0001127602-24-024933", "0001493152-24-035572", "0000950170-24-094747", "0001493152-24-028828", "0001127602-24-019872", "0001127602-24-019865", "0001127602-24-019857", "0001127602-24-019848", "0001127602-24-019841", "0001127602-24-019835", "0001493152-24-022906", "0001193125-24-154236", "0001193125-24-153019", "0001193125-24-149137", "0001193125-24-139369", "0001193125-24-139359", "0000950170-24-059550", "0001493152-24-018118", "0001127602-24-013658", "0001127602-24-013656", "0001127602-24-013654", "0001127602-24-013490", "0001127602-24-013488", "0001193125-24-106975", "0001193125-24-102852", "0001140361-24-020060", "0001127602-24-011901", "0001127602-24-011895", "0001127602-24-011893", "0001127602-24-011887", "0001127602-24-011882", "0001127602-24-011875", "0001193125-24-084756", "0001193125-24-077564", "0001193125-24-076482", "0001193125-24-076472", "0000950170-24-034764", "0000950123-24-002950", "0001127602-24-010489", "0001127602-24-010327", "0001127602-24-010325", "0001127602-24-010323", "0001127602-24-010320", "0001193125-24-066954", "0001493152-24-009400", "0001493152-24-008942", "0001493152-24-008015", "0001193125-24-044527", "0001193125-24-036598", "0001193125-24-036587", "0001193125-24-018845", "0001127602-24-002276", "0001127602-24-002270", "0001127602-24-002267", "0001959173-24-000374", "0001193125-24-009509", "0001127602-24-000654", "0001127602-24-000652", "0001127602-24-000650", "0001127602-24-000648", "0001127602-24-000645", "0001127602-24-000643", "0001193125-23-300370", "0000950123-23-011100", "0000950170-23-065130", "0001193125-23-279380", "0001193125-23-279377", "0001193125-23-278957", "0001193125-23-277564", "0001193125-23-277563", "0001193125-23-275205", "0001193125-23-271933", "0000950170-23-059778", "0001193125-23-266305", "0001127602-23-025164", "0001127602-23-025157", "0001127602-23-025154", "0001127602-23-025151", "0001127602-23-025144", "0001127602-23-025141", "0001193125-23-245790", "0001193125-23-227712", "0001193125-23-227681", "9999999995-23-002485", "0001193125-23-216686", "*********0-23-009068", "0001193125-23-212576", "0000950170-23-039399", "0001193125-23-205615", "0001193125-23-188530", "0001193125-23-185209", "0001193125-23-185120", "0001193125-23-184702", "0001127602-23-019776", "0001127602-23-019774", "0001127602-23-019772", "0001127602-23-019770", "0001127602-23-019768", "0001127602-23-019766", "0001193125-23-180542", "0001193125-23-169739", "0001193125-23-163938", "0001193125-23-163582", "0001127602-23-017884", "0001127602-23-017885", "0001127602-23-017881", "0001127602-23-017879", "0000950123-23-005623", "0001564590-23-007471", "0001127602-23-016379", "0001127602-23-015796", "0000950170-23-018480", "0001564590-23-007055", "0001127602-23-013819", "0001127602-23-013714", "0001127602-23-013712", "0001127602-23-013710", "0001127602-23-013708", "0001564590-23-006505", "0001564590-23-006348", "0001564590-23-006228", "9999999995-23-001027", "0001564590-23-006057", "0001564590-23-006020", "0001564590-23-006018", "0001564590-23-006017", "0001564590-23-006016", "0001127602-23-013327", "0001564590-23-005971", "0001564590-23-005968", "0001564590-23-005966", "0001140361-23-019132", "0001127602-23-013177", "0001564590-23-005879", "0001564590-23-005877", "0001564590-23-005865", "0001564590-23-005831", "0001127602-23-013028", "0001127602-23-012944", "0001564590-23-005580", "0001564590-23-005579", "0001564590-23-005507", "0001127602-23-012463", "0001564590-23-005432", "0001127602-23-012021", "0001127602-23-012019", "0001127602-23-012017", "0001127602-23-012015", "0001127602-23-012013", "0001127602-23-012011", "0001127602-23-011737", "0001127602-23-011434", "0000950170-23-009766", "0001564590-23-004328", "0001127602-23-011265", "0001127602-23-011248", "0001127602-23-010968", "0001127602-23-010853", "0001564590-23-003928", "0001127602-23-009989", "0001127602-23-009813", "0001127602-23-009811", "0001127602-23-009777", "0001127602-23-009775", "0001127602-23-009773", "0001127602-23-009770", "0001127602-23-009696", "0001127602-23-009534", "0001127602-23-009532", "0001127602-23-009395", "0001127602-23-009394", "0001127602-23-009392", "0001127602-23-009205", "0001127602-23-008972", "0001127602-23-008532", "0001127602-23-008066", "0001127602-23-007701", "0001127602-23-006606", "0001127602-23-006340", "0001127602-23-006047", "0001127602-23-006045", "0001127602-23-006043", "0001127602-23-005727", "0001127602-23-005440", "0001127602-23-005438", "0001193125-23-038036", "0001193125-23-037957", "0001193125-23-037951", "0001127602-23-005033", "0001127602-23-005031", "0001564590-23-001800", "0001564590-23-001798", "0001564590-23-001792", "0001564590-23-001753", "0001564590-23-001749", "0001564590-23-001668", "0001564590-23-001667", "0001564590-23-001665", "0001564590-23-001663", "0001564590-23-001567", "0001564590-23-001566", "0001564590-23-001518", "0000950170-23-001817", "0000950170-23-001812", "0001127602-23-002395", "0001127602-23-001508", "0001127602-23-001010", "0001127602-23-000683", "0001127602-23-000654", "0001127602-23-000649", "0001127602-23-000647", "0001127602-23-000645", "0001127602-23-000643", "0001127602-23-000224", "0001127602-23-000002", "0001127602-22-028331", "*********0-22-013955", "0001127602-22-028285", "0000950123-22-012719", "0001127602-22-028136", "0001127602-22-027093", "0001127602-22-026326", "0001564590-22-038110", "0000950170-22-023956", "0001564590-22-037214", "0001564590-22-032061", "0001127602-22-022740", "0001127602-22-022739", "0001127602-22-022738", "0001127602-22-022737", "0001127602-22-022401", "0001564590-22-031108", "0001127602-22-021869", "0000899243-22-029752", "0000950170-22-017637", "0001564590-22-029854", "0001564590-22-028823", "0000899243-22-027462", "0000899243-22-027461", "0000899243-22-027459", "0000899243-22-027456", "0000899243-22-027455", "0000899243-22-027454", "0000899243-22-027453", "0000899243-22-027452", "0000899243-22-027451", "0000899243-22-027444", "0001564590-22-027287", "0001564590-22-027256", "0001564590-22-027005", "9999999997-22-003692", "9999999995-22-002172", "0000899243-22-026893", "0000899243-22-026892", "0000899243-22-026891", "0000899243-22-026888", "0000899243-22-026885", "0000899243-22-026883", "0000899243-22-026881", "0000899243-22-026874", "0000899243-22-026866", "0000899243-22-026853", "0000876661-22-000591", "0000950123-22-007443", "0001564590-22-026348", "0001564590-22-026340", "0001564590-22-026211", "*********0-22-007667", "0001564590-22-026067", "0001564590-22-026068", "*********0-22-007590", "0001564590-22-025724", "0001564590-22-025709", "0001564590-22-024583", "0001564590-22-024585", "0001564590-22-024584", "*********0-22-006128", "0001564590-22-021930", "0001564590-22-021928", "*********0-22-005679", "0001564590-22-020206", "0001564590-22-016865", "0001564590-22-016863", "9999999997-22-002234", "*********0-22-004398", "0001564590-22-014042", "0001564590-22-014036", "*********0-22-002321", "0001564590-22-005025", "0001564590-22-005018", "*********0-22-000291", "0000950123-21-016433", "0000950123-21-016430", "*********0-21-013817", "0000950123-21-013209"], "filingDate": ["2025-06-11", "2025-06-05", "2025-06-05", "2025-06-02", "2025-05-28", "2025-05-23", "2025-05-22", "2025-05-16", "2025-05-16", "2025-05-16", "2025-05-12", "2025-05-12", "2025-05-09", "2025-05-06", "2025-04-22", "2025-04-08", "2025-04-08", "2025-04-08", "2025-04-08", "2025-04-08", "2025-04-08", "2025-04-08", "2025-04-08", "2025-04-01", "2025-03-28", "2025-03-28", "2025-03-28", "2025-03-26", "2025-03-26", "2025-03-21", "2025-03-21", "2025-03-19", "2025-03-05", "2025-03-05", "2025-03-05", "2025-03-05", "2025-03-04", "2025-03-03", "2025-02-27", "2025-02-25", "2025-02-25", "2025-02-25", "2025-02-19", "2025-02-14", "2025-02-12", "2025-02-12", "2025-02-12", "2025-02-06", "2025-02-05", "2025-01-27", "2025-01-22", "2025-01-03", "2025-01-03", "2025-01-03", "2025-01-03", "2025-01-03", "2025-01-03", "2024-12-23", "2024-12-23", "2024-12-20", "2024-12-13", "2024-12-13", "2024-12-10", "2024-11-12", "2024-11-12", "2024-11-08", "2024-11-07", "2024-11-05", "2024-11-04", "2024-11-04", "2024-11-04", "2024-11-04", "2024-10-29", "2024-10-25", "2024-10-25", "2024-10-25", "2024-10-02", "2024-10-02", "2024-10-02", "2024-10-02", "2024-10-02", "2024-10-02", "2024-09-10", "2024-08-09", "2024-07-23", "2024-07-02", "2024-07-02", "2024-07-02", "2024-07-02", "2024-07-02", "2024-07-02", "2024-06-06", "2024-06-04", "2024-06-03", "2024-05-29", "2024-05-15", "2024-05-15", "2024-05-14", "2024-05-07", "2024-04-30", "2024-04-30", "2024-04-30", "2024-04-26", "2024-04-26", "2024-04-23", "2024-04-19", "2024-04-16", "2024-04-02", "2024-04-02", "2024-04-02", "2024-04-02", "2024-04-02", "2024-04-02", "2024-04-02", "2024-03-26", "2024-03-25", "2024-03-25", "2024-03-21", "2024-03-21", "2024-03-18", "2024-03-15", "2024-03-15", "2024-03-15", "2024-03-15", "2024-03-13", "2024-03-08", "2024-03-06", "2024-02-27", "2024-02-23", "2024-02-14", "2024-02-14", "2024-01-30", "2024-01-26", "2024-01-26", "2024-01-26", "2024-01-19", "2024-01-18", "2024-01-03", "2024-01-03", "2024-01-03", "2024-01-03", "2024-01-03", "2024-01-03", "2023-12-21", "2023-11-29", "2023-11-20", "2023-11-17", "2023-11-17", "2023-11-16", "2023-11-15", "2023-11-15", "2023-11-13", "2023-11-07", "2023-11-07", "2023-10-30", "2023-10-03", "2023-10-03", "2023-10-03", "2023-10-03", "2023-10-03", "2023-10-03", "2023-09-28", "2023-09-01", "2023-09-01", "2023-08-23", "2023-08-21", "2023-08-18", "2023-08-15", "2023-08-08", "2023-08-08", "2023-07-18", "2023-07-11", "2023-07-11", "2023-07-10", "2023-07-05", "2023-07-05", "2023-07-05", "2023-07-05", "2023-07-05", "2023-07-05", "2023-06-30", "2023-06-20", "2023-06-09", "2023-06-08", "2023-06-05", "2023-06-05", "2023-06-05", "2023-06-05", "2023-06-01", "2023-05-30", "2023-05-22", "2023-05-17", "2023-05-08", "2023-05-08", "2023-05-01", "2023-04-28", "2023-04-28", "2023-04-28", "2023-04-28", "2023-04-27", "2023-04-27", "2023-04-26", "2023-04-24", "2023-04-21", "2023-04-21", "2023-04-21", "2023-04-21", "2023-04-21", "2023-04-21", "2023-04-20", "2023-04-20", "2023-04-20", "2023-04-19", "2023-04-19", "2023-04-18", "2023-04-18", "2023-04-18", "2023-04-18", "2023-04-18", "2023-04-17", "2023-04-11", "2023-04-11", "2023-04-07", "2023-04-05", "2023-04-04", "2023-04-04", "2023-04-04", "2023-04-04", "2023-04-04", "2023-04-04", "2023-04-04", "2023-04-03", "2023-03-28", "2023-03-24", "2023-03-24", "2023-03-24", "2023-03-23", "2023-03-22", "2023-03-20", "2023-03-17", "2023-03-14", "2023-03-13", "2023-03-13", "2023-03-10", "2023-03-10", "2023-03-10", "2023-03-10", "2023-03-10", "2023-03-09", "2023-03-09", "2023-03-08", "2023-03-08", "2023-03-08", "2023-03-07", "2023-03-06", "2023-03-03", "2023-03-02", "2023-03-01", "2023-02-23", "2023-02-22", "2023-02-21", "2023-02-21", "2023-02-21", "2023-02-17", "2023-02-16", "2023-02-16", "2023-02-14", "2023-02-14", "2023-02-14", "2023-02-14", "2023-02-14", "2023-02-13", "2023-02-13", "2023-02-13", "2023-02-13", "2023-02-13", "2023-02-09", "2023-02-09", "2023-02-09", "2023-02-09", "2023-02-07", "2023-02-07", "2023-02-06", "2023-02-06", "2023-02-06", "2023-01-27", "2023-01-18", "2023-01-06", "2023-01-04", "2023-01-04", "2023-01-04", "2023-01-04", "2023-01-04", "2023-01-04", "2023-01-04", "2023-01-03", "2022-12-29", "2022-12-28", "2022-12-27", "2022-12-23", "2022-12-22", "2022-12-08", "2022-11-23", "2022-11-22", "2022-11-09", "2022-11-09", "2022-09-21", "2022-09-20", "2022-09-20", "2022-09-20", "2022-09-20", "2022-09-13", "2022-09-07", "2022-09-01", "2022-08-25", "2022-08-22", "2022-08-22", "2022-08-10", "2022-08-02", "2022-08-02", "2022-08-02", "2022-08-02", "2022-08-02", "2022-08-02", "2022-08-02", "2022-08-02", "2022-08-02", "2022-08-02", "2022-08-01", "2022-08-01", "2022-07-29", "2022-07-27", "2022-07-27", "2022-07-27", "2022-07-27", "2022-07-27", "2022-07-27", "2022-07-27", "2022-07-27", "2022-07-27", "2022-07-27", "2022-07-27", "2022-07-27", "2022-07-27", "2022-07-27", "2022-07-25", "2022-07-25", "2022-07-21", "2022-07-21", "2022-07-21", "2022-07-20", "2022-07-19", "2022-07-14", "2022-07-13", "2022-06-29", "2022-06-28", "2022-06-28", "2022-06-06", "2022-05-31", "2022-05-31", "2022-05-23", "2022-05-16", "2022-04-29", "2022-04-29", "2022-04-25", "2022-04-25", "2022-04-11", "2022-04-11", "2022-03-01", "2022-02-14", "2022-02-14", "2022-01-10", "2021-12-22", "2021-12-22", "2021-11-16", "2021-10-15"], "reportDate": ["", "2025-06-03", "2025-06-05", "2025-05-31", "2025-05-27", "2025-05-22", "2025-05-22", "2025-05-14", "2025-05-14", "2025-05-14", "2025-05-08", "2025-05-08", "2025-03-31", "2025-05-05", "2025-04-22", "2025-04-04", "2025-04-04", "2025-04-04", "2025-04-04", "2025-04-04", "2025-04-04", "", "2025-05-22", "2025-04-01", "", "2025-03-28", "2025-03-28", "2025-03-26", "", "2025-03-21", "2024-12-31", "2025-03-19", "2025-03-03", "2025-03-03", "2025-02-24", "2025-02-24", "", "", "2025-02-27", "2025-02-24", "2025-02-24", "2025-02-24", "2025-02-18", "", "2025-02-10", "2025-02-10", "2025-02-10", "2025-02-01", "2025-02-04", "", "", "2024-12-31", "2024-12-31", "2024-12-31", "2024-12-31", "2024-12-31", "2024-12-31", "2024-12-23", "", "2024-12-16", "2024-12-13", "2024-12-13", "2024-12-09", "2024-09-30", "", "2024-11-08", "2024-11-05", "2024-11-05", "2024-11-01", "2024-11-01", "2024-11-01", "2024-11-01", "2024-10-28", "", "", "", "2024-10-01", "2024-10-01", "2024-10-01", "2024-10-01", "2024-10-01", "2024-10-01", "2024-09-10", "2024-06-30", "2024-07-23", "2024-06-30", "2024-06-30", "2024-06-30", "2024-06-30", "2024-06-30", "2024-06-30", "2024-06-06", "2024-06-04", "2024-06-03", "2024-05-24", "2024-05-15", "", "2024-03-31", "2024-05-07", "2024-04-30", "2024-04-30", "2024-04-30", "2024-04-25", "2024-04-25", "2024-04-22", "", "2024-05-24", "2024-03-31", "2024-03-31", "2024-03-31", "2024-03-31", "2024-03-31", "2024-03-31", "2024-04-01", "2024-03-25", "2024-03-25", "", "2023-12-31", "", "2024-03-14", "2024-03-14", "2024-03-14", "2024-03-14", "2024-03-14", "2024-03-11", "2024-03-06", "2024-03-05", "2024-02-27", "2024-02-22", "", "2024-02-14", "2024-01-24", "2024-01-24", "2024-01-24", "2024-01-24", "", "2024-01-17", "2023-12-31", "2023-12-31", "2023-12-31", "2023-12-31", "2023-12-31", "2023-12-31", "2023-12-19", "", "2023-11-10", "2023-11-15", "", "2023-11-09", "2023-11-10", "", "2023-11-10", "2023-11-07", "2023-09-30", "2023-10-24", "2023-09-30", "2023-09-30", "2023-09-30", "2023-09-30", "2023-09-30", "2023-09-30", "2023-09-28", "2023-09-01", "", "", "", "", "", "2023-06-30", "2023-08-08", "", "", "2023-07-11", "2023-07-10", "2023-06-30", "2023-06-30", "2023-06-30", "2023-06-30", "2023-06-30", "2023-06-30", "", "2023-06-20", "", "", "2023-05-31", "2023-05-31", "2023-05-31", "2023-05-31", "", "2023-05-25", "2023-05-18", "2023-05-15", "2023-03-31", "2023-05-08", "2023-04-28", "2023-04-27", "2023-04-27", "2023-04-27", "2023-04-27", "2023-04-24", "", "", "", "", "", "", "", "", "2023-04-20", "", "", "2023-04-20", "2023-05-25", "2023-04-18", "", "", "", "2023-04-18", "2023-04-17", "2023-04-13", "", "2023-04-11", "", "2023-04-03", "", "2023-03-31", "2023-03-31", "2023-03-31", "2023-03-31", "2023-03-31", "2023-03-31", "2023-03-30", "2023-03-24", "2022-12-31", "2023-03-24", "2023-03-22", "2023-03-17", "2023-03-20", "2023-03-16", "2023-03-13", "2023-03-10", "2023-03-09", "2023-03-09", "2023-03-08", "2023-03-08", "2023-03-08", "2023-03-08", "2023-03-08", "2023-03-07", "2023-03-08", "2023-03-06", "2023-03-06", "2023-03-06", "2023-03-03", "2023-03-02", "2023-03-01", "2023-02-28", "2023-02-27", "2023-02-21", "2023-02-17", "2023-02-16", "2023-02-16", "2023-02-16", "2023-02-15", "2023-02-14", "2023-02-14", "", "", "", "2023-02-10", "2023-02-10", "", "", "2023-02-13", "2023-02-13", "", "", "", "", "", "", "", "", "2022-09-30", "2023-02-03", "2023-01-25", "2023-01-13", "2023-01-04", "2022-12-30", "2022-12-30", "2022-12-30", "2022-12-30", "2022-12-30", "2022-12-30", "2022-12-30", "2022-12-29", "2022-12-27", "", "2022-12-22", "", "2022-12-20", "2022-12-07", "2022-08-01", "2022-11-21", "2022-09-30", "2022-11-09", "2022-09-16", "2022-09-16", "2022-09-16", "2022-09-16", "2022-09-16", "2022-09-13", "2022-09-07", "2022-08-30", "2022-08-24", "2022-06-30", "2022-08-22", "", "2022-08-01", "2022-08-01", "2022-08-01", "2022-08-01", "2022-08-01", "2022-08-01", "2022-08-01", "2022-08-01", "2022-08-01", "2022-07-28", "", "2022-07-27", "", "", "", "2022-07-27", "2022-07-27", "2022-07-27", "2022-07-27", "2022-07-27", "2022-07-27", "2022-07-27", "2022-07-27", "2022-07-27", "2022-07-27", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "acceptanceDateTime": ["2025-06-11T16:15:32.000Z", "2025-06-05T20:42:27.000Z", "2025-06-05T20:05:42.000Z", "2025-06-02T10:06:48.000Z", "2025-05-28T13:00:41.000Z", "2025-05-23T13:22:59.000Z", "2025-05-22T20:15:34.000Z", "2025-05-16T20:15:26.000Z", "2025-05-16T20:14:36.000Z", "2025-05-16T20:13:34.000Z", "2025-05-12T20:14:02.000Z", "2025-05-12T20:12:43.000Z", "2025-05-09T20:05:39.000Z", "2025-05-06T11:30:33.000Z", "2025-04-22T20:15:56.000Z", "2025-04-08T20:49:28.000Z", "2025-04-08T20:47:44.000Z", "2025-04-08T20:45:58.000Z", "2025-04-08T20:44:14.000Z", "2025-04-08T20:42:32.000Z", "2025-04-08T20:41:05.000Z", "2025-04-08T10:25:10.000Z", "2025-04-08T10:23:28.000Z", "2025-04-01T12:30:13.000Z", "2025-03-28T20:39:37.000Z", "2025-03-28T20:06:14.000Z", "2025-03-28T11:01:04.000Z", "2025-03-26T21:14:14.000Z", "2025-03-26T21:03:09.000Z", "2025-03-21T20:31:31.000Z", "2025-03-21T20:08:30.000Z", "2025-03-19T20:30:23.000Z", "2025-03-05T21:42:20.000Z", "2025-03-05T21:40:57.000Z", "2025-03-05T21:39:20.000Z", "2025-03-05T21:37:22.000Z", "2025-03-04T21:16:38.000Z", "2025-03-03T22:28:34.000Z", "2025-02-27T22:00:09.000Z", "2025-02-25T21:11:24.000Z", "2025-02-25T21:10:22.000Z", "2025-02-25T12:30:12.000Z", "2025-02-19T12:00:20.000Z", "2025-02-14T22:27:29.000Z", "2025-02-12T21:11:42.000Z", "2025-02-12T21:10:50.000Z", "2025-02-12T21:09:55.000Z", "2025-02-06T21:30:52.000Z", "2025-02-05T12:30:40.000Z", "2025-01-27T17:20:06.000Z", "2025-01-22T19:36:06.000Z", "2025-01-03T21:15:41.000Z", "2025-01-03T21:14:03.000Z", "2025-01-03T21:11:50.000Z", "2025-01-03T21:09:35.000Z", "2025-01-03T21:07:27.000Z", "2025-01-03T21:05:27.000Z", "2024-12-23T21:51:31.000Z", "2024-12-23T21:49:11.000Z", "2024-12-20T18:05:54.000Z", "2024-12-13T22:17:59.000Z", "2024-12-13T22:15:42.000Z", "2024-12-10T11:32:29.000Z", "2024-11-12T21:20:33.000Z", "2024-11-12T17:54:41.000Z", "2024-11-08T17:51:36.000Z", "2024-11-07T21:25:48.000Z", "2024-11-05T22:00:22.000Z", "2024-11-04T21:39:49.000Z", "2024-11-04T21:38:07.000Z", "2024-11-04T21:36:18.000Z", "2024-11-04T21:34:28.000Z", "2024-10-29T10:17:01.000Z", "2024-10-25T22:16:55.000Z", "2024-10-25T21:12:55.000Z", "2024-10-25T21:10:55.000Z", "2024-10-02T20:55:54.000Z", "2024-10-02T20:54:08.000Z", "2024-10-02T20:52:15.000Z", "2024-10-02T20:49:16.000Z", "2024-10-02T20:47:05.000Z", "2024-10-02T20:45:24.000Z", "2024-09-10T10:15:29.000Z", "2024-08-09T20:06:00.000Z", "2024-07-23T21:05:10.000Z", "2024-07-02T20:16:15.000Z", "2024-07-02T20:14:36.000Z", "2024-07-02T20:13:09.000Z", "2024-07-02T20:11:46.000Z", "2024-07-02T20:10:18.000Z", "2024-07-02T20:08:43.000Z", "2024-06-06T18:57:40.000Z", "2024-06-04T20:06:02.000Z", "2024-06-03T20:05:55.000Z", "2024-05-29T20:05:58.000Z", "2024-05-15T20:07:15.000Z", "2024-05-15T20:05:52.000Z", "2024-05-14T20:11:03.000Z", "2024-05-07T21:21:03.000Z", "2024-04-30T21:05:48.000Z", "2024-04-30T21:03:58.000Z", "2024-04-30T21:02:43.000Z", "2024-04-26T21:33:57.000Z", "2024-04-26T21:30:49.000Z", "2024-04-23T20:05:55.000Z", "2024-04-19T20:01:09.000Z", "2024-04-16T20:01:51.000Z", "2024-04-02T21:01:33.000Z", "2024-04-02T20:59:55.000Z", "2024-04-02T20:58:03.000Z", "2024-04-02T20:56:23.000Z", "2024-04-02T20:54:22.000Z", "2024-04-02T20:52:23.000Z", "2024-04-02T20:10:55.000Z", "2024-03-26T19:36:34.000Z", "2024-03-25T20:08:05.000Z", "2024-03-25T20:05:46.000Z", "2024-03-21T20:30:49.000Z", "2024-03-21T19:57:19.000Z", "2024-03-18T21:34:31.000Z", "2024-03-15T23:11:19.000Z", "2024-03-15T23:09:40.000Z", "2024-03-15T23:08:00.000Z", "2024-03-15T23:04:21.000Z", "2024-03-13T21:14:00.000Z", "2024-03-08T21:15:28.000Z", "2024-03-06T13:00:38.000Z", "2024-02-27T22:23:46.000Z", "2024-02-23T21:17:06.000Z", "2024-02-14T22:18:30.000Z", "2024-02-14T22:15:18.000Z", "2024-01-30T14:00:30.000Z", "2024-01-26T21:10:34.000Z", "2024-01-26T21:08:49.000Z", "2024-01-26T21:07:25.000Z", "2024-01-20T02:00:42.000Z", "2024-01-18T11:01:54.000Z", "2024-01-04T02:07:17.000Z", "2024-01-04T02:05:22.000Z", "2024-01-04T02:03:50.000Z", "2024-01-04T02:02:10.000Z", "2024-01-04T01:59:50.000Z", "2024-01-04T01:57:31.000Z", "2023-12-21T12:00:55.000Z", "2023-11-29T21:19:28.000Z", "2023-11-20T21:31:15.000Z", "2023-11-17T14:09:31.000Z", "2023-11-17T14:06:04.000Z", "2023-11-16T22:26:30.000Z", "2023-11-15T13:42:29.000Z", "2023-11-15T13:41:11.000Z", "2023-11-13T11:07:01.000Z", "2023-11-07T13:05:30.000Z", "2023-11-07T13:00:48.000Z", "2023-10-30T21:24:29.000Z", "2023-10-03T20:14:52.000Z", "2023-10-03T20:13:04.000Z", "2023-10-03T20:11:51.000Z", "2023-10-03T20:10:37.000Z", "2023-10-03T20:08:35.000Z", "2023-10-03T20:05:49.000Z", "2023-09-28T21:12:03.000Z", "2023-09-01T20:45:55.000Z", "2023-09-01T20:30:43.000Z", "2023-08-24T04:15:08.000Z", "2023-08-21T14:17:55.000Z", "2023-08-18T20:30:06.000Z", "2023-08-14T22:05:18.000Z", "2023-08-08T12:29:53.000Z", "2023-08-08T12:15:52.000Z", "2023-07-18T12:59:29.000Z", "2023-07-11T16:42:02.000Z", "2023-07-11T14:26:28.000Z", "2023-07-10T20:41:00.000Z", "2023-07-05T12:20:25.000Z", "2023-07-05T12:17:49.000Z", "2023-07-05T12:16:33.000Z", "2023-07-05T12:14:30.000Z", "2023-07-05T12:13:07.000Z", "2023-07-05T12:11:33.000Z", "2023-06-30T20:56:36.000Z", "2023-06-20T13:06:53.000Z", "2023-06-09T10:47:00.000Z", "2023-06-08T20:56:10.000Z", "2023-06-05T21:23:46.000Z", "2023-06-05T21:23:44.000Z", "2023-06-05T21:20:54.000Z", "2023-06-05T21:17:33.000Z", "2023-06-01T00:45:16.000Z", "2023-05-26T22:20:00.000Z", "2023-05-22T10:49:30.000Z", "2023-05-17T10:55:00.000Z", "2023-05-08T12:28:43.000Z", "2023-05-08T12:15:29.000Z", "2023-05-01T10:30:18.000Z", "2023-04-28T10:52:27.000Z", "2023-04-28T10:50:27.000Z", "2023-04-28T10:49:10.000Z", "2023-04-28T10:46:20.000Z", "2023-04-27T21:25:15.000Z", "2023-04-27T11:22:55.000Z", "2023-04-25T23:45:33.000Z", "2023-04-25T04:15:07.000Z", "2023-04-21T20:32:49.000Z", "2023-04-21T15:53:21.000Z", "2023-04-21T15:46:20.000Z", "2023-04-21T15:42:13.000Z", "2023-04-21T15:38:28.000Z", "2023-04-21T11:53:28.000Z", "2023-04-20T13:38:06.000Z", "2023-04-20T13:16:30.000Z", "2023-04-20T12:43:28.000Z", "2023-04-19T20:06:37.000Z", "2023-04-19T10:55:30.000Z", "2023-04-18T19:51:55.000Z", "2023-04-18T19:44:35.000Z", "2023-04-18T19:08:25.000Z", "2023-04-18T15:39:29.000Z", "2023-04-18T12:42:47.000Z", "2023-04-17T11:01:03.000Z", "2023-04-11T12:30:47.000Z", "2023-04-11T12:15:37.000Z", "2023-04-06T22:24:51.000Z", "2023-04-05T10:48:49.000Z", "2023-04-04T21:23:51.000Z", "2023-04-04T10:36:24.000Z", "2023-04-04T10:34:51.000Z", "2023-04-04T10:33:35.000Z", "2023-04-04T10:32:17.000Z", "2023-04-04T10:30:53.000Z", "2023-04-04T10:28:42.000Z", "2023-04-03T13:32:19.000Z", "2023-03-28T11:45:14.000Z", "2023-03-24T20:55:06.000Z", "2023-03-24T20:51:43.000Z", "2023-03-24T12:53:46.000Z", "2023-03-24T00:40:46.000Z", "2023-03-22T10:42:33.000Z", "2023-03-20T20:27:36.000Z", "2023-03-17T20:15:49.000Z", "2023-03-14T11:45:41.000Z", "2023-03-13T10:27:35.000Z", "2023-03-13T10:25:39.000Z", "2023-03-10T22:13:53.000Z", "2023-03-10T22:12:00.000Z", "2023-03-10T22:06:34.000Z", "2023-03-10T22:01:16.000Z", "2023-03-10T11:37:02.000Z", "2023-03-09T11:30:43.000Z", "2023-03-09T11:28:25.000Z", "2023-03-08T11:40:44.000Z", "2023-03-08T11:37:36.000Z", "2023-03-08T11:34:55.000Z", "2023-03-07T11:33:37.000Z", "2023-03-06T11:19:35.000Z", "2023-03-03T11:10:35.000Z", "2023-03-02T11:32:19.000Z", "2023-03-01T11:38:38.000Z", "2023-02-23T11:42:46.000Z", "2023-02-22T11:24:58.000Z", "2023-02-21T11:58:15.000Z", "2023-02-21T11:56:40.000Z", "2023-02-21T11:54:52.000Z", "2023-02-17T11:05:27.000Z", "2023-02-16T11:46:46.000Z", "2023-02-16T11:44:46.000Z", "2023-02-14T17:00:03.000Z", "2023-02-14T16:37:00.000Z", "2023-02-14T16:35:59.000Z", "2023-02-14T12:00:50.000Z", "2023-02-14T11:58:11.000Z", "2023-02-13T21:48:02.000Z", "2023-02-13T21:46:57.000Z", "2023-02-13T21:06:21.000Z", "2023-02-13T13:10:50.000Z", "2023-02-13T11:02:18.000Z", "2023-02-09T21:09:37.000Z", "2023-02-09T21:05:58.000Z", "2023-02-09T21:01:56.000Z", "2023-02-09T20:58:08.000Z", "2023-02-07T22:31:45.000Z", "2023-02-07T22:30:24.000Z", "2023-02-06T22:31:10.000Z", "2023-02-06T11:16:10.000Z", "2023-02-06T11:02:40.000Z", "2023-01-27T12:15:32.000Z", "2023-01-18T12:11:36.000Z", "2023-01-06T13:58:21.000Z", "2023-01-04T23:18:12.000Z", "2023-01-04T22:52:04.000Z", "2023-01-04T22:50:57.000Z", "2023-01-04T22:49:55.000Z", "2023-01-04T22:48:58.000Z", "2023-01-04T22:47:44.000Z", "2023-01-04T12:29:00.000Z", "2023-01-03T12:42:22.000Z", "2022-12-29T12:43:46.000Z", "2022-12-28T19:30:04.000Z", "2022-12-27T13:32:56.000Z", "2022-12-23T16:11:53.000Z", "2022-12-22T13:00:29.000Z", "2022-12-08T22:36:45.000Z", "2022-11-23T19:53:16.000Z", "2022-11-22T02:38:54.000Z", "2022-11-09T21:46:07.000Z", "2022-11-09T21:30:44.000Z", "2022-09-21T20:15:45.000Z", "2022-09-20T15:21:13.000Z", "2022-09-20T15:18:54.000Z", "2022-09-20T15:17:21.000Z", "2022-09-20T15:13:27.000Z", "2022-09-13T22:19:53.000Z", "2022-09-07T10:55:36.000Z", "2022-09-01T17:46:06.000Z", "2022-08-25T20:47:03.000Z", "2022-08-22T12:44:19.000Z", "2022-08-22T12:16:06.000Z", "2022-08-09T22:24:10.000Z", "2022-08-02T22:41:53.000Z", "2022-08-02T22:40:04.000Z", "2022-08-02T22:35:29.000Z", "2022-08-02T22:33:24.000Z", "2022-08-02T22:33:03.000Z", "2022-08-02T22:32:59.000Z", "2022-08-02T22:32:31.000Z", "2022-08-02T22:32:08.000Z", "2022-08-02T22:31:58.000Z", "2022-08-02T22:29:11.000Z", "2022-08-01T21:30:02.000Z", "2022-08-01T20:05:46.000Z", "2022-07-29T12:31:46.000Z", "2022-07-28T13:09:43.000Z", "2022-07-28T04:18:26.000Z", "2022-07-27T22:36:27.000Z", "2022-07-27T22:35:23.000Z", "2022-07-27T22:34:56.000Z", "2022-07-27T22:33:45.000Z", "2022-07-27T22:32:49.000Z", "2022-07-27T22:32:15.000Z", "2022-07-27T22:30:20.000Z", "2022-07-27T22:27:48.000Z", "2022-07-27T22:24:27.000Z", "2022-07-27T22:17:29.000Z", "2022-07-27T13:16:50.000Z", "2022-07-27T12:09:46.000Z", "2022-07-25T21:06:36.000Z", "2022-07-25T20:59:08.000Z", "2022-07-22T01:09:35.000Z", "2022-07-21T16:30:05.000Z", "2022-07-21T00:59:48.000Z", "2022-07-21T01:03:22.000Z", "2022-07-19T20:30:03.000Z", "2022-07-14T01:58:45.000Z", "2022-07-13T21:29:10.000Z", "2022-06-29T00:08:53.000Z", "2022-06-29T00:11:52.000Z", "2022-06-29T00:10:47.000Z", "2022-06-06T22:30:04.000Z", "2022-05-31T21:30:49.000Z", "2022-05-31T21:29:47.000Z", "2022-05-23T23:30:04.000Z", "2022-05-13T22:53:43.000Z", "2022-04-29T21:36:55.000Z", "2022-04-29T21:31:00.000Z", "2022-04-26T13:16:07.000Z", "2022-04-25T16:30:03.000Z", "2022-04-11T10:02:39.000Z", "2022-04-09T00:21:42.000Z", "2022-03-01T23:30:05.000Z", "2022-02-14T22:20:57.000Z", "2022-02-14T22:07:39.000Z", "2022-01-10T23:30:04.000Z", "2021-12-22T19:27:01.000Z", "2021-12-22T17:15:50.000Z", "2021-11-16T12:30:08.000Z", "2021-10-15T21:19:51.000Z"], "act": ["33", "", "34", "34", "34", "34", "34", "", "", "", "", "", "34", "34", "34", "", "", "", "", "", "", "34", "34", "34", "33", "34", "34", "34", "33", "34", "34", "34", "", "", "", "", "33", "34", "34", "", "", "34", "34", "34", "", "", "", "34", "34", "", "", "", "", "", "", "", "", "34", "33", "34", "", "", "34", "34", "33", "34", "", "34", "", "", "", "", "34", "34", "34", "34", "", "", "", "", "", "", "34", "34", "34", "", "", "", "", "", "", "34", "34", "34", "34", "34", "33", "34", "34", "", "", "", "", "", "34", "33", "34", "", "", "", "", "", "", "34", "34", "34", "33", "34", "33", "", "", "", "", "", "34", "34", "34", "34", "34", "33", "34", "34", "", "", "", "33", "34", "", "", "", "", "", "", "34", "33", "", "34", "33", "34", "34", "33", "34", "34", "34", "34", "", "", "", "", "", "", "34", "34", "33", "33", "", "", "33", "34", "34", "NE", "34", "34", "34", "", "", "", "", "", "", "33", "34", "34", "33", "", "", "", "", "33", "34", "", "", "34", "34", "", "", "", "", "", "34", "33", "33", "33", "33", "", "", "", "", "", "34", "33", "34", "34", "", "", "", "34", "34", "", "", "34", "34", "34", "", "33", "", "", "", "", "", "", "", "", "34", "34", "", "", "", "", "34", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "34", "34", "34", "", "", "", "", "34", "34", "34", "", "", "", "", "", "", "33", "34", "34", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "33", "", "", "", "34", "34", "34", "34", "", "", "", "", "", "34", "", "", "34", "34", "34", "", "", "", "", "", "", "", "", "", "", "33", "34", "33", "", "33", "", "", "", "", "", "", "", "", "", "", "34", "34", "", "", "", "", "33", "", "", "34", "33", "33", "", "", "", "", "33", "", "33", "", "33", "", "", "", "33", "", "", "33", "", "", "33", "", "33"], "form": ["D", "4", "8-K", "8-K", "8-K", "8-K", "8-K", "4", "4", "4", "4", "4", "10-Q", "8-K", "8-K", "4", "4", "4", "4", "4", "4", "DEFA14A", "DEF 14A", "8-K", "S-8", "PRE 14A", "8-K", "8-K", "424B5", "8-K", "10-K", "8-K", "4", "4", "4/A", "4/A", "D", "SCHEDULE 13G", "8-K", "4", "4", "8-K", "8-K", "SCHEDULE 13G/A", "4", "4", "4", "8-K", "8-K", "CORRESP", "UPLOAD", "4", "4", "4", "4", "4", "4", "8-K", "424B5", "8-K", "4", "4", "8-K", "10-Q", "D", "8-K", "4", "8-K", "4", "4", "4", "4", "8-K", "SC 13D/A", "SC 13D", "SC 13D", "4", "4", "4", "4", "4", "4", "8-K", "10-Q", "8-K", "4", "4", "4", "4", "4", "4", "8-K", "8-K", "8-K", "8-K", "8-K", "424B5", "10-Q", "8-K", "4", "4", "4", "4", "4", "8-K", "S-8", "DEF 14A", "4", "4", "4", "4", "4", "4", "8-K", "8-K", "8-K", "424B5", "10-K", "D", "4", "4", "4", "4", "4", "8-K", "8-K", "8-K", "8-K", "8-K", "424B5", "8-K", "8-K", "4", "4", "4", "144", "8-K", "4", "4", "4", "4", "4", "4", "8-K", "D", "3", "8-K", "424B5", "8-K", "8-K", "424B5", "8-K", "8-K", "10-Q", "8-K", "4", "4", "4", "4", "4", "4", "8-K", "8-K", "424B5", "EFFECT", "CORRESP", "UPLOAD", "S-3", "10-Q", "8-K", "RW", "FWP", "8-K", "8-K", "4", "4", "4", "4", "4", "4", "S-8", "8-K", "FWP", "S-1", "4", "4", "4", "4", "DRS", "8-K", "4", "4", "10-Q", "8-K", "4", "4", "4", "4", "4", "8-K", "424B3", "424B4", "EFFECT", "S-1/A", "CORRESP", "CORRESP", "CORRESP", "CORRESP", "4", "FWP", "S-1/A", "8-K", "DEF 14A", "4", "CORRESP", "CORRESP", "FWP", "8-K", "4", "4", "FWP", "8-K", "FWP", "4", "S-1/A", "4", "4", "4", "4", "4", "4", "4", "4", "10-K", "8-K", "4", "3", "4", "4", "8-K", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "SC 13G", "SC 13G", "SC 13G", "4", "4", "CORRESP", "CORRESP", "8-K/A", "8-K", "FWP", "CORRESP", "CORRESP", "CORRESP", "CORRESP", "CORRESP", "CORRESP", "S-1", "10-Q/A", "8-K", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "UPLOAD", "4", "DRS", "4", "4", "4/A", "8-K", "10-Q", "8-K", "8-K", "4", "4", "4", "4", "4", "8-K", "4", "4", "10-Q", "8-K", "SC 13D", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "S-8", "8-K", "424B4", "SEC STAFF LETTER", "EFFECT", "3", "3", "3", "3", "3", "3", "3", "3", "3", "3", "CERT", "8-A12B", "CORRESP", "CORRESP", "CORRESP", "UPLOAD", "S-1/A", "CORRESP", "UPLOAD", "FWP", "S-1/A", "S-1/A", "CORRESP", "CORRESP", "UPLOAD", "CORRESP", "S-1/A", "UPLOAD", "S-1/A", "CORRESP", "S-1/A", "SEC STAFF LETTER", "UPLOAD", "CORRESP", "S-1", "UPLOAD", "DRSLTR", "DRS/A", "UPLOAD", "DRSLTR", "DRS/A", "UPLOAD", "DRS"], "fileNumber": ["021-548685", "", "001-41455", "001-41455", "001-41455", "001-41455", "001-41455", "", "", "", "", "", "001-41455", "001-41455", "001-41455", "", "", "", "", "", "", "001-41455", "001-41455", "001-41455", "333-286225", "001-41455", "001-41455", "001-41455", "333-273984", "001-41455", "001-41455", "001-41455", "", "", "", "", "021-539826", "005-93731", "001-41455", "", "", "001-41455", "001-41455", "005-93731", "", "", "", "001-41455", "001-41455", "", "005-93731", "", "", "", "", "", "", "001-41455", "333-273984", "001-41455", "", "", "001-41455", "001-41455", "021-529135", "001-41455", "", "001-41455", "", "", "", "", "001-41455", "005-93731", "005-93731", "005-93731", "", "", "", "", "", "", "001-41455", "001-41455", "001-41455", "", "", "", "", "", "", "001-41455", "001-41455", "001-41455", "001-41455", "001-41455", "333-273984", "001-41455", "001-41455", "", "", "", "", "", "001-41455", "333-278828", "001-41455", "", "", "", "", "", "", "001-41455", "001-41455", "001-41455", "333-273984", "001-41455", "021-508262", "", "", "", "", "", "001-41455", "001-41455", "001-41455", "001-41455", "001-41455", "333-273984", "001-41455", "001-41455", "", "", "", "001-41455", "001-41455", "", "", "", "", "", "", "001-41455", "021-498379", "", "001-41455", "333-273984", "001-41455", "001-41455", "333-273984", "001-41455", "001-41455", "001-41455", "001-41455", "", "", "", "", "", "", "001-41455", "001-41455", "333-273984", "333-273984", "", "", "333-273984", "001-41455", "001-41455", "333-272524", "333-272524", "001-41455", "001-41455", "", "", "", "", "", "", "333-273086", "001-41455", "333-272524", "333-272524", "", "", "", "", "377-06733", "001-41455", "", "", "001-41455", "001-41455", "", "", "", "", "", "001-41455", "333-269606", "333-269606", "333-269606", "333-269606", "", "", "", "", "", "333-269606", "333-269606", "001-41455", "001-41455", "", "", "", "333-269606", "001-41455", "", "", "333-269606", "001-41455", "333-269606", "", "333-269606", "", "", "", "", "", "", "", "", "001-41455", "001-41455", "", "", "", "", "001-41455", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "005-93731", "005-93731", "005-93731", "", "", "", "", "001-41455", "001-41455", "333-269606", "", "", "", "", "", "", "333-269606", "001-41455", "001-41455", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "377-06525", "", "", "", "001-41455", "001-41455", "001-41455", "001-41455", "", "", "", "", "", "001-41455", "", "", "001-41455", "001-41455", "005-93731", "", "", "", "", "", "", "", "", "", "", "333-266453", "001-41455", "333-264225", "", "333-264225", "", "", "", "", "", "", "", "", "", "", "001-41455", "001-41455", "", "", "", "", "333-264225", "", "", "333-264225", "333-264225", "333-264225", "", "", "", "", "333-264225", "", "333-264225", "", "333-264225", "", "", "", "333-264225", "", "", "377-05598", "", "", "377-05598", "", "377-05598"], "filmNumber": ["251040188", "", "251027103", "251013021", "25991429", "25979573", "25977329", "", "", "", "", "", "25930353", "25915251", "25857917", "", "", "", "", "", "", "25820184", "25820171", "25796794", "25787034", "25785965", "25781442", "25774280", "25774165", "25760991", "25760548", "25753173", "", "", "", "", "25704663", "25699781", "25680413", "", "", "25659406", "25637146", "25631167", "", "", "", "25597862", "25590904", "", "", "", "", "", "", "", "", "241574537", "241574518", "241566655", "", "", "241537098", "241448569", "241446293", "241439116", "", "241428388", "", "", "", "", "241402526", "241398096", "241397840", "241397833", "", "", "", "", "", "", "241288701", "241192593", "241135352", "", "", "", "", "", "", "241024723", "241017954", "241013606", "24998340", "24950515", "24950420", "24944444", "24923296", "", "", "", "", "", "24864843", "24857654", "24847781", "", "", "", "", "", "", "24814300", "24783246", "24779279", "24779223", "24771832", "24771166", "", "", "", "", "", "24746966", "24734544", "24724404", "24688689", "24671427", "24640614", "24640534", "24576016", "", "", "", "24546910", "24540046", "", "", "", "", "", "", "231502758", "231452469", "", "231416724", "231416717", "231415624", "231409156", "231409152", "231394669", "231381660", "231381642", "231361577", "", "", "", "", "", "", "231290782", "231233149", "231232974", "231198820", "", "", "231172454", "231149482", "231149457", "231093103", "231081696", "231081408", "231080224", "", "", "", "", "", "", "231062504", "231023693", "231003839", "231002679", "", "", "", "", "23982774", "23970838", "", "", "23895957", "23895938", "", "", "", "", "", "23858598", "23851871", "23846437", "23841502", "23836815", "", "", "", "", "", "23831643", "23831582", "23831491", "23830103", "", "", "", "23826533", "23825954", "", "", "23812432", "23812410", "23807396", "", "23799539", "", "", "", "", "", "", "", "", "23760565", "23760544", "", "", "", "", "23742776", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "23625163", "23624877", "23624864", "", "", "", "", "23618361", "23614820", "23614408", "", "", "", "", "", "", "23591708", "23588309", "23588249", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "221483948", "", "", "", "221407324", "221373362", "221373154", "221256589", "", "", "", "", "", "221229885", "", "", "221182298", "221182259", "221149808", "", "", "", "", "", "", "", "", "", "", "221125997", "221124758", "221117496", "", "221112586", "", "", "", "", "", "", "", "", "", "", "221109165", "221108905", "", "", "", "", "221095417", "", "", "221082141", "221081706", "221051495", "", "", "", "", "22984874", "", "22924583", "", "22877850", "", "", "", "22818301", "", "", "22634515", "", "", "211512244", "", "211326755"], "items": ["06b", "", "8.01,9.01", "7.01,8.01,9.01", "1.01,3.02,8.01", "5.03,9.01", "5.07,8.01,9.01", "", "", "", "", "", "", "1.01,3.02,8.01,9.01", "8.01,9.01", "", "", "", "", "", "", "", "", "8.01,9.01", "", "", "7.01,9.01", "8.01,9.01", "", "8.01,9.01", "", "8.01,9.01", "", "", "", "", "06b", "", "8.01,9.01", "", "", "1.01,3.02,8.01,9.01", "1.01,3.02,8.01,9.01", "", "", "", "", "5.02,9.01", "7.01,8.01,9.01", "", "", "", "", "", "", "", "", "8.01,9.01", "", "8.01,9.01", "", "", "1.01,3.02,8.01,9.01", "", "06b", "7.01,9.01", "", "8.01,9.01", "", "", "", "", "1.01,3.02,8.01,9.01", "", "", "", "", "", "", "", "", "", "7.01,8.01,9.01", "", "8.01,9.01", "", "", "", "", "", "", "7.01,9.01", "7.01,8.01,9.01", "7.01,9.01", "5.07", "8.01,9.01", "", "", "7.01,9.01", "", "", "", "", "", "1.01,3.02,8.01,9.01", "", "", "", "", "", "", "", "", "5.02", "1.01,3.02,8.01,9.01", "8.01,9.01", "", "", "06b", "", "", "", "", "", "1.01,3.02,9.01", "8.01,9.01", "8.01,9.01", "8.01,9.01", "8.01,9.01", "", "1.01,9.01", "8.01,9.01", "", "", "", "", "8.01,9.01", "", "", "", "", "", "", "8.01,9.01", "06b", "", "1.01,3.02,8.01,9.01", "", "1.01,3.02,9.01", "1.02,3.01,5.02,8.01,9.01", "", "8.01,9.01", "2.02,7.01,9.01", "", "8.01,9.01", "", "", "", "", "", "", "8.01,9.01", "1.01,9.01", "", "S-3,,2023-08-23 16:30:00", "", "", "", "", "2.02,7.01,9.01", "", "", "7.01,9.01", "7.01,9.01", "", "", "", "", "", "", "", "7.01,9.01", "", "", "", "", "", "", "", "5.07", "", "", "", "2.02,7.01,9.01", "", "", "", "", "", "1.01,7.01,9.01", "", "", "S-1,,2023-04-24 17:30:00", "", "", "", "", "", "", "", "", "7.01,9.01", "", "", "", "", "", "7.01,9.01", "", "", "", "7.01,9.01", "", "", "", "", "", "", "", "", "", "", "", "", "2.02,7.01,9.01", "", "", "", "", "5.02", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "7.01,9.01", "7.01,9.01", "", "", "", "", "", "", "", "", "", "4.02", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "4.01,9.01", "", "2.02,7.01,9.01", "5.02,9.01", "", "", "", "", "", "7.01,9.01", "", "", "", "2.02,7.01,9.01", "", "", "", "", "", "", "", "", "", "", "", "", "1.01,5.03,7.01,9.01", "", "", "S-1,,2022-07-27 17:00:00", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "core_type": ["D", "4", "XBRL", "XBRL", "XBRL", "XBRL", "XBRL", "4", "4", "4", "4", "4", "XBRL", "XBRL", "XBRL", "4", "4", "4", "4", "4", "4", "DEFA14A", "XBRL", "XBRL", "S-8", "XBRL", "XBRL", "XBRL", "424B5", "XBRL", "XBRL", "XBRL", "4", "4", "4/A", "4/A", "D", "SCHEDULE 13G", "XBRL", "4", "4", "XBRL", "XBRL", "SCHEDULE 13G/A", "4", "4", "4", "XBRL", "XBRL", "CORRESP", "LETTER", "4", "4", "4", "4", "4", "4", "XBRL", "424B5", "XBRL", "4", "4", "XBRL", "XBRL", "D", "XBRL", "4", "XBRL", "4", "4", "4", "4", "XBRL", "SC 13D/A", "SC 13D", "SC 13D", "4", "4", "4", "4", "4", "4", "XBRL", "XBRL", "XBRL", "4", "4", "4", "4", "4", "4", "XBRL", "XBRL", "XBRL", "XBRL", "XBRL", "424B5", "XBRL", "XBRL", "4", "4", "4", "4", "4", "XBRL", "S-8", "DEF 14A", "4", "4", "4", "4", "4", "4", "XBRL", "XBRL", "XBRL", "424B5", "XBRL", "D", "4", "4", "4", "4", "4", "XBRL", "XBRL", "XBRL", "XBRL", "XBRL", "424B5", "XBRL", "XBRL", "4", "4", "4", "144", "XBRL", "4", "4", "4", "4", "4", "4", "XBRL", "D", "3", "XBRL", "424B5", "XBRL", "XBRL", "424B5", "XBRL", "XBRL", "XBRL", "XBRL", "4", "4", "4", "4", "4", "4", "XBRL", "XBRL", "424B5", "EFFECT", "CORRESP", "LETTER", "S-3", "XBRL", "XBRL", "RW", "FWP", "XBRL", "XBRL", "4", "4", "4", "4", "4", "4", "S-8", "XBRL", "FWP", "XBRL", "4", "4", "4", "4", "DRS", "XBRL", "4", "4", "XBRL", "XBRL", "4", "4", "4", "4", "4", "XBRL", "424B3", "424B4", "EFFECT", "S-1/A", "CORRESP", "CORRESP", "CORRESP", "CORRESP", "4", "FWP", "S-1/A", "XBRL", "DEF 14A", "4", "CORRESP", "CORRESP", "FWP", "XBRL", "4", "4", "FWP", "XBRL", "FWP", "4", "XBRL", "4", "4", "4", "4", "4", "4", "4", "4", "XBRL", "XBRL", "4", "3", "4", "4", "XBRL", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "SC 13G", "SC 13G", "SC 13G", "4", "4", "CORRESP", "CORRESP", "XBRL", "XBRL", "FWP", "CORRESP", "CORRESP", "CORRESP", "CORRESP", "CORRESP", "CORRESP", "XBRL", "XBRL", "XBRL", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "LETTER", "4", "DRS", "4", "4", "4/A", "XBRL", "XBRL", "XBRL", "XBRL", "4", "4", "4", "4", "4", "XBRL", "4", "4", "XBRL", "XBRL", "SC 13D", "4", "4", "4", "4", "4", "4", "4", "4", "4", "4", "S-8", "8-K", "424B4", "REDACTED EXHIBIT", "EFFECT", "3", "3", "3", "3", "3", "3", "3", "3", "3", "3", "CERT", "8-A12B", "CORRESP", "CORRESP", "CORRESP", "LETTER", "S-1/A", "CORRESP", "LETTER", "FWP", "S-1/A", "S-1/A", "CORRESP", "CORRESP", "LETTER", "CORRESP", "S-1/A", "LETTER", "S-1/A", "CORRESP", "S-1/A", "REDACTED EXHIBIT", "LETTER", "CORRESP", "S-1", "LETTER", "DRSLTR", "DRS/A", "LETTER", "DRSLTR", "DRS/A", "LETTER", "DRS"], "size": [13028, 7508, 254903, 25592910, 984346, 766609, 1481927, 5358, 5285, 5292, 6921, 7506, 6842288, 999899, 29230689, 5442, 5167, 5158, 5155, 5155, 5188, 9727827, 3695687, 3141391, 115658, 3699451, 917187, 259764, 29408, 4693561, 14483272, 234374, 6932, 7515, 8011, 7439, 12159, 41948, 236669, 6944, 7520, 1153366, 1159577, 6926, 5297, 5370, 5304, 805006, 10417258, 36950, 42806, 5166, 5169, 5166, 5454, 5178, 5199, 172537, 1770862, 241541, 7529, 6946, 834109, 6929294, 13038, 100793146, 34166, 251064, 6942, 6954, 6954, 7531, 756775, 51619, 32659, 31587, 5178, 5453, 5169, 5166, 5169, 5199, 15679759, 6571390, 243741, 5448, 5163, 5172, 5160, 5160, 5193, 250050, 11626521, 2073271, 154448, 173190, 19948, 5988548, 12748513, 5293, 5300, 5366, 6976, 7565, 812928, 76096, 2075919, 5450, 5163, 5172, 5163, 5160, 5193, 146556, 702476, 172902, 18899, 10611866, 12698, 6729, 7563, 6979, 6974, 6976, 587541, 296736, 272364, 257460, 173896, 1643940, 416402, 168767, 5296, 5369, 5303, 4257, 178895, 5454, 5166, 5166, 5169, 5178, 5199, 171672, 14625, 4283, 729018, 1583766, 278507, 228532, 17913, 177597, 182645, 6525887, 200058, 5116, 5083, 5086, 5086, 5095, 5374, 165867, 452385, 900609, 1692, 6687, 40209, 1162601, 6451668, 182221, 7370, 11549648, 167334, 166954, 5090, 5081, 5081, 5078, 5111, 5369, 85638, 164960, 11202307, 14290995, 5213, 5128, 5131, 5137, 8494095, 197445, 4970, 4964, 5946658, 248441, 6053, 4375, 4330, 4325, 4614, 789246, 60122, 7314461, 1691, 7629184, 16400, 13554, 12395, 13283, 5240, 8596248, 8303481, 227747, 2244116, 4970, 16349, 12267, 3964772, 232427, 4959, 6940, 4454533, 232943, 9311048, 8029, 16974714, 5365, 5080, 5089, 5077, 5107, 5077, 8029, 5238, 12900664, 249919, 7379, 2696, 7298, 4970, 175671, 6908, 6918, 5208, 5169, 5175, 5166, 5248, 6905, 7265, 6021, 6905, 6916, 7997, 7350, 7996, 6790, 7997, 7348, 6876, 6019, 6319, 6442, 6388, 6970, 6320, 5880, 41549, 40075, 38493, 6020, 5206, 12950, 13602, 527997, 489236, 3937256, 16093, 13533, 12606, 13401, 16091, 13528, 25487760, 9702956, 174230, 6831, 4926, 6021, 5048, 5060, 5336, 5051, 5048, 5048, 7485, 6823, 7339, 40476, 7339, 9533353, 7336, 6483, 6334, 179302, 7637450, 242758, 1756791, 5181, 5254, 5175, 5172, 4288, 6154908, 4294, 6899, 7339617, 241640, 107076, 7689, 4601, 4662, 7244, 4671, 5609, 4667, 4599, 4600, 6312, 465242, 831353, 8442202, 275110, 1666, 15616, 6035, 7248, 6520, 24735, 15164, 7260, 7255, 8476, 24022, 560583, 21468, 54920, 13525, 13745, 43291, 8575264, 14448, 43243, 16433576, 8565041, 9378992, 15763, 91879, 43205, 13005, 10129096, 43142, 9022945, 18217, 7824460, 275933, 44048, 37150, 12317555, 50380, 108436, 11934867, 58098, 136667, 10499426, 87557, 6859720], "isXBRL": [0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 1, 1, 0, 1, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "isInlineXBRL": [0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 1, 1, 0, 1, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "primaryDocument": ["xslFormDX01/primary_doc.xml", "xslF345X05/form4.xml", "form8-k.htm", "form8-k.htm", "form8-k.htm", "form8-k.htm", "form8-k.htm", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "maia-20250331.htm", "form8-k.htm", "form8-k.htm", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "ny20041469x3_defa14a.htm", "ny20041469x2_def14a.htm", "form8-k.htm", "forms-8.htm", "ny20041469x1_pre14a.htm", "form8-k.htm", "form8-k.htm", "form424b5.htm", "form8-k.htm", "maia-20241231.htm", "form8-k.htm", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4a.xml", "xslF345X05/form4a.xml", "xslFormDX01/primary_doc.xml", "xslSCHEDULE_13G_X01/primary_doc.xml", "form8-k.htm", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "form8-k.htm", "form8-k.htm", "xslSCHEDULE_13G_X01/primary_doc.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "form8-k.htm", "form8-k.htm", "filename1.htm", "filename1.pdf", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "d666778d8k.htm", "d698846d424b5.htm", "form8-k.htm", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "d899097d8k.htm", "maia-20240930.htm", "xslFormDX01/primary_doc.xml", "form8-k.htm", "xslF345X05/form4.xml", "form8-k.htm", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "d902975d8k.htm", "d902674dsc13da.htm", "d903454dsc13d.htm", "d901744dsc13d.htm", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "form8-k.htm", "maia-20240630.htm", "form8-k.htm", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "form8-k.htm", "d844775d8k.htm", "d728441d8k.htm", "d640108d8k.htm", "d807098d8k.htm", "d836726d424b5.htm", "maia-20240331.htm", "form8-k.htm", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "d815817d8k.htm", "d752532ds8.htm", "ny20019222x1_def14a.htm", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "d818590d8k.htm", "d776139d8k.htm", "d802381d8k.htm", "d802379d424b5.htm", "maia-20231231.htm", "xslFormDX01/primary_doc.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "d778616d8k.htm", "form8-k.htm", "form8-k.htm", "form8-k.htm", "d772027d8k.htm", "d770368d424b5.htm", "d756637d8k.htm", "d741606d8k.htm", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xsl144X01/primary_doc.xml", "d712719d8k.htm", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "d622268d8k.htm", "xslFormDX01/primary_doc.xml", "xslF345X02/ownership.xml", "d614732d8k.htm", "d600722d424b5.htm", "d595363d8k.htm", "d882839d8k.htm", "d930511d424b5.htm", "d596404d8k.htm", "d578280d8k.htm", "maia-20230930.htm", "d570319d8k.htm", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "xslF345X05/form4.xml", "d546973d8k.htm", "d482665d8k.htm", "d868753d424b5.htm", "xslEFFECTX01/primary_doc.xml", "filename1.htm", "filename1.pdf", "d764599ds3.htm", "maia-20230630.htm", "d514056d8k.htm", "d538490drw.htm", "d529616dfwp.htm", "d499735d8k.htm", "d527457d8k.htm", "xslF345X04/form4.xml", "xslF345X04/form4.xml", "xslF345X04/form4.xml", "xslF345X04/form4.xml", "xslF345X04/form4.xml", "xslF345X04/form4.xml", "d491537ds8.htm", "d514991d8k.htm", "d520174dfwp.htm", "d476138ds1.htm", "xslF345X04/form4.xml", "xslF345X04/form4.xml", "xslF345X04/form4.xml", "xslF345X04/form4.xml", "filename1.htm", "maia-8k_20230525.htm", "xslF345X04/form4.xml", "xslF345X04/form4.xml", "maia-20230331.htm", "maia-8k_20230508.htm", "xslF345X04/form4.xml", "xslF345X04/form4.xml", "xslF345X04/form4.xml", "xslF345X04/form4.xml", "xslF345X04/form4.xml", "maia-8k_20230424.htm", "maia-424b3.htm", "maia-424b4.htm", "xslEFFECTX01/primary_doc.xml", "maia-s1a.htm", "filename1.htm", "filename1.htm", "filename1.htm", "filename1.htm", "xslF345X04/form4.xml", "maia-fwp.htm", "maia-s1a.htm", "maia-8k_20230420.htm", "ny20007569x1_def14a.htm", "xslF345X04/form4.xml", "filename1.htm", "filename1.htm", "maia-fwp.htm", "maia-8k_20230418.htm", "xslF345X04/form4.xml", "xslF345X04/form4.xml", "maia-fwp.htm", "maia-8k_20230411.htm", "maia-fwp.htm", "xslF345X04/form4.xml", "maia-s1a.htm", "xslF345X04/form4.xml", "xslF345X04/form4.xml", "xslF345X04/form4.xml", "xslF345X04/form4.xml", "xslF345X04/form4.xml", "xslF345X04/form4.xml", "xslF345X04/form4.xml", "xslF345X04/form4.xml", "maia-20221231.htm", "maia-8k_20230324.htm", "xslF345X04/form4.xml", "xslF345X02/form3.xml", "xslF345X04/form4.xml", "xslF345X04/form4.xml", "maia-8k_20230313.htm", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "d266595dsc13g.htm", "d424131dsc13g.htm", "d435114dsc13g.htm", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "filename1.htm", "filename1.htm", "maia-8ka_20230213.htm", "maia-8k_20230213.htm", "maia-fwp.htm", "filename1.htm", "filename1.htm", "filename1.htm", "filename1.htm", "filename1.htm", "filename1.htm", "maia-s1.htm", "maia-20220930.htm", "maia-20230203.htm", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "filename1.pdf", "xslF345X03/form4.xml", "filename1.htm", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4a.xml", "maia-8k_20221121.htm", "maia-20220930.htm", "maia-8k_20221109.htm", "maia-8k_20220916.htm", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "xslF345X03/form4.xml", "maia-8k_20220907.htm", "xslF345X03/form4.xml", "xslF345X03/doc4.xml", "maia-20220630.htm", "maia-8k_20220822.htm", "none-sc13d.htm", "xslF345X03/doc4.xml", "xslF345X03/doc4.xml", "xslF345X03/doc4.xml", "xslF345X03/doc4.xml", "xslF345X03/doc4.xml", "xslF345X03/doc4.xml", "xslF345X03/doc4.xml", "xslF345X03/doc4.xml", "xslF345X03/doc4.xml", "xslF345X03/doc4.xml", "maia-s8.htm", "maia-8k_20220727.htm", "maia-424b4.htm", "filename1.pdf", "xslEFFECTX01/primary_doc.xml", "xslF345X02/doc3.xml", "xslF345X02/doc3.xml", "xslF345X02/doc3.xml", "xslF345X02/doc3.xml", "xslF345X02/doc3.xml", "xslF345X02/doc3.xml", "xslF345X02/doc3.xml", "xslF345X02/doc3.xml", "xslF345X02/doc3.xml", "xslF345X02/doc3.xml", "MAIA072722.pdf", "maia-8k12b_20220727.htm", "filename1.htm", "filename1.htm", "filename1.htm", "filename1.pdf", "maia-s1a.htm", "filename1.htm", "filename1.pdf", "maia-fwp.htm", "maia-s1a.htm", "maia-s1a.htm", "filename1.htm", "filename1.htm", "filename1.pdf", "filename1.htm", "maia-s1a.htm", "filename1.pdf", "maia-s1a.htm", "filename1.htm", "maia-s1a.htm", "filename1.pdf", "filename1.pdf", "filename1.htm", "maia-s1.htm", "filename1.pdf", "filename1.htm", "filename1.htm", "filename1.pdf", "filename1.htm", "filename1.htm", "filename1.pdf", "filename1.htm"], "primaryDocDescription": ["", "PRIMARY DOCUMENT", "8-K", "8-K", "8-K", "8-K", "8-K", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "10-Q", "8-K", "", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "DEFA14A", "DEF 14A", "", "", "PRE 14A", "", "", "", "", "10-K", "", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "", "", "", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "", "", "", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "", "", "", "", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "8-K", "424B5", "", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "8-K", "10-Q", "", "", "PRIMARY DOCUMENT", "", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "8-K", "SC 13D/A", "SC 13D", "SC 13D", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "", "10-Q", "", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "", "8-K", "8-K", "8-K", "8-K", "424B5", "10-Q", "", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "8-K", "S-8", "DEF 14A", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "8-K", "8-K", "8-K", "424B5", "10-K", "", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "8-K", "", "", "", "8-K", "424B5", "8-K", "8-K", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "", "8-K", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "8-K", "", "3", "8-K", "424B5", "8-K", "8-K", "424B5", "8-K", "8-K", "10-Q", "8-K", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "8-K", "8-K", "424B5", "", "", "", "S-3", "10-Q", "8-K", "RW", "FWP", "8-K", "8-K", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "S-8", "8-K", "FWP", "S-1", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "", "8-K", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "10-Q", "8-K", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "8-K", "424B3", "424B4", "", "S-1/A", "", "", "", "", "PRIMARY DOCUMENT", "FWP", "S-1/A", "8-K", "DEF 14A", "PRIMARY DOCUMENT", "", "", "FWP", "8-K", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "FWP", "8-K", "FWP", "PRIMARY DOCUMENT", "S-1/A", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "10-K", "8-K", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "8-K", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "SC 13G", "SC 13G", "SC 13G", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "", "", "8-K/A", "8-K", "FWP", "", "", "", "", "", "", "S-1", "10-Q/A", "8-K", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "", "PRIMARY DOCUMENT", "", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "8-K", "10-Q", "8-K", "8-K", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "8-K", "PRIMARY DOCUMENT", "FORM 4 SUBMISSION", "10-Q", "8-K", "SC 13D", "FORM 4 SUBMISSION", "FORM 4 SUBMISSION", "FORM 4 SUBMISSION", "FORM 4 SUBMISSION", "FORM 4 SUBMISSION", "FORM 4 SUBMISSION", "FORM 4 SUBMISSION", "FORM 4 SUBMISSION", "FORM 4 SUBMISSION", "FORM 4 SUBMISSION", "S-8", "8-K", "424B4", "", "", "FORM 3 SUBMISSION", "FORM 3 SUBMISSION", "FORM 3 SUBMISSION", "FORM 3 SUBMISSION", "FORM 3 SUBMISSION", "FORM 3 SUBMISSION", "FORM 3 SUBMISSION", "FORM 3 SUBMISSION", "FORM 3 SUBMISSION", "FORM 3 SUBMISSION", "NYSE AMERICAN CERTIFICATION", "8-A12B", "", "", "", "", "S-1/A", "", "", "FWP", "S-1/A", "S-1/A", "", "", "", "", "S-1/A", "", "S-1/A", "", "S-1/A", "", "", "", "S-1", "", "", "", "", "", "", "", ""]}, "files": []}}