{"cik": "0001172862", "entityType": "other", "sic": "", "sicDescription": "", "ownerOrg": "", "insiderTransactionForOwnerExists": 0, "insiderTransactionForIssuerExists": 0, "name": "SM Investors Offshore, Ltd.", "tickers": [], "exchanges": [], "ein": "000000000", "lei": null, "description": "", "website": "", "investorWebsite": "", "category": "", "fiscalYearEnd": "1231", "stateOfIncorporation": "E9", "stateOfIncorporationDescription": "Cayman Islands", "addresses": {"mailing": {"street1": "ELIAN FIDUCIARY SERVICES (CAYMAN) LTD.", "street2": "89 NEXUS WAY, CAMANA BAY", "city": "GRAND CAYMAN", "stateOrCountry": "E9", "zipCode": "KY1-9007", "stateOrCountryDescription": "Cayman Islands", "isForeignLocation": 0, "foreignStateTerritory": null, "country": null, "countryCode": null}, "business": {"street1": "ELIAN FIDUCIARY SERVICES (CAYMAN) LTD.", "street2": "89 NEXUS WAY, CAMANA BAY", "city": "GRAND CAYMAN", "stateOrCountry": "E9", "zipCode": "KY1-9007", "stateOrCountryDescription": "Cayman Islands", "isForeignLocation": null, "foreignStateTerritory": null, "country": null, "countryCode": null}}, "phone": "************", "flags": "", "formerNames": [{"name": "SM INVESTORS OFFSHORE LTD", "from": "2002-05-07T00:00:00.000Z", "to": "2008-06-25T00:00:00.000Z"}], "filings": {"recent": {"accessionNumber": ["0001636587-25-000023", "0001636587-24-000015", "0000919574-23-003665", "0000919574-23-003474", "0000919574-22-003682", "0000919574-21-003893", "0000919574-20-003967", "0000919574-19-004026", "0000919574-18-004103", "0000919574-17-004786", "0000919574-16-013614", "0000919574-15-004839", "0000919574-14-003683", "0000919574-13-003856", "0000919574-12-004043", "0000919574-11-003884", "0000919574-09-012369", "9999999997-08-029999", "9999999997-02-027811"], "filingDate": ["2025-05-19", "2024-05-14", "2023-06-09", "2023-05-25", "2022-06-01", "2021-06-02", "2020-06-05", "2019-06-07", "2018-06-07", "2017-06-09", "2016-06-10", "2015-06-10", "2014-06-10", "2013-06-19", "2012-06-20", "2011-06-20", "2009-06-22", "2008-06-25", "2002-05-07"], "reportDate": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "acceptanceDateTime": ["2025-05-19T10:26:41.000Z", "2024-05-14T15:45:27.000Z", "2023-06-09T11:27:58.000Z", "2023-05-25T13:36:55.000Z", "2022-06-01T13:42:13.000Z", "2021-06-02T12:22:22.000Z", "2020-06-05T10:53:48.000Z", "2019-06-07T15:02:27.000Z", "2018-06-07T17:16:02.000Z", "2017-06-09T12:04:01.000Z", "2016-06-10T14:55:42.000Z", "2015-06-10T16:47:11.000Z", "2014-06-10T14:40:45.000Z", "2013-06-19T11:29:31.000Z", "2012-06-20T14:22:21.000Z", "2011-06-20T15:54:36.000Z", "2009-06-22T10:51:34.000Z", "2008-06-26T16:03:55.000Z", "2002-05-10T14:54:40.000Z"], "act": ["33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "34", ""], "form": ["D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "REGDEX/A", "REGDEX"], "fileNumber": ["021-43153", "021-43153", "021-43153", "021-43153", "021-43153", "021-43153", "021-43153", "021-43153", "021-43153", "021-43153", "021-43153", "021-43153", "021-43153", "021-43153", "021-43153", "021-43153", "021-43153", "021-43153", "021-43153"], "filmNumber": ["25962083", "24943903", "231004302", "23959052", "22986858", "21988348", "20944973", "19885299", "18887347", "17902024", "161708401", "15923876", "14901750", "13921164", "12917082", "11921054", "09902630", "08053782", "02032257"], "items": ["06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06,3C,3C.1", "06,3C,3C.1", "06,3C,3C.1", "06,3C,3C.1", "06", "06"], "core_type": ["D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "REGDEX/A", "REGDEX"], "size": [8493, 9327, 9070, 9070, 9069, 9069, 9069, 9068, 9068, 9112, 9113, 9051, 8272, 8317, 8333, 8333, 9444, 1533, 1405], "isXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "isInlineXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "primaryDocument": ["xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "9999999997-08-029999.paper", "9999999997-02-027811.paper"], "primaryDocDescription": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "AUTO-GENERATED PAPER DOCUMENT", "AUTO-GENERATED PAPER DOCUMENT"]}, "files": []}}