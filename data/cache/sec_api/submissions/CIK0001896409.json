{"cik": "**********", "entityType": "other", "sic": "", "sicDescription": "", "ownerOrg": "", "insiderTransactionForOwnerExists": 0, "insiderTransactionForIssuerExists": 0, "name": "Nationwide PPVUL Separate Account - AC1", "tickers": [], "exchanges": [], "ein": "*********", "lei": null, "description": "", "website": "", "investorWebsite": "", "category": "", "fiscalYearEnd": "1231", "stateOfIncorporation": "OH", "stateOfIncorporationDescription": "OH", "addresses": {"mailing": {"street1": "ONE NATIONWIDE PLAZA", "street2": null, "city": "COLUMBUS", "stateOrCountry": "OH", "zipCode": "43215", "stateOrCountryDescription": "OH", "isForeignLocation": 0, "foreignStateTerritory": null, "country": null, "countryCode": null}, "business": {"street1": "ONE NATIONWIDE PLAZA", "street2": null, "city": "COLUMBUS", "stateOrCountry": "OH", "zipCode": "43215", "stateOrCountryDescription": "OH", "isForeignLocation": null, "foreignStateTerritory": null, "country": null, "countryCode": null}}, "phone": "************", "flags": "", "formerNames": [], "filings": {"recent": {"accessionNumber": ["**********-25-000006", "**********-25-000005", "**********-25-000003", "**********-25-000002", "**********-25-000001", "**********-24-000012", "**********-24-000011", "**********-24-000010", "**********-24-000009", "**********-24-000008", "**********-24-000007", "**********-24-000006", "**********-24-000005", "**********-24-000004", "**********-24-000003", "**********-24-000002", "**********-24-000001", "**********-23-000003", "**********-23-000002", "**********-23-000001", "0001135856-23-000063", "0001135856-23-000055", "0001804211-23-000004", "0001135856-23-000043", "0001135856-23-000035", "0001804435-23-000003", "0001135856-23-000023", "0001135856-23-000015", "0001135856-23-000007", "**********-22-000007", "0001804211-22-000010", "0001135856-22-000032", "0001135856-22-000025", "0001816695-22-000007", "0001135856-22-000013", "**********-22-000006", "**********-22-000005", "**********-22-000004", "**********-22-000003", "**********-22-000002", "**********-22-000001", "**********-21-000002", "**********-21-000001"], "filingDate": ["2025-05-19", "2025-04-18", "2025-03-19", "2025-02-19", "2025-01-15", "2024-12-26", "2024-11-20", "2024-10-15", "2024-09-18", "2024-08-16", "2024-07-19", "2024-06-18", "2024-05-17", "2024-04-17", "2024-03-20", "2024-02-16", "2024-01-17", "2023-12-19", "2023-11-16", "2023-10-25", "2023-09-19", "2023-08-22", "2023-07-21", "2023-06-20", "2023-05-22", "2023-04-21", "2023-03-20", "2023-02-23", "2023-01-23", "2022-12-16", "2022-11-16", "2022-10-31", "2022-09-21", "2022-08-22", "2022-07-19", "2022-06-27", "2022-05-27", "2022-04-25", "2022-03-22", "2022-02-23", "2022-01-25", "2021-12-21", "2021-12-01"], "reportDate": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "acceptanceDateTime": ["2025-05-19T12:37:32.000Z", "2025-04-18T13:32:14.000Z", "2025-03-19T14:27:29.000Z", "2025-02-19T21:11:48.000Z", "2025-01-15T21:34:16.000Z", "2024-12-26T14:42:46.000Z", "2024-11-20T21:30:09.000Z", "2024-10-15T18:35:13.000Z", "2024-09-18T19:45:07.000Z", "2024-08-16T19:46:41.000Z", "2024-07-19T13:02:40.000Z", "2024-06-18T14:55:54.000Z", "2024-05-17T20:45:26.000Z", "2024-04-17T19:30:55.000Z", "2024-03-20T14:46:35.000Z", "2024-02-16T19:43:13.000Z", "2024-01-17T21:47:36.000Z", "2023-12-19T16:07:52.000Z", "2023-11-16T18:11:36.000Z", "2023-10-25T20:37:04.000Z", "2023-09-19T19:05:46.000Z", "2023-08-22T14:03:12.000Z", "2023-07-21T14:45:44.000Z", "2023-06-20T13:45:46.000Z", "2023-05-22T18:23:38.000Z", "2023-04-21T18:03:10.000Z", "2023-03-20T18:01:20.000Z", "2023-02-23T13:46:10.000Z", "2023-01-23T14:11:25.000Z", "2022-12-16T19:04:18.000Z", "2022-11-16T13:48:04.000Z", "2022-10-31T15:19:27.000Z", "2022-09-21T12:19:53.000Z", "2022-08-22T15:33:21.000Z", "2022-07-19T12:45:19.000Z", "2022-06-27T16:09:13.000Z", "2022-05-27T19:59:58.000Z", "2022-04-25T15:11:24.000Z", "2022-03-22T17:22:27.000Z", "2022-02-23T18:49:42.000Z", "2022-01-25T14:34:45.000Z", "2021-12-21T17:06:13.000Z", "2021-12-01T16:29:40.000Z"], "act": ["33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33"], "form": ["D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D"], "fileNumber": ["021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039", "021-423039"], "filmNumber": ["25962734", "25849327", "25751157", "25640114", "25533311", "241577313", "241480782", "241371379", "241307325", "241216537", "241126252", "241050516", "24960634", "24850668", "24766733", "24647617", "24539047", "231495969", "231413731", "231346478", "231263446", "231191284", "231101501", "231023756", "23943946", "23835838", "23745853", "23656162", "23542692", "221467574", "221393098", "221344571", "221255051", "221182742", "221090458", "221042810", "22977164", "22847955", "22758380", "22662474", "22551300", "211507990", "211461870"], "items": ["06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1", "06b,3C,3C.1"], "core_type": ["D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D"], "size": [8095, 8095, 8008, 8008, 8008, 8009, 8009, 8009, 8009, 8009, 7718, 7718, 7717, 7717, 7717, 7717, 7717, 7718, 7685, 7685, 7685, 7685, 7685, 7685, 7684, 7591, 7590, 7590, 7590, 7591, 7591, 7590, 7590, 7590, 7498, 7498, 7497, 7398, 7215, 7123, 7087, 7088, 6993], "isXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "isInlineXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "primaryDocument": ["xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml"], "primaryDocDescription": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]}, "files": []}}