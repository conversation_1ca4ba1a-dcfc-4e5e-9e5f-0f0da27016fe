{"cik": "**********", "entityType": "other", "sic": "", "sicDescription": "", "ownerOrg": "", "insiderTransactionForOwnerExists": 0, "insiderTransactionForIssuerExists": 0, "name": "Nationwide PPVUL Separate Account - 4", "tickers": [], "exchanges": [], "ein": "*********", "lei": null, "description": "", "website": "", "investorWebsite": "", "category": "", "fiscalYearEnd": "1231", "stateOfIncorporation": "OH", "stateOfIncorporationDescription": "OH", "addresses": {"mailing": {"street1": "ONE NATIONWIDE PLAZA", "street2": null, "city": "COLUMBUS", "stateOrCountry": "OH", "zipCode": "43215", "stateOrCountryDescription": "OH", "isForeignLocation": 0, "foreignStateTerritory": null, "country": null, "countryCode": null}, "business": {"street1": "ONE NATIONWIDE PLAZA", "street2": null, "city": "COLUMBUS", "stateOrCountry": "OH", "zipCode": "43215", "stateOrCountryDescription": "OH", "isForeignLocation": null, "foreignStateTerritory": null, "country": null, "countryCode": null}}, "phone": "************", "flags": "", "formerNames": [{"name": "Nationwide Private Placement Variable Account PP4", "from": "2020-02-26T00:00:00.000Z", "to": "2021-02-23T00:00:00.000Z"}], "filings": {"recent": {"accessionNumber": ["**********-25-000006", "**********-25-000005", "**********-25-000003", "**********-25-000002", "**********-25-000001", "**********-24-000012", "**********-24-000011", "**********-24-000010", "**********-24-000009", "**********-24-000008", "**********-24-000007", "**********-24-000006", "**********-24-000005", "**********-24-000004", "**********-24-000003", "**********-24-000002", "**********-24-000001", "**********-23-000007", "**********-23-000006", "**********-23-000005", "**********-23-000060", "**********-23-000053", "**********-23-000002", "**********-23-000041", "**********-23-000033", "**********-23-000001", "**********-23-000021", "**********-23-000013", "**********-23-000005", "**********-22-000008", "**********-22-000008", "**********-22-000030", "**********-22-000023", "**********-22-000018", "**********-22-000011", "**********-22-000006", "**********-22-000005", "**********-22-000004", "**********-22-000003", "**********-22-000002", "**********-22-000001", "**********-21-000012", "**********-21-000011", "**********-21-000010", "**********-21-000009", "**********-21-000008", "**********-21-000007", "**********-21-000006", "**********-21-000005", "**********-21-000004", "**********-21-000003", "**********-21-000003", "**********-21-000001", "**********-20-000010", "**********-20-000009", "**********-20-000008", "**********-20-000007", "**********-20-000006", "**********-20-000005", "**********-20-000004", "**********-20-000003", "**********-20-000002", "**********-20-000003", "**********-20-000001"], "filingDate": ["2025-05-19", "2025-04-18", "2025-03-19", "2025-02-19", "2025-01-15", "2024-12-26", "2024-11-20", "2024-10-15", "2024-09-18", "2024-08-16", "2024-07-19", "2024-06-18", "2024-05-17", "2024-04-17", "2024-03-20", "2024-02-16", "2024-01-17", "2023-12-19", "2023-11-16", "2023-10-25", "2023-09-19", "2023-08-22", "2023-07-21", "2023-06-20", "2023-05-22", "2023-04-21", "2023-03-20", "2023-02-23", "2023-01-23", "2022-12-16", "2022-11-16", "2022-10-31", "2022-09-21", "2022-08-22", "2022-07-19", "2022-06-27", "2022-05-27", "2022-04-25", "2022-03-22", "2022-02-23", "2022-01-25", "2021-12-21", "2021-11-22", "2021-10-27", "2021-09-17", "2021-08-26", "2021-07-23", "2021-06-24", "2021-05-26", "2021-04-21", "2021-03-25", "2021-02-23", "2021-01-19", "2020-12-18", "2020-11-20", "2020-10-21", "2020-09-18", "2020-08-24", "2020-07-17", "2020-06-22", "2020-05-21", "2020-04-20", "2020-03-20", "2020-02-26"], "reportDate": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "acceptanceDateTime": ["2025-05-19T12:41:59.000Z", "2025-04-18T17:20:53.000Z", "2025-03-19T14:31:40.000Z", "2025-02-19T21:16:53.000Z", "2025-01-15T21:42:13.000Z", "2024-12-26T14:50:51.000Z", "2024-11-20T21:34:12.000Z", "2024-10-15T18:39:21.000Z", "2024-09-18T19:49:49.000Z", "2024-08-16T20:02:03.000Z", "2024-07-19T13:07:26.000Z", "2024-06-18T15:08:49.000Z", "2024-05-17T20:49:49.000Z", "2024-04-17T19:36:58.000Z", "2024-03-20T14:51:22.000Z", "2024-02-16T19:48:07.000Z", "2024-01-17T21:53:33.000Z", "2023-12-19T16:14:56.000Z", "2023-11-16T18:04:34.000Z", "2023-10-25T19:28:33.000Z", "2023-09-19T18:41:14.000Z", "2023-08-22T13:52:23.000Z", "2023-07-21T14:26:35.000Z", "2023-06-20T13:32:12.000Z", "2023-05-22T18:12:18.000Z", "2023-04-21T17:49:03.000Z", "2023-03-20T17:04:19.000Z", "2023-02-23T13:37:00.000Z", "2023-01-23T14:01:33.000Z", "2022-12-16T18:19:36.000Z", "2022-11-16T13:33:36.000Z", "2022-10-31T15:05:10.000Z", "2022-09-21T12:04:54.000Z", "2022-08-22T13:03:24.000Z", "2022-07-19T12:22:52.000Z", "2022-06-27T16:21:03.000Z", "2022-05-27T19:45:56.000Z", "2022-04-25T15:00:26.000Z", "2022-03-22T17:07:16.000Z", "2022-02-23T18:37:54.000Z", "2022-01-25T14:06:59.000Z", "2021-12-21T16:55:54.000Z", "2021-11-22T18:39:21.000Z", "2021-10-27T17:29:25.000Z", "2021-09-17T14:31:20.000Z", "2021-08-26T20:36:57.000Z", "2021-07-23T13:12:20.000Z", "2021-06-24T15:40:03.000Z", "2021-05-26T15:18:06.000Z", "2021-04-21T17:02:59.000Z", "2021-03-25T19:56:35.000Z", "2021-02-23T15:38:17.000Z", "2021-01-19T16:19:00.000Z", "2020-12-18T16:54:38.000Z", "2020-11-20T20:18:04.000Z", "2020-10-21T19:33:11.000Z", "2020-09-18T15:04:20.000Z", "2020-08-24T13:53:57.000Z", "2020-07-17T15:33:46.000Z", "2020-06-22T18:18:10.000Z", "2020-05-21T12:17:21.000Z", "2020-04-20T19:48:15.000Z", "2020-03-20T17:46:23.000Z", "2020-02-26T17:29:39.000Z"], "act": ["33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33"], "form": ["D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D"], "fileNumber": ["021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381", "021-361381"], "filmNumber": ["25962743", "25849920", "25751166", "25640263", "25533406", "241577641", "241480871", "241371394", "241307352", "241216618", "241126300", "241050542", "24960726", "24850691", "24766768", "24647650", "24539098", "231496024", "231413703", "231345451", "231263369", "231191270", "231101383", "231023742", "23943919", "23835755", "23745583", "23656069", "23542651", "221467414", "221393057", "221344426", "221255035", "221182348", "221090439", "221042886", "22976777", "22847886", "22758304", "22662405", "22551246", "211507948", "211431365", "211352126", "211259451", "211212865", "211109229", "211041488", "21963849", "21840592", "21772240", "21663566", "21534591", "201398641", "201332127", "201250505", "201183017", "201125010", "201033125", "20978207", "20900033", "20802531", "20731355", "20654221"], "items": ["06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7"], "core_type": ["D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D"], "size": [7622, 7622, 7629, 7629, 7629, 7666, 7666, 7666, 7666, 7666, 7666, 7666, 7665, 7665, 7665, 7665, 7665, 7665, 7632, 7632, 7632, 7632, 7632, 7632, 7631, 7631, 7631, 7631, 7631, 7536, 7441, 7441, 7441, 7441, 7441, 7347, 7346, 7346, 7346, 7346, 7346, 7347, 7347, 7347, 7347, 7347, 7347, 7347, 7346, 7346, 7345, 7293, 7293, 7294, 7294, 7294, 7294, 7294, 7294, 7293, 7295, 7297, 7295, 7203], "isXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "isInlineXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "primaryDocument": ["xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml"], "primaryDocDescription": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]}, "files": []}}