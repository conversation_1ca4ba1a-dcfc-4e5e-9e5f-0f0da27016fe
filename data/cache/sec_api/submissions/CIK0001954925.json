{"cik": "0001954925", "entityType": "other", "sic": "", "sicDescription": "", "ownerOrg": "", "insiderTransactionForOwnerExists": 0, "insiderTransactionForIssuerExists": 0, "name": "P/E Capital DAO LLC", "tickers": [], "exchanges": [], "ein": "920518560", "lei": null, "description": "", "website": "", "investorWebsite": "", "category": "", "fiscalYearEnd": "1231", "stateOfIncorporation": "WY", "stateOfIncorporationDescription": "WY", "addresses": {"mailing": {"street1": "680 S CACHE STREET", "street2": "SUITE 100-7414", "city": "JACKSON", "stateOrCountry": "WY", "zipCode": "83001", "stateOrCountryDescription": "WY", "isForeignLocation": 0, "foreignStateTerritory": null, "country": null, "countryCode": null}, "business": {"street1": "680 S CACHE STREET", "street2": "SUITE 100-7414", "city": "JACKSON", "stateOrCountry": "WY", "zipCode": "83001", "stateOrCountryDescription": "WY", "isForeignLocation": null, "foreignStateTerritory": null, "country": null, "countryCode": null}}, "phone": "************", "flags": "", "formerNames": [], "filings": {"recent": {"accessionNumber": ["0001954925-25-000005", "0001954925-25-000003", "0001954925-25-000002", "0001954925-25-000001", "0001954925-24-000006", "0001954925-24-000005", "0001954925-24-000004", "0001954925-24-000002", "0001954925-24-000001", "0001954925-23-000079", "0001954925-23-000078", "0001954925-23-000077", "0001954925-23-000076", "0001954925-23-000075", "0001954925-23-000074", "0001954925-23-000073", "0001954925-23-000072", "0001954925-23-000071", "0001954925-23-000070", "0001954925-23-000069", "0001954925-23-000068", "0001954925-23-000067", "0001954925-23-000066", "0001954925-23-000065", "0001954925-23-000064", "0001954925-23-000063", "0001954925-23-000062", "0001954925-23-000061", "0001954925-23-000060", "0001954925-23-000059", "0001954925-23-000058", "0001954925-23-000057", "0001954925-23-000056", "0001954925-23-000055", "0001954925-23-000054", "0001954925-23-000053", "0001954925-23-000052", "0001954925-23-000051", "0001954925-23-000050", "0001954925-23-000049", "0001954925-23-000048", "0001954925-23-000047", "0001954925-23-000046", "0001954925-23-000045", "0001954925-23-000044", "0001954925-23-000043", "0001954925-23-000042", "0001954925-23-000041", "0001954925-23-000040", "0001954925-23-000039", "0001954925-23-000038", "0001954925-23-000037", "0001954925-23-000036", "0001954925-23-000035", "0001954925-23-000034", "0001954925-23-000033", "0001954925-23-000032", "0001954925-23-000031", "0001954925-23-000030", "0001954925-23-000029", "0001954925-23-000028", "0001954925-23-000027", "0001954925-23-000026", "0001954925-23-000025", "0001954925-23-000024", "0001954925-23-000023", "0001954925-23-000022", "0001954925-23-000021", "0001954925-23-000020", "0001954925-23-000019", "0001954925-23-000018", "0001954925-23-000016", "0001954925-23-000015", "0001954925-23-000014", "0001954925-23-000013", "0001954925-23-000012", "0001954925-23-000011", "0001954925-23-000010", "0001954925-23-000009", "0001954925-23-000008", "0001954925-23-000007", "0001954925-23-000006", "0001954925-23-000005", "0001954925-23-000004", "0001954925-23-000003", "0001954925-23-000002", "0001954925-23-000001", "0001954925-22-000002", "0001954925-22-000001"], "filingDate": ["2025-05-19", "2025-04-04", "2025-03-03", "2025-02-11", "2024-12-18", "2024-12-10", "2024-12-10", "2024-04-05", "2024-04-05", "2023-09-26", "2023-09-26", "2023-09-26", "2023-09-26", "2023-09-26", "2023-09-26", "2023-09-22", "2023-09-22", "2023-09-22", "2023-09-21", "2023-09-21", "2023-09-21", "2023-09-21", "2023-09-21", "2023-09-21", "2023-09-21", "2023-09-21", "2023-09-20", "2023-09-20", "2023-09-20", "2023-09-20", "2023-09-20", "2023-09-20", "2023-09-20", "2023-09-20", "2023-09-20", "2023-09-20", "2023-09-20", "2023-09-20", "2023-09-20", "2023-09-19", "2023-09-19", "2023-09-19", "2023-09-19", "2023-09-19", "2023-09-19", "2023-09-19", "2023-09-19", "2023-09-19", "2023-09-19", "2023-09-18", "2023-09-18", "2023-09-18", "2023-09-18", "2023-09-18", "2023-09-18", "2023-09-18", "2023-09-18", "2023-09-18", "2023-09-18", "2023-04-17", "2023-04-14", "2023-04-14", "2023-04-14", "2023-04-13", "2023-04-13", "2023-04-13", "2023-04-13", "2023-04-13", "2023-04-13", "2023-04-12", "2023-04-12", "2023-01-19", "2023-01-19", "2023-01-19", "2023-01-19", "2023-01-19", "2023-01-19", "2023-01-19", "2023-01-19", "2023-01-19", "2023-01-19", "2023-01-19", "2023-01-19", "2023-01-19", "2023-01-17", "2023-01-17", "2023-01-17", "2022-12-01", "2022-12-01"], "reportDate": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "acceptanceDateTime": ["2025-05-16T21:49:36.000Z", "2025-04-04T14:18:34.000Z", "2025-03-03T13:15:17.000Z", "2025-02-11T14:34:47.000Z", "2024-12-18T16:51:23.000Z", "2024-12-10T15:48:44.000Z", "2024-12-10T15:40:45.000Z", "2024-04-05T14:44:27.000Z", "2024-04-05T14:37:30.000Z", "2023-09-26T12:22:34.000Z", "2023-09-26T12:16:31.000Z", "2023-09-26T12:11:06.000Z", "2023-09-26T12:00:48.000Z", "2023-09-26T11:54:39.000Z", "2023-09-26T11:46:02.000Z", "2023-09-22T12:13:28.000Z", "2023-09-22T12:06:26.000Z", "2023-09-22T12:00:38.000Z", "2023-09-21T12:48:48.000Z", "2023-09-21T12:44:07.000Z", "2023-09-21T12:39:49.000Z", "2023-09-21T12:30:52.000Z", "2023-09-21T12:27:04.000Z", "2023-09-21T12:22:19.000Z", "2023-09-21T12:18:20.000Z", "2023-09-21T12:13:32.000Z", "2023-09-20T13:47:00.000Z", "2023-09-20T13:32:50.000Z", "2023-09-20T13:27:29.000Z", "2023-09-20T13:23:00.000Z", "2023-09-20T12:51:39.000Z", "2023-09-20T12:00:26.000Z", "2023-09-20T11:55:16.000Z", "2023-09-20T11:46:12.000Z", "2023-09-20T11:43:59.000Z", "2023-09-20T11:35:00.000Z", "2023-09-20T11:30:08.000Z", "2023-09-20T10:54:59.000Z", "2023-09-20T10:50:07.000Z", "2023-09-19T12:27:18.000Z", "2023-09-19T12:21:13.000Z", "2023-09-19T12:15:45.000Z", "2023-09-19T12:07:57.000Z", "2023-09-19T12:03:26.000Z", "2023-09-19T11:58:09.000Z", "2023-09-19T11:52:35.000Z", "2023-09-19T11:47:23.000Z", "2023-09-19T11:41:26.000Z", "2023-09-19T11:35:12.000Z", "2023-09-18T13:57:11.000Z", "2023-09-18T13:50:33.000Z", "2023-09-18T13:45:47.000Z", "2023-09-18T13:40:21.000Z", "2023-09-18T13:35:15.000Z", "2023-09-18T13:28:21.000Z", "2023-09-18T13:18:50.000Z", "2023-09-18T13:08:33.000Z", "2023-09-18T13:04:22.000Z", "2023-09-18T12:53:41.000Z", "2023-04-17T15:10:14.000Z", "2023-04-14T07:07:48.000Z", "2023-04-14T07:02:30.000Z", "2023-04-14T06:59:11.000Z", "2023-04-13T08:04:38.000Z", "2023-04-13T08:00:57.000Z", "2023-04-13T07:48:25.000Z", "2023-04-13T07:40:50.000Z", "2023-04-13T07:34:33.000Z", "2023-04-13T07:28:41.000Z", "2023-04-12T15:21:45.000Z", "2023-04-12T14:40:50.000Z", "2023-01-19T13:30:17.000Z", "2023-01-19T13:18:20.000Z", "2023-01-19T13:04:24.000Z", "2023-01-19T12:38:11.000Z", "2023-01-19T12:36:34.000Z", "2023-01-19T12:34:48.000Z", "2023-01-19T12:32:57.000Z", "2023-01-19T12:30:41.000Z", "2023-01-19T12:15:44.000Z", "2023-01-19T12:04:54.000Z", "2023-01-19T11:56:38.000Z", "2023-01-19T11:49:55.000Z", "2023-01-19T11:41:24.000Z", "2023-01-17T13:03:22.000Z", "2023-01-17T12:57:15.000Z", "2023-01-17T12:24:38.000Z", "2022-12-01T11:39:18.000Z", "2022-12-01T10:30:57.000Z"], "act": ["33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33"], "form": ["D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D", "D", "D/A", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D/A", "D", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D", "D", "D", "D", "D/A", "D/A", "D", "D/A", "D"], "fileNumber": ["021-492329", "021-471148", "021-470866", "021-492332", "021-466244", "021-466244", "021-470866", "021-470866", "021-470866", "021-493022", "021-493021", "021-493020", "021-493019", "021-493019", "021-493018", "021-492797", "021-492791", "021-492789", "021-492690", "021-492689", "021-492688", "021-492685", "021-492684", "021-492680", "021-492676", "021-492671", "021-492580", "021-492576", "021-492574", "021-492572", "021-492568", "021-492561", "021-492560", "021-492557", "021-492556", "021-492555", "021-492553", "021-492547", "021-492546", "021-492440", "021-492439", "021-492438", "021-492435", "021-492434", "021-492433", "021-492431", "021-492430", "021-492428", "021-492426", "021-492347", "021-492346", "021-492342", "021-492340", "021-492339", "021-492337", "021-492335", "021-492332", "021-492332", "021-492329", "021-479096", "021-470866", "021-471180", "021-471166", "021-471152", "021-471149", "021-471156", "021-471154", "021-471152", "021-471149", "021-466244", "021-471148", "021-471180", "021-471166", "021-471166", "021-471156", "021-471154", "021-471152", "021-471149", "021-471148", "021-471156", "021-471154", "021-471152", "021-471149", "021-471148", "021-470866", "021-466244", "021-470866", "021-466244", "021-466244"], "filmNumber": ["25961367", "25813060", "25696268", "25609147", "241559896", "241538339", "241538302", "24826152", "24826139", "231279087", "231279073", "231279049", "231278778", "231278660", "231278618", "231270906", "231270842", "231270825", "231268547", "231268539", "231268535", "231268516", "231268511", "231268498", "231268487", "231268466", "231266125", "231266064", "231266049", "231266034", "231265931", "231265815", "231265808", "231265780", "231265776", "231265761", "231265754", "231265602", "231265594", "231263021", "231263010", "231262997", "231262978", "231262971", "231262960", "231262940", "231262925", "231262915", "231262901", "231260876", "231260871", "231260864", "231260849", "231260844", "231260822", "231260805", "231260773", "231260693", "231260662", "23823732", "23819707", "23819700", "23819687", "23817098", "23817085", "23817066", "23817061", "23817049", "23817045", "23815520", "23815414", "23536889", "23536836", "23536796", "23536702", "23536699", "23536697", "23536694", "23536693", "23536651", "23536610", "23536543", "23536493", "23536441", "23531015", "23531000", "23530904", "221437921", "221437768"], "items": ["04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.2", "04.3,06c", "04.3,06c", "04.3,06c", "04.3,06c", "04.3,06c", "04.3,06c", "04.3,06c", "04.3,06c", "04.2,06c", "04.2,06c", "04.2,06c", "04.2,06c", "04.2,06c", "04.3,06c", "04.2", "04.3,06c", "04.2", "04.2"], "core_type": ["D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D", "D", "D/A", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D/A", "D", "D", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D/A", "D", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D", "D", "D", "D", "D/A", "D/A", "D", "D/A", "D"], "size": [7967, 8003, 7988, 7864, 8022, 8023, 7986, 7965, 7965, 7989, 7983, 7982, 8075, 8001, 7970, 8001, 7998, 7997, 7943, 7937, 7938, 7929, 7930, 7923, 7932, 7925, 7928, 7942, 7939, 7884, 7928, 7936, 7941, 7921, 7936, 7917, 7921, 7894, 7894, 7873, 7909, 7911, 7904, 7856, 7844, 7895, 7914, 7899, 7900, 7926, 7911, 7901, 7900, 7913, 7900, 7923, 8010, 7914, 7891, 7509, 7757, 7657, 7674, 7719, 7710, 7779, 7777, 7718, 7709, 7743, 7713, 7769, 7881, 7749, 7985, 7983, 7925, 7916, 7919, 7889, 7887, 7829, 7817, 7820, 7857, 7452, 7605, 7199, 7095], "isXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "isInlineXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "primaryDocument": ["xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml"], "primaryDocDescription": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]}, "files": []}}