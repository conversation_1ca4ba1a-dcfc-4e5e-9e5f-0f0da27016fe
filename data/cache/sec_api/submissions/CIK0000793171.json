{"cik": "0000793171", "entityType": "operating", "sic": "2836", "sicDescription": "Biological Products, (No Diagnostic Substances)", "ownerOrg": "03 Life Sciences", "insiderTransactionForOwnerExists": 0, "insiderTransactionForIssuerExists": 1, "name": "Vitro Biopharma, Inc.", "tickers": [], "exchanges": [], "ein": "*********", "lei": null, "description": "", "website": "", "investorWebsite": "", "category": "Non-accelerated filer<br>Smaller reporting company", "fiscalYearEnd": "1031", "stateOfIncorporation": "NV", "stateOfIncorporationDescription": "NV", "addresses": {"mailing": {"street1": "3200 CHERRY CREEK DRIVE SOUTH", "street2": "SUITE 410", "city": "DENVER", "stateOrCountry": "CO", "zipCode": "80209", "stateOrCountryDescription": "CO", "isForeignLocation": 0, "foreignStateTerritory": null, "country": null, "countryCode": null}, "business": {"street1": "3200 CHERRY CREEK DRIVE SOUTH", "street2": "SUITE 410", "city": "DENVER", "stateOrCountry": "CO", "zipCode": "80209", "stateOrCountryDescription": "CO", "isForeignLocation": null, "foreignStateTerritory": null, "country": null, "countryCode": null}}, "phone": "8558487627", "flags": "", "formerNames": [{"name": "VITRO DIAGNOSTICS INC", "from": "1996-12-13T00:00:00.000Z", "to": "2021-10-20T00:00:00.000Z"}], "filings": {"recent": {"accessionNumber": ["0001641172-25-014683", "0001493152-25-011003", "0001493152-25-010585", "0001493152-25-003942", "0001493152-24-036326", "9999999995-24-002788", "0001493152-24-035690", "0001493152-24-035688", "0001493152-24-035112", "0001493152-24-034388", "0000000000-24-009860", "0001493152-24-033733", "0001493152-24-033726", "0000000000-24-009520", "0001493152-24-030729", "0001493152-24-030726", "0000000000-24-008504", "0001493152-24-028669", "0001493152-24-025755", "0001493152-24-025754", "0001493152-24-025334", "0001493152-24-022030", "0001493152-24-020410", "0001493152-24-015061", "0001493152-24-009929", "0000000000-24-001960", "0001493152-24-004780", "0001493152-24-004779", "0001493152-24-004126", "0001493152-24-003346", "0001493152-24-002272", "0000000000-24-000414", "0001493152-23-044572", "0001876512-23-000002", "0001493152-23-042449", "0001493152-23-032370", "0001493152-23-032369", "0001493152-23-032102", "0001493152-23-032099", "0001493152-23-030408", "0001493152-23-030409", "0001493152-23-030373", "0001493152-23-027196", "0001493152-23-027194", "0001493152-23-026766", "0001493152-23-026764", "0001493152-23-026315", "0001493152-23-026309", "0001143313-23-000049", "0001493152-23-026184", "0001493152-23-026047", "0001493152-23-026046", "0001493152-23-024935", "0001493152-23-024671", "0001493152-23-024669", "0000000000-23-007340", "0001493152-23-023989", "0001493152-23-023200", "0001493152-23-022975", "0001493152-23-022972", "0001493152-23-021670", "0001493152-23-021108", "0000000000-23-006303", "0001493152-23-020585", "0001493152-23-020021", "0001493152-23-020018", "0000000000-23-005619", "0001493152-23-016638", "0001493152-23-016635", "0001493152-23-015832", "0001493152-23-015831", "0001493152-23-013119", "0001493152-23-011018", "0001493152-23-011017", "0001493152-23-008428", "0001493152-23-007598", "0001954927-23-000001", "0001493152-23-004852", "0001493152-23-002944", "0000000000-23-000837", "0001493152-23-002197", "0001493152-23-001912", "0001493152-23-001910", "0001493152-23-001609", "0000000000-23-000108", "0001493152-22-036159", "0001493152-22-036158", "0001493152-22-035733", "0000000000-22-013349", "0001493152-22-034325", "0001493152-22-033845", "0000000000-22-012519", "0000000000-22-012511", "0001493152-22-032740", "0001493152-22-031884", "0001493152-22-031880", "0001493152-22-031879", "0001493152-22-031875", "0001493152-22-031874", "0001493152-22-030651", "0001493152-22-030649", "0001493152-22-030383", "0001493152-22-030380", "0000000000-22-011047", "0000000000-22-011044", "0001493152-22-025547", "0001493152-22-025515", "0001011034-21-000004", "9999999997-20-005004", "0001011034-20-000026", "0001376474-20-000243", "0001376474-20-000242", "0001011034-20-000023", "0001011034-20-000021", "0001011034-20-000013", "0001011034-20-000006", "0001011034-20-000003", "0001011034-19-000027", "0001011034-19-000013", "0001011034-19-000002", "0001011034-18-000100", "0001011034-18-000094", "0001011034-18-000066", "0001011034-18-000040", "0001011034-18-000029", "0001011034-17-000188", "0001011034-17-000150", "0001011034-17-000067", "0001708402-17-000001", "0001011034-17-000050", "0001011034-17-000048", "0001011034-17-000037", "0001011034-17-000023", "0001376474-16-000845", "0001376474-16-000627", "0001376474-16-000531", "0001376474-15-000488", "0001376474-15-000380", "0001376474-15-000356", "0001011034-15-000048", "0001011034-15-000036", "0001011034-15-000030", "0001011034-15-000023", "0001011034-15-000022", "0001011034-15-000017", "0001011034-14-000124", "0001011034-14-000120", "0001011034-14-000119", "0001376474-14-000329", "0001011034-14-000113", "0001011034-14-000105", "0001011034-14-000094", "0001294606-14-000140", "0001011034-14-000086", "0001294606-14-000052", "0001294606-14-000020", "0001011034-14-000012", "0001011034-14-000011", "0001011034-14-000010", "0001294606-13-000234", "0001011034-13-000118", "0001294606-13-000159", "0001011034-13-000081", "0001011034-13-000079", "0001294606-13-000064", "0001011034-13-000040", "0001011034-13-000030", "0001294606-13-000016", "0001011034-13-000015", "0001294606-12-000375", "0001011034-12-000200", "0001011034-12-000187", "0001011034-12-000185", "0001376474-12-000184", "0001011034-12-000118", "0001011034-12-000117", "0001011034-12-000099", "0001011034-12-000096", "0001376474-12-000066", "0001011034-12-000052", "0001376474-12-000037", "0001011034-12-000036", "0001011034-12-000028", "0001376474-11-000153", "0001011034-11-000120", "0001011034-11-000094", "0001011034-11-000088", "0001011034-11-000087", "0001011034-11-000086", "0001011034-11-000084", "0001011034-11-000049", "0001011034-11-000031", "0001011034-11-000024", "0001011034-11-000018", "0000793171-10-000004", "0001011034-10-000155", "0001011034-10-000154", "0001011034-10-000152", "0001011034-10-000151", "0001011034-10-000106", "0001011034-10-000099", "0001011034-10-000066", "0001011034-10-000049", "0001011034-10-000038", "0001011034-10-000026", "0001011034-10-000009", "0001011034-10-000004", "0001011034-09-000161", "0001011034-09-000160", "0001011034-09-000154", "0001011034-09-000153", "0001011034-09-000121", "0001011034-09-000116", "0001011034-09-000114", "0001011034-09-000112", "0001011034-09-000092", "0001011034-09-000089", "0001011034-09-000088", "0001011034-09-000065", "0001011034-09-000034", "0001011034-09-000032", "0001011034-09-000031", "0001181431-09-009345", "0001181431-09-005208", "0001000096-08-000293", "0001181431-08-053194", "0001000096-08-000149", "0001011034-08-000069", "0001011034-08-000066", "0001181431-08-026088", "0001000096-08-000083", "0001011034-08-000031", "0001354488-08-000213", "9999999997-08-006772", "0001354488-08-000181", "0001011034-08-000023", "0001181431-08-007707", "0001181431-08-006048", "0001050502-07-000341", "0001181431-07-057424", "0001000096-07-000259", "0001000096-07-000102", "0001000096-07-000045", "0001181431-07-006369", "0001050502-07-000014", "0001181431-07-005442", "0000000000-07-002736", "0001181431-07-004044", "0001181431-06-070164", "0001050502-06-000380", "0001050502-06-000250", "0001000096-06-000234", "0001050502-06-000091", "0001035704-06-000084", "0000950134-06-001429", "0001035704-06-000054", "0001035704-06-000033", "0000000000-06-001438", "0000950134-06-000316", "0001000096-05-000674", "0001108890-05-000601", "0001000096-05-000399", "0001000096-05-000267", "0001000096-05-000235", "0001000096-05-000172", "0001000096-05-000086", "0001050502-05-000119", "0001050502-05-000064", "0001050502-05-000063", "0001050502-05-000053", "0001050502-05-000052", "0001050502-05-000051", "0001050502-05-000050", "0001050502-05-000047", "0001000096-04-000520", "0001050502-04-000356", "0001000096-04-000076", "0001050502-04-000044", "0001000096-04-000037", "0001000096-03-000635", "0001000096-03-000405", "0001000096-03-000284", "0001079974-03-000341", "0001079974-03-000148", "0001079974-03-000140", "0001079974-03-000079", "0001079974-03-000035", "0001079974-03-000033", "0001079974-02-000594", "0001079974-02-000593", "0001079974-02-000495", "0001079974-02-000435", "0001079974-02-000298", "0001079974-02-000223", "0001079974-02-000165", "0001079974-02-000159", "0001079974-02-000156", "0001079974-02-000122", "0001079974-02-000113", "0001079974-01-500164", "0001079974-01-500106", "0001025537-01-500021", "0001079974-01-000050", "0001079974-01-000037", "0001079974-01-000035", "0001079974-01-000034", "0001079974-01-000033", "0001079974-01-000021", "0001025537-01-000029", "0001079974-01-000010", "0001079974-01-000004", "0001079974-00-000172", "0001079974-00-000171", "0001079974-00-000169", "0001079974-00-000165", "0001079974-00-000163", "0001079974-00-000149", "0001079974-00-000146", "0001079974-00-000123", "0001079974-00-000074", "0001079974-00-000072", "0001013993-00-000070", "0001072588-00-000099", "0001013993-00-000012", "0001072588-00-000019", "0001072588-00-000018", "0001072588-00-000016", "0001072588-00-000013", "0001072588-00-000012", "0001072588-00-000009", "0001013993-00-000006", "0001013993-00-000005", "0001013993-99-000088", "0001013993-99-000087", "0001013993-99-000079", "0001013993-99-000078", "0001013993-99-000077", "0001072588-99-000095", "0001072588-99-000092", "0001013993-99-000063", "0001013993-99-000049", "0001013993-99-000022", "0001013993-99-000021", "0001047469-99-003282", "0001047469-99-000552", "0001013993-99-000003", "0001013993-98-000051", "0001013993-98-000050", "0001013993-98-000049", "0001013993-98-000048", "0001013993-97-000045", "0001013993-97-000029", "0001013993-97-000012", "0001013993-97-000006", "0001013993-97-000005", "0001013993-97-000004", "0001013993-97-000003", "0001013993-97-000001", "0001013993-96-000032", "0001013993-96-000024"], "filingDate": ["2025-06-11", "2025-03-20", "2025-03-17", "2025-01-28", "2024-09-16", "2024-09-11", "2024-09-10", "2024-09-10", "2024-09-05", "2024-08-29", "2024-08-29", "2024-08-23", "2024-08-23", "2024-08-20", "2024-08-08", "2024-08-08", "2024-07-26", "2024-07-22", "2024-07-01", "2024-07-01", "2024-06-27", "2024-05-31", "2024-05-17", "2024-04-17", "2024-03-14", "2024-02-21", "2024-02-02", "2024-02-02", "2024-01-29", "2024-01-23", "2024-01-16", "2024-01-11", "2023-12-12", "2023-12-01", "2023-11-22", "2023-09-12", "2023-09-12", "2023-09-08", "2023-09-08", "2023-08-29", "2023-08-28", "2023-08-28", "2023-08-08", "2023-08-08", "2023-08-04", "2023-08-04", "2023-08-01", "2023-08-01", "2023-08-01", "2023-08-01", "2023-07-31", "2023-07-31", "2023-07-19", "2023-07-17", "2023-07-17", "2023-07-11", "2023-07-07", "2023-06-30", "2023-06-29", "2023-06-29", "2023-06-16", "2023-06-13", "2023-06-12", "2023-06-08", "2023-06-02", "2023-06-02", "2023-05-26", "2023-05-12", "2023-05-12", "2023-05-08", "2023-05-08", "2023-04-20", "2023-04-05", "2023-04-05", "2023-03-21", "2023-03-14", "2023-02-15", "2023-02-14", "2023-01-30", "2023-01-25", "2023-01-23", "2023-01-19", "2023-01-19", "2023-01-17", "2023-01-04", "2022-12-21", "2022-12-21", "2022-12-16", "2022-12-12", "2022-12-02", "2022-11-28", "2022-11-17", "2022-11-17", "2022-11-17", "2022-11-14", "2022-11-14", "2022-11-14", "2022-11-14", "2022-11-14", "2022-11-04", "2022-11-04", "2022-11-03", "2022-11-03", "2022-10-06", "2022-10-06", "2022-09-12", "2022-09-09", "2021-10-20", "2020-10-13", "2020-10-08", "2020-10-05", "2020-10-05", "2020-07-09", "2020-06-04", "2020-02-04", "2020-01-27", "2020-01-23", "2019-06-20", "2019-03-25", "2019-01-29", "2018-12-12", "2018-09-25", "2018-06-26", "2018-05-08", "2018-03-23", "2017-12-27", "2017-10-13", "2017-07-11", "2017-06-05", "2017-06-01", "2017-05-30", "2017-04-19", "2017-03-29", "2016-09-22", "2016-03-30", "2016-01-22", "2015-12-07", "2015-09-24", "2015-08-31", "2015-04-01", "2015-03-24", "2015-03-19", "2015-02-17", "2015-02-13", "2015-01-29", "2014-10-03", "2014-10-01", "2014-09-24", "2014-09-22", "2014-09-12", "2014-08-22", "2014-07-28", "2014-06-19", "2014-06-16", "2014-03-17", "2014-02-12", "2014-01-29", "2014-01-29", "2014-01-28", "2013-09-23", "2013-09-16", "2013-06-18", "2013-06-14", "2013-06-13", "2013-03-21", "2013-03-18", "2013-02-25", "2013-02-12", "2013-01-29", "2012-10-17", "2012-10-15", "2012-09-20", "2012-09-14", "2012-06-22", "2012-06-19", "2012-06-14", "2012-05-11", "2012-05-02", "2012-03-21", "2012-03-16", "2012-02-16", "2012-02-13", "2012-01-31", "2011-10-12", "2011-09-14", "2011-07-07", "2011-06-29", "2011-06-28", "2011-06-21", "2011-06-14", "2011-04-01", "2011-03-18", "2011-03-15", "2011-01-28", "2010-09-30", "2010-09-21", "2010-09-17", "2010-09-14", "2010-09-09", "2010-06-21", "2010-06-14", "2010-04-12", "2010-03-17", "2010-02-16", "2010-01-29", "2010-01-08", "2010-01-06", "2009-12-30", "2009-12-30", "2009-12-04", "2009-12-04", "2009-09-11", "2009-08-31", "2009-08-19", "2009-08-19", "2009-07-17", "2009-07-13", "2009-07-13", "2009-06-11", "2009-03-19", "2009-03-17", "2009-03-17", "2009-02-17", "2009-01-29", "2008-09-22", "2008-09-15", "2008-06-16", "2008-05-27", "2008-05-21", "2008-04-23", "2008-03-17", "2008-02-19", "2008-02-19", "2008-02-14", "2008-02-13", "2008-02-08", "2008-02-06", "2008-01-30", "2007-09-18", "2007-09-14", "2007-06-15", "2007-03-19", "2007-02-12", "2007-01-29", "2007-01-26", "2007-01-24", "2007-01-18", "2007-01-16", "2006-12-15", "2006-09-14", "2006-06-14", "2006-05-25", "2006-03-20", "2006-02-13", "2006-01-30", "2006-01-26", "2006-01-11", "2006-01-10", "2006-01-10", "2005-12-08", "2005-09-14", "2005-06-14", "2005-04-26", "2005-04-13", "2005-03-16", "2005-02-11", "2005-01-28", "2005-01-19", "2005-01-19", "2005-01-18", "2005-01-18", "2005-01-18", "2005-01-18", "2005-01-14", "2004-09-10", "2004-06-14", "2004-03-15", "2004-02-05", "2004-01-29", "2003-12-12", "2003-09-15", "2003-06-19", "2003-06-16", "2003-03-24", "2003-03-18", "2003-02-27", "2003-01-30", "2003-01-30", "2002-12-13", "2002-12-13", "2002-10-09", "2002-09-16", "2002-06-14", "2002-05-07", "2002-03-20", "2002-03-18", "2002-03-12", "2002-02-12", "2002-01-30", "2001-09-14", "2001-06-14", "2001-04-03", "2001-03-16", "2001-02-28", "2001-02-22", "2001-02-22", "2001-02-22", "2001-02-16", "2001-02-14", "2001-02-05", "2001-01-29", "2000-12-01", "2000-11-02", "2000-10-30", "2000-10-17", "2000-10-13", "2000-09-21", "2000-09-15", "2000-08-22", "2000-06-29", "2000-06-28", "2000-06-15", "2000-06-08", "2000-03-15", "2000-02-16", "2000-02-16", "2000-02-16", "2000-02-16", "2000-02-16", "2000-02-11", "2000-02-01", "2000-02-01", "1999-11-12", "1999-11-12", "1999-10-19", "1999-10-19", "1999-10-19", "1999-09-28", "1999-09-10", "1999-08-26", "1999-05-13", "1999-03-01", "1999-02-22", "1999-02-04", "1999-01-08", "1999-01-08", "1998-11-02", "1998-11-02", "1998-11-02", "1998-11-02", "1997-08-28", "1997-06-09", "1997-03-04", "1997-02-14", "1997-01-22", "1997-01-21", "1997-01-21", "1997-01-07", "1996-12-13", "1996-10-23"], "reportDate": ["", "2025-01-31", "2025-01-31", "2024-10-31", "2024-07-31", "", "", "", "", "", "", "", "", "", "", "", "", "2024-07-16", "", "", "2024-06-21", "2024-04-30", "2024-05-13", "2024-04-11", "2024-01-31", "", "", "", "2023-10-31", "2024-01-18", "2024-01-10", "", "", "", "2023-11-16", "", "", "", "", "", "", "2023-07-31", "", "", "", "", "", "", "", "", "", "", "2023-07-13", "", "", "", "2023-06-30", "2022-11-11", "", "", "2023-06-13", "2023-04-30", "", "2023-06-05", "", "", "", "2023-03-30", "2023-03-15", "", "", "2023-04-14", "", "2023-03-30", "2023-03-15", "2023-01-31", "", "", "2022-10-31", "", "", "", "", "2023-01-06", "", "", "", "2022-12-12", "", "", "2022-11-20", "", "", "2022-11-11", "2022-11-11", "2022-11-11", "2022-11-11", "2022-11-11", "2022-11-11", "", "", "", "", "", "", "", "", "", "", "", "2020-10-01", "2020-09-30", "2020-06-25", "2020-05-29", "2020-01-20", "2020-01-22", "", "2019-06-01", "2019-03-21", "2019-01-24", "2018-12-06", "2018-09-25", "2018-06-26", "2018-05-03", "2018-03-23", "2017-12-20", "2017-10-12", "2017-07-06", "2017-05-24", "2017-06-01", "2017-05-26", "2017-04-14", "2017-03-23", "2015-10-31", "2015-07-31", "2015-04-30", "2015-11-06", "2015-07-31", "2015-08-21", "2015-03-30", "2015-01-31", "2015-03-19", "2014-09-23", "2014-10-31", "2014-10-31", "2014-09-30", "2014-08-18", "2014-09-23", "2014-07-31", "2014-07-31", "2014-08-18", "2013-10-18", "2014-04-30", "2014-04-30", "2014-01-31", "2013-10-31", "2013-09-26", "2013-10-31", "2013-02-21", "2013-07-31", "2013-07-31", "2013-04-30", "2013-04-30", "2013-05-28", "2013-01-31", "2013-01-31", "2013-02-20", "2012-10-31", "2012-10-31", "2012-07-31", "2012-07-31", "2012-07-31", "2012-07-31", "2012-04-30", "2012-04-30", "2012-04-30", "2012-05-10", "2012-05-01", "2012-01-31", "2012-01-31", "2011-10-31", "2011-10-31", "2011-10-31", "2011-07-31", "2011-07-31", "2011-04-27", "2011-04-30", "2011-06-27", "2011-04-27", "2011-04-30", "2011-03-30", "2011-03-17", "2011-01-31", "2010-10-31", "", "2010-09-17", "2010-07-31", "2010-07-31", "2010-09-08", "2010-04-30", "2010-04-30", "2010-02-18", "2010-01-31", "2009-10-31", "2009-10-31", "", "", "2009-12-30", "2009-12-29", "2009-12-04", "", "2009-07-31", "2009-08-17", "", "2009-08-17", "2002-06-15", "", "2008-05-01", "2009-04-30", "2009-01-31", "2009-01-31", "2008-10-31", "2009-02-17", "2008-10-31", "2008-07-31", "2008-07-31", "2008-04-30", "", "2008-04-30", "2008-04-22", "2008-01-31", "", "2007-10-31", "", "2007-12-31", "2008-01-31", "2008-02-05", "2008-01-29", "2007-07-31", "2007-09-14", "2007-04-30", "2007-01-31", "2006-10-31", "2007-01-29", "", "2007-01-23", "", "2007-01-16", "2006-10-31", "2006-07-31", "2006-04-30", "2006-05-19", "2006-01-31", "2005-10-31", "2005-10-31", "", "2006-01-05", "", "2006-01-05", "2005-10-31", "2005-07-31", "2005-04-30", "2005-04-08", "2005-04-08", "2005-01-31", "2004-10-31", "2004-10-31", "2004-12-09", "2004-12-09", "2003-02-26", "2003-02-26", "2002-02-13", "2001-03-13", "2003-09-02", "2004-07-31", "2004-04-30", "2004-01-31", "2003-10-31", "2003-10-31", "2003-09-02", "2003-07-31", "2003-04-30", "2003-04-30", "2003-01-31", "2003-01-31", "", "2002-10-31", "2002-10-31", "", "2002-11-11", "", "2002-07-31", "2002-04-30", "2002-04-23", "2002-01-31", "2002-01-31", "", "2001-10-31", "2001-10-31", "2001-07-31", "2001-04-30", "", "2001-01-31", "2001-04-10", "2000-07-31", "2000-04-30", "2000-01-31", "2001-04-10", "", "2001-04-10", "2000-10-31", "2000-12-01", "2000-10-09", "2000-12-01", "2000-12-01", "2000-10-09", "2000-07-31", "2000-07-31", "2000-08-07", "2000-04-30", "2000-01-31", "2000-04-30", "", "2000-01-31", "2000-02-11", "2000-02-11", "2000-02-11", "2000-02-11", "2000-02-11", "", "1999-10-31", "1999-10-31", "", "", "", "", "", "", "", "1999-07-31", "1999-04-30", "1999-01-31", "1998-10-31", "", "", "1998-12-29", "1998-07-31", "1998-04-30", "1998-01-31", "1997-10-31", "1997-07-31", "1997-04-30", "1996-01-31", "1996-10-31", "1996-10-31", "1996-07-31", "1996-04-30", "1995-01-31", "1995-10-31", "1996-10-22"], "acceptanceDateTime": ["2025-06-11T15:50:15.000Z", "2025-03-20T11:33:48.000Z", "2025-03-17T16:21:47.000Z", "2025-01-28T13:07:05.000Z", "2024-09-16T12:20:20.000Z", "2024-09-12T00:15:03.000Z", "2024-09-10T19:48:08.000Z", "2024-09-10T19:44:42.000Z", "2024-09-05T11:05:17.000Z", "2024-08-29T16:24:21.000Z", "2024-08-29T13:36:04.000Z", "2024-08-23T16:30:41.000Z", "2024-08-23T16:29:47.000Z", "2024-08-20T12:36:07.000Z", "2024-08-08T14:58:19.000Z", "2024-08-08T14:57:36.000Z", "2024-07-26T14:36:09.000Z", "2024-07-22T15:09:22.000Z", "2024-07-01T14:20:16.000Z", "2024-07-01T14:19:37.000Z", "2024-06-26T21:13:47.000Z", "2024-05-30T18:01:22.000Z", "2024-05-17T15:48:13.000Z", "2024-04-17T17:27:32.000Z", "2024-03-14T14:06:34.000Z", "2024-02-21T12:36:09.000Z", "2024-02-02T16:52:19.000Z", "2024-02-02T16:51:43.000Z", "2024-01-29T15:31:43.000Z", "2024-01-22T21:17:49.000Z", "2024-01-12T18:02:54.000Z", "2024-01-11T17:36:08.000Z", "2023-12-12T16:51:40.000Z", "2023-12-01T10:29:32.000Z", "2023-11-22T13:55:18.000Z", "2023-09-12T16:50:09.000Z", "2023-09-12T16:50:09.000Z", "2023-09-08T16:17:47.000Z", "2023-09-08T16:15:10.000Z", "2023-08-28T21:58:25.000Z", "2023-08-28T21:58:09.000Z", "2023-08-28T16:54:54.000Z", "2023-08-08T17:10:35.000Z", "2023-08-08T17:07:21.000Z", "2023-08-04T13:09:53.000Z", "2023-08-04T13:08:41.000Z", "2023-08-01T17:23:54.000Z", "2023-08-01T17:21:11.000Z", "2023-08-01T12:16:35.000Z", "2023-08-01T10:03:26.000Z", "2023-07-31T14:01:14.000Z", "2023-07-31T14:00:10.000Z", "2023-07-19T16:11:19.000Z", "2023-07-17T07:30:21.000Z", "2023-07-17T07:30:18.000Z", "2023-07-11T11:30:07.000Z", "2023-07-07T16:17:03.000Z", "2023-06-30T20:35:36.000Z", "2023-06-29T17:29:27.000Z", "2023-06-29T17:29:10.000Z", "2023-06-16T16:15:22.000Z", "2023-06-13T17:24:44.000Z", "2023-06-12T17:30:05.000Z", "2023-06-08T16:51:12.000Z", "2023-06-02T17:20:46.000Z", "2023-06-02T17:19:55.000Z", "2023-05-26T12:30:07.000Z", "2023-05-12T16:13:13.000Z", "2023-05-12T16:10:31.000Z", "2023-05-08T17:32:00.000Z", "2023-05-08T17:30:31.000Z", "2023-04-20T17:14:28.000Z", "2023-04-05T16:03:32.000Z", "2023-04-05T16:02:18.000Z", "2023-03-21T17:12:48.000Z", "2023-03-14T16:08:02.000Z", "2023-02-15T09:03:42.000Z", "2023-02-14T16:06:04.000Z", "2023-01-30T16:42:31.000Z", "2023-01-25T16:30:09.000Z", "2023-01-23T16:11:20.000Z", "2023-01-19T16:27:24.000Z", "2023-01-19T16:26:08.000Z", "2023-01-17T16:05:34.000Z", "2023-01-04T16:30:11.000Z", "2022-12-21T16:51:40.000Z", "2022-12-21T16:50:54.000Z", "2022-12-16T16:05:27.000Z", "2022-12-12T14:30:16.000Z", "2022-12-02T16:45:57.000Z", "2022-11-28T16:48:21.000Z", "2022-11-17T17:30:07.000Z", "2022-11-17T16:30:07.000Z", "2022-11-17T12:45:07.000Z", "2022-11-14T14:33:08.000Z", "2022-11-14T14:30:37.000Z", "2022-11-14T14:30:26.000Z", "2022-11-14T14:28:55.000Z", "2022-11-14T14:28:31.000Z", "2022-11-04T15:31:16.000Z", "2022-11-04T15:29:17.000Z", "2022-11-03T17:29:46.000Z", "2022-11-03T17:28:47.000Z", "2022-10-06T18:30:05.000Z", "2022-10-06T18:30:05.000Z", "2022-09-09T21:46:13.000Z", "2022-09-09T17:13:13.000Z", "2021-10-20T12:20:36.000Z", "2020-10-13T11:55:27.000Z", "2020-10-08T16:08:25.000Z", "2020-10-05T17:20:43.000Z", "2020-10-05T13:47:41.000Z", "2020-07-09T12:23:57.000Z", "2020-06-04T14:41:42.000Z", "2020-02-04T16:05:11.000Z", "2020-01-27T15:19:37.000Z", "2020-01-23T17:17:08.000Z", "2019-06-20T17:07:51.000Z", "2019-03-25T14:02:51.000Z", "2019-01-29T12:51:30.000Z", "2018-12-12T11:28:37.000Z", "2018-09-25T17:03:36.000Z", "2018-06-26T16:56:48.000Z", "2018-05-07T17:42:13.000Z", "2018-03-23T16:32:56.000Z", "2017-12-27T14:25:28.000Z", "2017-10-12T18:02:00.000Z", "2017-07-11T16:49:23.000Z", "2017-06-05T11:31:11.000Z", "2017-06-01T16:08:31.000Z", "2017-05-30T12:44:49.000Z", "2017-04-19T14:04:40.000Z", "2017-03-29T17:23:04.000Z", "2016-09-22T17:23:19.000Z", "2016-03-30T16:16:31.000Z", "2016-01-22T09:47:13.000Z", "2015-12-07T16:44:48.000Z", "2015-09-24T11:21:08.000Z", "2015-08-31T17:20:00.000Z", "2015-04-01T13:00:04.000Z", "2015-03-24T13:39:37.000Z", "2015-03-19T15:04:27.000Z", "2015-02-17T13:01:39.000Z", "2015-02-13T14:50:24.000Z", "2015-01-29T11:31:16.000Z", "2014-10-03T13:55:53.000Z", "2014-10-01T12:26:19.000Z", "2014-09-24T11:50:09.000Z", "2014-09-22T14:14:15.000Z", "2014-09-12T16:19:06.000Z", "2014-08-22T13:17:20.000Z", "2014-07-28T16:57:08.000Z", "2014-06-18T18:32:22.000Z", "2014-06-16T12:14:24.000Z", "2014-03-14T17:54:39.000Z", "2014-02-12T16:24:00.000Z", "2014-01-29T11:59:32.000Z", "2014-01-29T11:26:44.000Z", "2014-01-28T16:44:07.000Z", "2013-09-23T14:05:56.000Z", "2013-09-16T17:17:51.000Z", "2013-06-18T17:29:48.000Z", "2013-06-14T17:15:56.000Z", "2013-06-13T15:05:32.000Z", "2013-03-21T15:49:07.000Z", "2013-03-18T15:53:51.000Z", "2013-02-25T15:57:28.000Z", "2013-02-12T17:29:20.000Z", "2013-01-29T16:28:21.000Z", "2012-10-17T17:18:13.000Z", "2012-10-15T15:35:12.000Z", "2012-09-20T16:11:53.000Z", "2012-09-14T15:58:45.000Z", "2012-06-22T14:47:37.000Z", "2012-06-19T14:52:05.000Z", "2012-06-14T12:52:53.000Z", "2012-05-11T13:24:04.000Z", "2012-05-02T15:17:16.000Z", "2012-03-21T10:26:11.000Z", "2012-03-16T14:47:50.000Z", "2012-02-16T12:27:43.000Z", "2012-02-13T16:05:35.000Z", "2012-01-31T12:47:46.000Z", "2011-10-12T12:22:35.000Z", "2011-09-14T16:41:53.000Z", "2011-07-07T16:02:30.000Z", "2011-06-29T11:07:24.000Z", "2011-06-28T15:12:29.000Z", "2011-06-21T13:29:33.000Z", "2011-06-14T13:33:32.000Z", "2011-04-01T13:19:04.000Z", "2011-03-18T13:45:52.000Z", "2011-03-15T15:34:41.000Z", "2011-01-28T16:29:01.000Z", "2010-09-30T13:05:40.000Z", "2010-09-21T15:04:13.000Z", "2010-09-17T16:44:31.000Z", "2010-09-14T16:18:23.000Z", "2010-09-09T13:42:39.000Z", "2010-06-21T16:30:43.000Z", "2010-06-14T14:54:37.000Z", "2010-04-12T12:35:09.000Z", "2010-03-17T15:42:27.000Z", "2010-02-16T12:07:47.000Z", "2010-01-29T13:47:50.000Z", "2010-01-08T16:18:57.000Z", "2010-01-06T14:07:14.000Z", "2009-12-30T12:57:37.000Z", "2009-12-30T12:43:14.000Z", "2009-12-04T16:01:32.000Z", "2009-12-04T15:54:38.000Z", "2009-09-11T15:58:24.000Z", "2009-08-31T18:05:17.000Z", "2009-08-19T15:05:08.000Z", "2009-08-19T12:20:11.000Z", "2009-07-17T13:00:25.000Z", "2009-07-13T13:18:28.000Z", "2009-07-13T13:12:06.000Z", "2009-06-11T14:54:24.000Z", "2009-03-19T16:04:16.000Z", "2009-03-17T13:43:06.000Z", "2009-03-17T12:51:53.000Z", "2009-02-17T17:13:11.000Z", "2009-01-29T13:10:20.000Z", "2008-09-22T10:17:44.000Z", "2008-09-15T17:15:57.000Z", "2008-06-16T09:17:35.000Z", "2008-05-27T15:32:44.000Z", "2008-05-21T11:45:21.000Z", "2008-04-22T19:32:17.000Z", "2008-03-17T11:50:27.000Z", "2008-02-19T17:05:21.000Z", "2008-02-19T06:56:33.000Z", "2008-02-20T16:55:26.000Z", "2008-02-13T12:22:27.000Z", "2008-02-08T13:15:09.000Z", "2008-02-05T17:55:08.000Z", "2008-01-29T17:32:36.000Z", "2007-09-18T17:16:12.000Z", "2007-09-14T12:09:34.000Z", "2007-06-15T10:50:41.000Z", "2007-03-19T13:48:06.000Z", "2007-02-12T17:30:58.000Z", "2007-01-29T14:36:05.000Z", "2007-01-26T16:30:52.000Z", "2007-01-23T19:49:18.000Z", "2007-01-18T15:31:33.000Z", "2007-01-16T16:41:18.000Z", "2006-12-15T17:24:20.000Z", "2006-09-14T16:07:08.000Z", "2006-06-14T16:37:48.000Z", "2006-05-25T09:46:39.000Z", "2006-03-20T16:01:27.000Z", "2006-02-13T16:15:10.000Z", "2006-01-30T15:55:30.000Z", "2006-01-26T09:57:40.000Z", "2006-01-11T09:43:14.000Z", "2006-01-10T15:36:05.000Z", "2006-01-10T12:00:53.000Z", "2005-12-08T14:44:10.000Z", "2005-09-14T09:42:53.000Z", "2005-06-14T16:21:49.000Z", "2005-04-26T12:19:59.000Z", "2005-04-13T15:19:56.000Z", "2005-03-16T15:47:44.000Z", "2005-02-11T16:47:50.000Z", "2005-01-28T09:56:01.000Z", "2005-01-19T14:46:21.000Z", "2005-01-19T14:45:45.000Z", "2005-01-18T10:11:30.000Z", "2005-01-18T10:10:37.000Z", "2005-01-18T10:10:07.000Z", "2005-01-18T10:09:29.000Z", "2005-01-14T19:31:33.000Z", "2004-09-10T12:26:08.000Z", "2004-06-14T16:52:01.000Z", "2004-03-15T15:58:27.000Z", "2004-02-05T16:25:31.000Z", "2004-01-29T11:35:49.000Z", "2003-12-12T16:25:57.000Z", "2003-09-15T11:02:32.000Z", "2003-06-19T08:18:22.000Z", "2003-06-16T14:26:43.000Z", "2003-03-24T14:19:55.000Z", "2003-03-18T10:58:19.000Z", "2003-02-27T13:25:37.000Z", "2003-01-30T12:49:18.000Z", "2003-01-30T09:34:12.000Z", "2002-12-13T09:09:03.000Z", "2002-12-13T09:08:20.000Z", "2002-10-09T16:20:39.000Z", "2002-09-16T11:25:27.000Z", "2002-06-14T11:29:11.000Z", "2002-05-07T16:35:23.000Z", "2002-03-20T00:00:00.000Z", "2002-03-18T00:00:00.000Z", "2002-03-12T00:00:00.000Z", "2002-02-12T00:00:00.000Z", "2002-01-30T00:00:00.000Z", "2001-09-14T00:00:00.000Z", "2001-06-14T00:00:00.000Z", "2001-04-03T00:00:00.000Z", "2001-03-16T00:00:00.000Z", "2001-02-28T00:00:00.000Z", "2001-02-22T00:00:00.000Z", "2001-02-22T00:00:00.000Z", "2001-02-22T00:00:00.000Z", "2001-02-16T00:00:00.000Z", "2001-02-14T00:00:00.000Z", "2001-02-05T00:00:00.000Z", "2001-01-29T00:00:00.000Z", "2000-12-01T00:00:00.000Z", "2000-11-02T00:00:00.000Z", "2000-10-30T00:00:00.000Z", "2000-10-17T00:00:00.000Z", "2000-10-13T00:00:00.000Z", "2000-09-21T00:00:00.000Z", "2000-09-15T00:00:00.000Z", "2000-08-22T00:00:00.000Z", "2000-06-29T00:00:00.000Z", "2000-06-28T00:00:00.000Z", "2000-06-15T00:00:00.000Z", "2000-06-08T00:00:00.000Z", "2000-03-15T00:00:00.000Z", "2000-02-16T00:00:00.000Z", "2000-02-16T00:00:00.000Z", "2000-02-16T00:00:00.000Z", "2000-02-16T00:00:00.000Z", "2000-02-16T00:00:00.000Z", "2000-02-11T00:00:00.000Z", "2000-02-01T00:00:00.000Z", "2000-02-01T00:00:00.000Z", "1999-11-12T00:00:00.000Z", "1999-11-12T00:00:00.000Z", "1999-10-19T00:00:00.000Z", "1999-10-19T00:00:00.000Z", "1999-10-19T00:00:00.000Z", "1999-09-28T00:00:00.000Z", "1999-09-10T00:00:00.000Z", "1999-08-26T00:00:00.000Z", "1999-05-13T00:00:00.000Z", "1999-03-01T00:00:00.000Z", "1999-02-22T00:00:00.000Z", "1999-02-04T00:00:00.000Z", "1999-01-08T00:00:00.000Z", "1999-01-08T00:00:00.000Z", "1998-11-02T00:00:00.000Z", "1998-11-02T00:00:00.000Z", "1998-11-02T00:00:00.000Z", "1998-11-02T00:00:00.000Z", "1997-08-28T00:00:00.000Z", "1997-06-09T00:00:00.000Z", "1997-03-04T00:00:00.000Z", "1997-02-14T00:00:00.000Z", "1997-01-22T00:00:00.000Z", "1997-01-21T00:00:00.000Z", "1997-01-21T00:00:00.000Z", "1997-01-07T00:00:00.000Z", "1996-12-13T00:00:00.000Z", "1996-10-23T00:00:00.000Z"], "act": ["33", "34", "34", "34", "34", "33", "", "", "33", "", "", "", "33", "", "", "33", "", "34", "", "33", "34", "34", "34", "34", "34", "", "", "33", "34", "34", "34", "", "33", "33", "34", "", "", "", "", "33", "", "34", "", "", "", "", "", "", "34", "34", "", "", "34", "", "33", "", "34", "", "", "33", "34", "34", "", "34", "", "33", "", "34", "34", "33", "", "34", "33", "34", "34", "34", "34", "34", "34", "", "33", "", "34", "34", "", "", "34", "34", "", "34", "34", "", "", "", "", "", "", "", "", "", "34", "", "33", "", "", "34", "33", "33", "34", "33", "34", "34", "34", "34", "34", "34", "33", "34", "34", "34", "34", "34", "34", "34", "34", "34", "34", "34", "", "34", "34", "34", "34", "34", "34", "34", "34", "34", "34", "34", "34", "34", "", "34", "34", "", "", "", "34", "34", "34", "", "34", "34", "34", "34", "", "34", "", "34", "34", "34", "34", "34", "34", "34", "34", "34", "34", "34", "34", "34", "34", "34", "34", "34", "34", "34", "34", "34", "34", "34", "34", "34", "34", "", "34", "34", "34", "34", "34", "34", "34", "34", "33", "34", "34", "34", "34", "34", "34", "34", "34", "34", "34", "33", "33", "34", "34", "", "34", "34", "", "33", "34", "", "34", "", "34", "34", "34", "34", "34", "34", "34", "34", "34", "34", "", "34", "34", "34", "34", "34", "34", "", "34", "34", "34", "34", "34", "34", "34", "34", "", "34", "", "34", "", "34", "34", "34", "34", "34", "34", "", "34", "", "34", "", "34", "34", "", "34", "34", "34", "34", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "form": ["D", "10-Q", "NT 10-Q", "10-K", "10-Q", "EFFECT", "CORRESP", "CORRESP", "S-1/A", "CORRESP", "UPLOAD", "CORRESP", "S-1/A", "UPLOAD", "CORRESP", "S-1/A", "UPLOAD", "8-K", "CORRESP", "S-1/A", "8-K", "10-Q", "8-K", "8-K", "10-Q", "UPLOAD", "CORRESP", "S-1/A", "10-K", "8-K", "8-K", "UPLOAD", "S-1/A", "D", "8-K", "CORRESP", "CORRESP", "CORRESP", "CORRESP", "S-1/A", "CORRESP", "10-Q", "CORRESP", "CORRESP", "CORRESP", "CORRESP", "CORRESP", "CORRESP", "CERT", "8-A12B", "CORRESP", "CORRESP", "8-K", "CORRESP", "S-1/A", "UPLOAD", "8-K", "3/A", "CORRESP", "S-1/A", "8-K", "10-Q", "UPLOAD", "8-K", "CORRESP", "S-1/A", "UPLOAD", "8-K/A", "8-K/A", "S-1/A", "CORRESP", "8-K", "D/A", "8-K", "8-K", "10-Q", "SC 13G", "SC 13G", "10-K", "UPLOAD", "D", "CORRESP", "10-12G/A", "8-K", "UPLOAD", "CORRESP", "10-12G/A", "8-K", "UPLOAD", "SC 13D", "8-K", "UPLOAD", "UPLOAD", "3", "3", "3", "3", "3", "3", "CORRESP", "10-12G/A", "CORRESP", "S-1/A", "UPLOAD", "UPLOAD", "10-12G", "S-1", "D", "REVOKED", "D/A", "8-K", "8-K", "8-K", "8-K", "8-K/A", "8-K", "D", "8-K", "8-K", "8-K", "8-K", "8-K", "8-K", "8-K", "8-K", "8-K", "8-K", "8-K", "3", "8-K", "8-K", "8-K", "8-K", "10-K", "10-Q", "10-Q", "8-K", "NTN 10Q", "8-K", "8-K", "10-Q", "NT 10-Q", "4/A", "10-K", "NT 10-K", "4", "3", "4", "10-Q", "NT 10-Q", "8-K", "4", "10-Q", "NT 10-Q", "10-Q", "10-K", "4", "NT 10-K", "3", "10-Q", "NT 10-Q", "10-Q", "NT 10-Q", "8-K", "10-Q", "NT 10-Q", "8-K", "10-K", "NT 10-K", "10-Q/A", "10-Q/A", "10-Q", "NT 10-Q", "10-Q/A", "10-Q", "NT 10-Q", "8-K", "8-K", "10-Q", "NT 10-Q", "10-K/A", "10-K", "NT 10-K", "10-Q/A", "10-Q", "4", "10-Q", "8-K/A", "8-K", "NT 10-Q", "8-K", "8-K", "10-Q", "10-K", "D", "8-K", "10-Q", "NT 10-Q", "8-K", "10-Q", "NT 10-Q", "8-K", "10-Q", "10-K", "NT 10-K", "144", "D", "8-K", "8-K", "4", "SC 13G/A", "10-Q", "4", "144", "8-K", "3", "SC 13D/A", "4", "10-Q", "10-Q", "NT 10-Q", "10-K", "8-K", "NT 10-K", "10QSB", "NT 10-Q", "10QSB", "SC 13D", "4", "8-K", "10QSB", "SC 13G", "10KSB/A", "REGDEX", "10KSB", "3", "8-K", "NT 10-K", "10QSB", "NT 10-Q", "10QSB", "10QSB", "10KSB", "NT 10-K", "CORRESP", "8-K/A", "UPLOAD", "8-K", "5", "10QSB", "10QSB", "8-K", "10QSB", "10KSB", "NT 10-K", "CORRESP", "8-K/A", "UPLOAD", "8-K", "5", "10QSB", "10QSB", "3", "8-K", "10QSB", "10KSB", "NT 10-K", "5", "5", "4/A", "4/A", "4/A", "4/A", "4/A", "10QSB", "10QSB", "10QSB", "10KSB", "NT 10-K", "4", "10QSB", "10QSB", "NT 10-Q", "10QSB", "NT 10-Q", "SC 13D/A", "10KSB", "NT 10-K", "SC 13D/A", "4", "SC 13D/A", "10QSB", "10QSB", "8-K", "10QSB", "NT 10-Q", "SC 13D/A", "10KSB", "NT 10-K", "10-Q", "10QSB", "SC 13D/A", "10QSB", "DEF 14A", "10QSB/A", "10QSB/A", "10QSB/A", "PRE 14A", "SC 13D/A", "PRE 14A", "10KSB", "8-K", "8-K/A", "DEF 14A", "PRE 14A", "8-K", "10QSB", "NT 10-Q", "8-K", "10QSB", "10QSB/A", "NT 10-K", "SC 13D/A", "10QSB", "4", "5", "4", "5", "5", "SC 13D/A", "10KSB", "NT 10-K", "SC 13D/A", "SC 13D/A", "SC 13D/A", "SC 13D/A", "SC 13D", "SC 13D", "SC 13D", "10QSB", "10QSB", "10QSB", "10KSB", "SC 13D/A", "SC 13D", "8-K", "10QSB", "10QSB", "10QSB", "10KSB", "10QSB", "10QSB", "10QSB", "10KSB/A", "10KSB", "10QSB", "10QSB", "10QSB", "10KSB", "8-K/A"], "fileNumber": ["021-548679", "001-41766", "001-41766", "001-41766", "001-41766", "333-267366", "", "", "333-267366", "", "333-267366", "", "333-267366", "333-267366", "", "333-267366", "333-267366", "001-41766", "", "333-267366", "001-41766", "001-41766", "001-41766", "001-41766", "001-41766", "333-267366", "", "333-267366", "001-41766", "001-41766", "001-41766", "333-267366", "333-267366", "021-498570", "001-41766", "", "", "", "", "333-267366", "", "001-41766", "", "", "", "", "", "", "001-41766", "001-41766", "", "", "000-17378", "", "333-267366", "333-267366", "000-17378", "", "", "333-267366", "000-17378", "000-17378", "333-267366", "000-17378", "", "333-267366", "333-267366", "000-17378", "000-17378", "333-267366", "", "000-17378", "021-471523", "000-17378", "000-17378", "000-17378", "005-41585", "005-41585", "000-17378", "000-17378", "021-471523", "", "000-17378", "000-17378", "000-17378", "", "000-17378", "000-17378", "000-17378", "005-41585", "000-17378", "000-17378", "333-267366", "", "", "", "", "", "", "", "000-17378", "", "333-267366", "000-17378", "333-267366", "000-17378", "333-267366", "021-417700", "000-17378", "021-358825", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "021-358825", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "", "000-17378", "000-17378", "", "", "", "000-17378", "000-17378", "000-17378", "", "000-17378", "000-17378", "000-17378", "000-17378", "", "000-17378", "", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "021-148368", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "021-19713-D", "000-17378", "000-17378", "", "005-41585", "000-17378", "", "000-17378", "000-17378", "", "005-41585", "", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "005-41585", "", "000-17378", "000-17378", "005-41585", "000-17378", "021-19713-D", "000-17378", "", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "", "000-17378", "", "000-17378", "", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "", "000-17378", "", "000-17378", "", "000-17378", "000-17378", "", "000-17378", "000-17378", "000-17378", "000-17378", "", "", "", "", "", "", "", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "005-41585", "000-17378", "000-17378", "005-41585", "000-17378", "005-41585", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "005-41585", "000-17378", "000-17378", "000-17378", "000-17378", "005-41585", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "005-41585", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "005-41585", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "005-41585", "000-17378", "000-17378", "005-41585", "005-41585", "005-41585", "005-41585", "005-41585", "005-41585", "005-41585", "000-17378", "000-17378", "000-17378", "000-17378", "005-41585", "005-41585", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378", "000-17378"], "filmNumber": ["251039829", "25755024", "25744784", "25562614", "241300456", "241293787", "", "", "241280391", "", "", "", "241236672", "", "", "241187699", "", "241131265", "", "241089847", "241075069", "241006960", "24959355", "24852090", "24749082", "", "", "24592373", "24573244", "24550329", "24533094", "", "231481795", "231457921", "231432688", "", "", "", "", "231217919", "", "231216866", "", "", "", "", "", "", "231130515", "231129974", "", "", "231096833", "", "231090510", "", "231076982", "", "", "231058622", "231021287", "231012230", "", "231002640", "", "23990184", "", "23915821", "23915756", "23899119", "", "23833913", "23802449", "23802421", "23750448", "23730799", "23633806", "23628835", "23568441", "", "23544599", "", "23537710", "23531604", "", "", "221478933", "221467962", "", "221442619", "221426167", "", "", "", "", "", "", "", "", "", "221362097", "", "221359281", "", "", "221237247", "221236855", "211333231", "201235438", "201231074", "201224526", "201223016", "201020034", "20942733", "20573679", "20549291", "20543020", "19909155", "19702186", "19547912", "181230346", "181086209", "18919831", "18812367", "18710482", "171275938", "171135244", "17960400", "", "17884817", "17876402", "17769610", "17723039", "161898216", "161539844", "161354945", "151273226", "151122383", "151085767", "15742254", "15721204", "15712706", "", "15613050", "15557449", "", "", "", "141113818", "141101051", "141059617", "", "14929081", "14922194", "14695639", "14600566", "", "14555729", "", "131109832", "131099473", "13920329", "13915076", "13911287", "13707696", "13697584", "13638937", "13598430", "13555736", "121149013", "121143746", "121102160", "121092676", "12922103", "12914823", "12907056", "12833336", "12804924", "12705002", "12697251", "12618633", "12599102", "12558281", "111137071", "111090903", "", "11937629", "11935568", "11922954", "11910134", "11730001", "11697749", "11688485", "11556223", "101098525", "101082498", "101078628", "101071659", "101063963", "10908290", "10894599", "10744399", "10688794", "10604022", "10556965", "10517887", "10510904", "091265704", "091265659", "", "091223684", "091065442", "", "091023877", "091023342", "", "09941660", "", "09886804", "09693528", "09687381", "09687157", "09615529", "09553809", "081081597", "081072354", "08899497", "08860654", "", "08770339", "08691709", "08627250", "08624655", "08040150", "08602598", "", "08578318", "08559053", "071123089", "071117018", "07921578", "07702764", "07604600", "07560391", "", "07547928", "", "07532403", "", "061090827", "06905185", "06865749", "06698748", "06603848", "06561970", "", "06523721", "", "06521435", "", "051083548", "05895117", "", "05748298", "05685384", "05599527", "05555963", "", "", "", "", "", "", "", "041024448", "04861944", "04669581", "04570425", "04551100", "", "03895009", "03749506", "03745279", "03613651", "03607148", "03582761", "03531475", "03530954", "02856278", "02856277", "02785229", "02764514", "02678912", "02637005", "02579616", "02577223", "02573562", "02537302", "02521253", "1737852", "1660642", "1592695", "1570246", "1557715", "1552376", "1552373", "1552372", "1549109", "1544715", "1525149", "1518322", "782765", "751545", "749397", "741627", "740132", "726427", "723623", "707819", "664536", "662955", "655613", "651200", "570163", "547536", "547524", "547490", "547287", "547275", "534776", "519201", "518931", "99747934", "99747923", "99730290", "99730289", "99730288", "99719094", "99709882", "99699602", "99620272", "99553918", "99546265", "99520907", "99503049", "99502565", "98736012", "98736011", "98736010", "98736009", "97671589", "97620980", "97550228", "97533788", "97508760", "97507698", "97507697", "97502169", "96680351", "96646586"], "items": ["06b", "", "", "", "", "S-1,,2024-09-11 16:30:00", "", "", "", "", "", "", "", "", "", "", "", "1.01,2.03,3.02,9.01", "", "", "1.01,3.02,9.01", "", "1.01,2.03,3.02,9.01", "1.01,2.03,3.02,9.01", "", "", "", "", "", "5.02,8.01,9.01", "1.01,2.03,3.02,9.01", "", "", "06b", "1.01,2.03,3.02,9.01", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "1.01,2.03,3.02,9.01", "", "", "", "3.03,5.03,9.01", "", "", "", "1.01,3.02,9.01", "", "", "1.01,3.02,9.01", "", "", "", "1.01,3.02,9.01", "1.01,3.02,9.01", "", "", "1.01,3.02,9.01", "06b", "1.01,3.02,9.01", "1.01,3.02,9.01", "", "", "", "", "", "06b", "", "", "1.01,3.02,9.01", "", "", "", "4.02,9.01", "", "", "1.01,5.02,9.01", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "06b", "", "06b", "2.02,7.01,9.01", "8.01,9.01", "2.02,7.01,9.01", "1.01,7.01,9.01", "2.02,3.02,5.03,7.01,9.01", "2.02,7.01,9.01", "06b", "2.02,7.01,9.01", "2.02,7.01,9.01", "2.02,7.01", "4.01,9.01", "2.02,7.01,9.01", "2.02,7.01,9.01", "3.02,5.02,7.01,9.01", "2.02,7.01,9.01", "2.02,7.01,9.01", "1.02", "1.01,9.01", "", "1.01", "5.02", "1.01,9.01", "1.01,2.03,7.01,8.01,9.01", "", "", "", "3.02,5.02", "", "3.02,8.01,9.01", "5.02", "", "", "", "", "", "", "", "", "", "", "3.02,5.02,7.01,9.01", "", "", "", "", "", "", "", "", "", "", "", "", "3.02", "", "", "5.02,7.01,9.01", "", "", "", "", "", "", "", "", "", "3.02", "7.01,9.01", "", "", "", "", "", "", "", "", "", "1.01,8.01,9.01", "3.02", "", "1.01,9.01", "5.02", "", "", "06", "3.02", "", "", "3.02,9.01", "", "", "3.02,5.02", "", "", "", "", "06", "7.01,9.01", "3.02", "", "", "", "", "", "8.01,9.01", "", "", "", "", "", "", "", "3.02", "", "", "", "", "", "", "7.01,9.01", "", "", "", "06", "", "", "3.02,7.01,9.01", "", "", "", "", "", "", "", "", "4.01,9.01", "", "4.01,9.01", "", "", "", "5.02", "", "", "", "", "4.01,9.01", "", "4.01,9.01", "", "", "", "", "1.01,3.02,5.01,5.02,9.01", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "1,6", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "9", "4,7", "", "", "4,7", "", "", "1,2,5,7", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "1", "", "", "", "", "", "", "", "", "", "", "", "", "", "4"], "core_type": ["D", "XBRL", "NT 10-Q", "XBRL", "XBRL", "EFFECT", "CORRESP", "CORRESP", "XBRL", "CORRESP", "LETTER", "CORRESP", "XBRL", "LETTER", "CORRESP", "XBRL", "LETTER", "XBRL", "CORRESP", "XBRL", "XBRL", "XBRL", "XBRL", "XBRL", "XBRL", "LETTER", "CORRESP", "XBRL", "XBRL", "XBRL", "XBRL", "LETTER", "XBRL", "D", "XBRL", "CORRESP", "CORRESP", "CORRESP", "CORRESP", "XBRL", "CORRESP", "XBRL", "CORRESP", "CORRESP", "CORRESP", "CORRESP", "CORRESP", "CORRESP", "CERT", "8-A12B", "CORRESP", "CORRESP", "XBRL", "CORRESP", "XBRL", "LETTER", "XBRL", "3/A", "CORRESP", "XBRL", "XBRL", "XBRL", "LETTER", "XBRL", "CORRESP", "XBRL", "LETTER", "XBRL", "XBRL", "XBRL", "CORRESP", "XBRL", "D/A", "XBRL", "XBRL", "XBRL", "SC 13G", "SC 13G", "10-K", "LETTER", "D", "CORRESP", "10-12G/A", "XBRL", "LETTER", "CORRESP", "10-12G/A", "XBRL", "LETTER", "SC 13D", "8-K", "LETTER", "LETTER", "3", "3", "3", "3", "3", "3", "CORRESP", "10-12G/A", "CORRESP", "S-1/A", "LETTER", "LETTER", "10-12G", "S-1", "D", "REVOKED", "D/A", "8-K", "8-K", "8-K", "8-K", "8-K/A", "8-K", "D", "8-K", "8-K", "8-K", "8-K", "8-K", "8-K", "8-K", "8-K", "8-K", "8-K", "8-K", "3", "8-K", "8-K", "8-K", "8-K", "10-K", "10-Q", "10-Q", "8-K", "NT 10-Q", "8-K", "8-K", "10-Q", "NT 10-Q", "4/A", "10-K", "NT 10-K", "4", "3", "4", "10-Q", "NT 10-Q", "8-K", "4", "10-Q", "NT 10-Q", "10-Q", "10-K", "4", "NT 10-K", "3", "10-Q", "NT 10-Q", "10-Q", "NT 10-Q", "8-K", "10-Q", "NT 10-Q", "8-K", "10-K", "NT 10-K", "10-Q/A", "10-Q/A", "10-Q", "NT 10-Q", "10-Q/A", "10-Q", "NT 10-Q", "8-K", "8-K", "10-Q", "NT 10-Q", "10-K/A", "10-K", "NT 10-K", "10-Q/A", "10-Q", "4", "10-Q", "8-K/A", "8-K", "NT 10-Q", "8-K", "8-K", "10-Q", "10-K", "D", "8-K", "10-Q", "NT 10-Q", "8-K", "10-Q", "NT 10-Q", "8-K", "10-Q", "10-K", "NT 10-K", "144", "D", "8-K", "8-K", "4", "SC 13G/A", "10-Q", "4", "144", "8-K", "3", "SC 13D/A", "4", "10-Q", "10-Q", "NT 10-Q", "10-K", "8-K", "NT 10-K", "10QSB", "NT 10-Q", "10QSB", "SC 13D", "4", "8-K", "10QSB", "SC 13G", "10KSB/A", "REGDEX", "10KSB", "3", "8-K", "NT 10-K", "10QSB", "NT 10-Q", "10QSB", "10QSB", "10KSB", "NT 10-K", "CORRESP", "8-K/A", "LETTER", "8-K", "5", "10QSB", "10QSB", "8-K", "10QSB", "10KSB", "NT 10-K", "CORRESP", "8-K/A", "LETTER", "8-K", "5", "10QSB", "10QSB", "3", "8-K", "10QSB", "10KSB", "NT 10-K", "5", "5", "4/A", "4/A", "4/A", "4/A", "4/A", "10QSB", "10QSB", "10QSB", "10KSB", "NT 10-K", "4", "10QSB", "10QSB", "NT 10-Q", "10QSB", "NT 10-Q", "SC 13D/A", "10KSB", "NT 10-K", "SC 13D/A", "4", "SC 13D/A", "10QSB", "10QSB", "8-K", "10QSB", "NT 10-Q", "SC 13D/A", "10KSB", "NT 10-K", "10-Q", "10QSB", "SC 13D/A", "10QSB", "DEF 14A", "10QSB/A", "10QSB/A", "10QSB/A", "PRE 14A", "SC 13D/A", "PRE 14A", "10KSB", "8-K", "8-K/A", "DEF 14A", "PRE 14A", "8-K", "10QSB", "NT 10-Q", "8-K", "10QSB", "10QSB/A", "NT 10-K", "SC 13D/A", "10QSB", "4", "5", "4", "5", "5", "SC 13D/A", "10KSB", "NT 10-K", "SC 13D/A", "SC 13D/A", "SC 13D/A", "SC 13D/A", "SC 13D", "SC 13D", "SC 13D", "10QSB", "10QSB", "10QSB", "10KSB", "SC 13D/A", "SC 13D", "8-K", "10QSB", "10QSB", "10QSB", "10KSB", "10QSB", "10QSB", "10QSB", "10KSB/A", "10KSB", "10QSB", "10QSB", "10QSB", "10KSB", "8-K/A"], "size": [8954, 8406586, 40388, 10675472, 8408070, 2075, 13377, 9373, 16413531, 21814, 43053, 35450, 15871211, 49361, 72956, 16762097, 58687, 411806, 50270, 17159240, 439113, 6659506, 211157, 399776, 5999037, 56326, 40656, 14965480, 12158913, 407403, 212817, 51966, 21145601, 10713, 1254818, 13579, 40333, 14618, 41720, 17746533, 24827, 7051489, 12705, 22437, 14368, 21564, 13062, 15283, 29391, 23195, 14801, 40369, 218444, 104019, 17922666, 48300, 7999242, 4739, 121791, 21495127, 219072, 6544797, 45349, 219739, 101030, 16219072, 47931, 220674, 223255, 14995132, 80538, 298928, 10601, 208656, 207156, 5547700, 5845, 94745, 3273425, 40184, 10499, 59210, 4248511, 216189, 43588, 122271, 4248256, 218284, 41419, 131307, 40741, 53089, 53486, 11974, 10451, 3101, 4539, 9601, 5834, 102976, 4248700, 143860, 4962324, 61881, 64439, 5556576, 33968715, 9035, 172097, 6906, 157997, 24479, 564696, 46028, 750922, 679217, 6807, 621221, 427662, 413917, 19616, 310299, 321643, 35029, 290123, 103926, 12211, 72017, 3574, 12059, 12155, 20173, 141013, 2687300, 2255533, 909741, 15249, 12519, 325949, 13634, 3079540, 11679, 7013, 3472825, 10768, 5007, 3548, 7666, 2691231, 10642, 30725, 11781, 2818476, 10626, 2599519, 3003457, 5905, 10645, 3534, 4326611, 10669, 2719133, 10673, 13053, 2619282, 10647, 30775, 3143013, 10644, 2917840, 32104, 301110, 10660, 1337623, 300705, 10653, 13364, 20443, 1676948, 10661, 1618254, 497594, 10664, 1735171, 311977, 4617, 312580, 44801, 13355, 10630, 130648, 9622, 234410, 429715, 7243, 12908, 275002, 11058, 77203, 268788, 11109, 13768, 237245, 438153, 11101, 20111, 6919, 19070, 13201, 5103, 28902, 250607, 21137, 26235, 65173, 3993, 34458, 13154, 245415, 205001, 10994, 395432, 13801, 33841, 85497, 33470, 53197, 86319, 7797, 13245, 47455, 27924, 245878, 1960, 372587, 5088, 140280, 33471, 47837, 33477, 44658, 123607, 467197, 33487, 3503, 16384, 37504, 16373, 20160, 46044, 46453, 14067, 47864, 373880, 19129, 5324, 16657, 37194, 18196, 8317, 59027, 59953, 5550, 78221, 44415, 178213, 6363, 8101, 14474, 5072, 9346, 7152, 7146, 9769, 49554, 44686, 37923, 147621, 6883, 8759, 43096, 42321, 7849, 31817, 8151, 11785, 163508, 8168, 21144, 21173, 9353, 43053, 38342, 5006, 33459, 7973, 10186, 158506, 7989, 41480, 38812, 65021, 29372, 71626, 42271, 23740, 22515, 70372, 13799, 65790, 156950, 9305, 7040, 116086, 115563, 6928, 32164, 8145, 125076, 28126, 24244, 5572, 15417, 22106, 14273, 16822, 14277, 16246, 16821, 15252, 63074, 5454, 11279, 11075, 12532, 12371, 12246, 13428, 95108, 26948, 27690, 28213, 82557, 11646, 11630, 5186, 27966, 27735, 27344, 83851, 27186, 27461, 26306, 91176, 91109, 28777, 28961, 28676, 91585, 6183], "isXBRL": [0, 1, 0, 1, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 0, 1, 1, 1, 0, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1, 1, 0, 0, 0, 1, 0, 1, 0, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "isInlineXBRL": [0, 1, 0, 1, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 0, 1, 1, 1, 0, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "primaryDocument": ["xslFormDX01/primary_doc.xml", "form10-q.htm", "formnt10-q.htm", "form10-k.htm", "form10-q.htm", "xslEFFECTX01/primary_doc.xml", "filename1.htm", "filename1.htm", "forms-1a.htm", "filename1.htm", "filename1.pdf", "filename1.htm", "forms-1a.htm", "filename1.pdf", "filename1.htm", "forms-1a.htm", "filename1.pdf", "form8-k.htm", "filename1.htm", "forms-1a.htm", "form8-k.htm", "form10-q.htm", "form8-k.htm", "form8-k.htm", "form10-q.htm", "filename1.pdf", "filename1.htm", "forms-1a.htm", "form10-k.htm", "form8-k.htm", "form8-k.htm", "filename1.pdf", "forms-1a.htm", "xslFormDX01/primary_doc.xml", "form8-k.htm", "filename1.htm", "filename1.htm", "filename1.htm", "filename1.htm", "forms-1a.htm", "filename1.htm", "form10-q.htm", "filename1.htm", "filename1.htm", "filename1.htm", "filename1.htm", "filename1.htm", "filename1.htm", "VTRO080123.pdf", "form8-a12b.htm", "filename1.htm", "filename1.htm", "form8-k.htm", "filename1.htm", "forms-1a.htm", "filename1.pdf", "form8-k.htm", "xslF345X02/ownership.xml", "filename1.htm", "forms-1a.htm", "form8-k.htm", "form10-q.htm", "filename1.pdf", "form8-k.htm", "filename1.htm", "forms-1a.htm", "filename1.pdf", "form8-ka.htm", "form8-ka.htm", "forms-1a.htm", "filename1.htm", "form8-k.htm", "xslFormDX01/primary_doc.xml", "form8-k.htm", "form8-k.htm", "form10-q.htm", "zamora13gfile.txt", "formsc13g.htm", "form10-k.htm", "filename1.pdf", "xslFormDX01/primary_doc.xml", "filename1.htm", "form10-12ga.htm", "form8-k.htm", "filename1.pdf", "filename1.htm", "form10-12ga.htm", "form8-k.htm", "filename1.pdf", "formsc13d.htm", "form8-k.htm", "filename1.pdf", "filename1.pdf", "xslF345X02/ownership.xml", "xslF345X02/ownership.xml", "xslF345X02/ownership.xml", "xslF345X02/ownership.xml", "xslF345X02/ownership.xml", "xslF345X02/ownership.xml", "filename1.htm", "form10-12ga.htm", "filename1.htm", "forms-1a.htm", "filename1.pdf", "filename1.pdf", "form10-12g.htm", "forms-1.htm", "xslFormDX01/primary_doc.xml", "filename1.pdf", "xslFormDX01/primary_doc.xml", "vitro_8k.htm", "vd_8k.htm", "vitro_8k.htm", "vitro_8k.htm", "vitro_8kz.htm", "vitro_8k.htm", "xslFormDX01/primary_doc.xml", "vitro_8k.htm", "vitro_8k.htm", "vitro_8k.htm", "vitro_8k.htm", "vitro_8k.htm", "vitro_8k.htm", "vitro_8k.htm", "vitro_8k.htm", "vitro_8k.htm", "vitro_8k.htm", "vitro_8k.htm", "xslF345X02/primary_doc.xml", "vitro_8k.htm", "vitro_8k.htm", "vitro_8k.htm", "vitro_8k.htm", "vdi_10k.htm", "vitro_10q.htm", "vd_10q.htm", "vd_8k.htm", "vd_nt10q.htm", "vds_8k.htm", "f8k502shusterresign.htm", "f10q0115v3clean.htm", "f12b2510q0115.htm", "xslF345X03/primary_doc.xml", "vitro10k2014v10clean.htm", "f12b2510k1014.htm", "xslF345X03/primary_doc.xml", "xslF345X02/primary_doc.xml", "xslF345X03/primary_doc.xml", "vd_10q.htm", "f12b2510q0714.htm", "f8k502liter.htm", "xslF345X03/primary_doc.xml", "f10q0414v4.htm", "f12b2510q0414.htm", "f10q0114v3.htm", "f10k1013v8clean.htm", "xslF345X03/primary_doc.xml", "f12b2510k1013.htm", "xslF345X02/primary_doc.xml", "f10q0713final.htm", "f12b2510q0713.htm", "f10q0413v8.htm", "f12b2510q0413.htm", "f8k3020513.htm", "f10q013113finalv2.htm", "f12b2510q0113.htm", "f8k502vanhornresign.htm", "f10k1012v6.htm", "f12b2510k1012.htm", "f10qa0712.htm", "f10qa0712.htm", "f10q0712final.htm", "f12b2510q0712.htm", "vd_10qz.htm", "f10q0412v3.htm", "f12b2510q0412.htm", "f8k3020512.htm", "pressrelmarket050112.htm", "vd_10q.htm", "f12b2510q0112.htm", "vd_10kz.htm", "f10k1011v8clean.htm", "f12b2510k1011.htm", "vd_10qz.htm", "f10q0711v4clean.htm", "xslF345X03/primary_doc.xml", "f10q0411v3cleantja.htm", "f8ka101201poscillico.htm", "f8k302042711.htm", "f12b2510q0411.htm", "f8k101201poscillico.htm", "f8k502huebnerresign.htm", "f10q0111v5clean.htm", "f10k1010v8clean.htm", "xslFormDX01/primary_doc.xml", "f8k3020910offering.htm", "f10q0710v6clean.htm", "f12b2510q0710.htm", "f8k3020910doc.htm", "vitro10q0410v6clean.htm", "f12b2510q0410.htm", "f8k502302.htm", "f10q0110v6clean.htm", "f10k1009v12clean.htm", "f12b2510k1009.htm", "sordeletform144.htm", "xslFormDX01/primary_doc.xml", "f8k701proffering.htm", "f8k302.htm", "xslF345X03/primary_doc.xml", "sch13gagibbs.htm", "f10q0709v4clean.htm", "xslF345X03/primary_doc.xml", "form144musick.htm", "f8k101ameriprise.htm", "xslF345X02/primary_doc.xml", "sch13da5musick.htm", "xslF345X03/primary_doc.xml", "f10q0409v5.htm", "f10q0109v4clean.htm", "f12b2510q0109.htm", "form10kv5clean.htm", "rrd233619.htm", "rrd231304.htm", "vitro73108.txt", "rrd218810.htm", "vitro43008.txt", "sch13dgibbs.htm", "xslF345X02/primary_doc.xml", "rrd203657.htm", "vitro13108.txt", "sch13ggibbs.htm", "vitro10ksba.htm", "9999999997-08-006772.paper", "vitro10ksbver3.htm", "xslF345X02/primary_doc.xml", "rrd193907.htm", "rrd192504.htm", "vitro707.txt", "rrd172574.htm", "vitro407.txt", "vitro1312007.htm", "vitro10312006.htm", "rrd144505.htm", "filename1.txt", "rrd143842.htm", "filename1.pdf", "rrd142664.htm", "xslF345X02/rrd139354.xml", "vitrodiag706.txt", "vitro406.txt", "vitro8k5192006.htm", "vitro106.txt", "d32376e10ksb.htm", "d32377nt10vk.htm", "filename1.htm", "d31949e8vkza.htm", "filename1.pdf", "d31877e8vk.htm", "xslF345X02/form5posillico_ex.xml", "vitro10qsb073105.txt", "vitro4302005.txt", "xslF345X02/form3posillico_ex.xml", "vitro8k.txt", "vitro1312005.txt", "vitro10ksb103104.txt", "vitront.txt", "xslF345X02/form5jrmt_ex.xml", "xslF345X02/form5jrm_ex.xml", "xslF345X02/form4ajrmt_ex.xml", "xslF345X02/form4ajrm203_ex.xml", "xslF345X02/form4ajrm21302_ex.xml", "xslF345X02/form4ajrm301_ex.xml", "xslF345X02/form4ajrm_ex.xml", "vitro10qsb073104.txt", "vitro404.txt", "vitro1312004.txt", "vitro1003.txt", "vitro12b25.txt", "xslF345X02/musickform4_ex.xml", "vitro10qsb073103.txt", "vitro4302003.txt", "vitro12b25_6122003.txt", "vitro10qsb_3242003.txt", "vitro12b25_3142003.txt", "vitromusick13d4a_2262003.txt", "vitro10ksb_1292003.txt", "vitro12b25_1272003.txt", "wwc13da3_12112002.txt", "f4wwwvitro_12122002.txt", "vitro13dahansen_10032002.txt", "vitro10qsb_9162002.txt", "vitro10qsb_6142002.txt", "vitro8k_5072002.txt", "vitro10qsb_3202002.txt", "vitro12b25_3152002.txt", "vitro13damus_3082002.txt", "vitro10ksb_1282002.txt", "vitro12b25_1282002.txt", "vitro10q_09142001.txt", "vitro10qsb_06132001.txt", "vitro13da3312001.txt", "0001.txt", "0001.txt", "0001.txt", "0001.txt", "0001.txt", "0001.txt", "0001.txt", "0001.txt", "0001.txt", "0001.txt", "0001.txt", "0001.txt", "0001.txt", "0001.txt", "0001.txt", "0001.txt", "0001.txt", "0001.txt", "0001.txt", "0001.txt", "0001.txt", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "primaryDocDescription": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "NYSE AMERICAN CERTIFICATION", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "ZAMORA SCHEDULE 13G", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "CURRENT EVENT REPORT - FORM 8-K", "CURRENT EVENT REPORT - FORM 8-K", "VITRO DIAGNOSTICS, INC.", "VITRO DIAGNOSTICS, INC.", "VITRO DIAGNOSTICS, INC.", "VITRO DIAGNOSTICS, INC.", "", "VITRO DIAGNOSTICS, INC.", "VITRO DIAGNOSTICS, INC.", "VITRO DIAGNOSTICS, INC.", "VITRO DIAGNOSTICS, INC.", "VITRO DIAGNOSTICS, INC.", "VITRO DIAGNOSTICS, INC.", "VITRO DIAGNOSTICS, INC.", "VITRO DIAGNOSTICS, INC.", "VITRO DIAGNOSTICS, INC.", "VITRO DIAGNOSTICS, INC.", "VITRO DIAGNOSTICS, INC.", "PRIMARY DOCUMENT", "VITRO DIAGNOSTICS, INC.", "UNITED STATES", "VITRO DIAGNOSTICS, INC.", "VITRO DIAGNOSTICS, INC.", "FORM 10-K", "FORM 10-Q", "FORM 10-Q", "FORM 8-K", "NOTIFICATION OF LATE 10-Q FILING", "FORM 8-K", "", "", "", "PRIMARY DOCUMENT", "", "", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "PRIMARY DOCUMENT", "FORM 10-Q", "", "", "PRIMARY DOCUMENT", "VITRO DIAGNOSTICS, INC. - QUARTERLY REPORT FOR THE PERIOD ENDED APRIL 30, 2014", "", "VITRO DIAGNOSTICS INC. - QUARTERLY REPORT FOR THE PERIOD ENDED JANUARY 31, 2014", "VIRTO DIAGNOSTICS, INC. - ANNUAL REPORT FOR THE YEAR ENDED OCTOBER 31, 2013", "PRIMARY DOCUMENT", "", "PRIMARY DOCUMENT", "VITRO DIAGNOSTICS INC. - QUARTERLY REPORT FOR THE PERIOD ENDED JULY 31, 2013", "", "VITRO DIAGNOSTICS, INC. - QUARTERLY REPORT FOR THE PERIOD ENDED APRIL 30, 2013", "", "", "VITRO DIAGNOSTICS, INC. - QUARTERLY REPORT FOR JANUARY 31, 2013", "", "", "VITRO DIAGNOSTICS, INC. - ANNUAL REPORT OF FORM 10-K FOR OCTOBER 31, 2012", "", "VITRO DIAGNOSTICS, INC. - AMENDED QUARTERLY REPORT FOR JULY 31, 2012", "", "", "", "CONVERTED BY EDGARWIZ", "", "", "", "", "VITRO DIAGNOSTICS 10-Q", "", "FORM 10-KSB  (00341691.DOC;2)", "", "", "CONVERTED BY EDGARWIZ", "", "PRIMARY DOCUMENT", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "PRIMARY DOCUMENT", "", "", "PRIMARY DOCUMENT", "", "", "PRIMARY DOCUMENT", "", "PRIMARY DOCUMENT", "", "", "", "", "", "", "FORM 10-QSB  (7/31/08)", "", "FORM 10-QSB  (4/30/2008)", "", "PRIMARY DOCUMENT", "", "FORM 10-QSB  (1/31/2008)", "", "PERIOD ENDED OCTOBER 31, 2007", "AUTO-GENERATED PAPER DOCUMENT", "PERIOD ENDED OCTOBER 31, 2007", "PRIMARY DOCUMENT", "", "", "10QSB", "", "FORM 10-Q<PERSON>  (4-30-2007)", "FORM 10-Q<PERSON>  (1-31-2007)", "FORM 10-K<PERSON>  (10-31-2006)", "", "", "", "", "", "FORM 5", "10QSB", "10QSB", "FORM 8-K  (5-19-2006)", "10QSB", "FORM 10-KSB", "NOTIFICATION OF LATE FILING FORM 10-K", "", "AMENDMENT TO FORM 8-K", "", "FORM 8-K", "", "PERIOD ENDED 07-31-05", "FORM 10-Q<PERSON>  (4-30-2005)", "FORM 3", "FORM 8-K", "FORM 10-Q<PERSON>  (1-31-2005)", "FORM 10-K<PERSON>  (10-31-2004)", "NT 10-K", "", "", "", "", "", "", "", "FORM 10-Q<PERSON>  (7-31-2004)", "10QSB", "FORM 10-Q<PERSON> (1-31-2004)", "10KSB", "FORM 12B25", "", "FORM 10-Q<PERSON>  (7-31-2003)", "FORM 10-QSB", "FORM 12B25", "QUARTERLY REPORT FOR PERIOD ENDED 1/31/2003", "FORM 12B25", "AMENDMENT #4 TO FORM 13D", "ANNUAL REPORT", "FORM 12B25", "AMENDMENT #3 TO FORM 13D", "FORM 4", "SCHEDULE 13D AMENDMENT", "QUARTERLY REPORT", "QUARTERLY REPORT FOR PERIOD ENDED 4/30/2002", "FORM 8-K FOR REPORT OF CHANGE IN CONTROL", "QUARTERLY REPORT FOR PERIOD ENDED 1/31/2002", "NOTIFICATION OF LATE FILING OF FORM 10QSB", "AMENDMENT #3 TO SCHEDULE 13D", "ANNUAL REPORT FOR YEAR ENDED 10/31/2001", "NOTIFICATION FOR FORM 10KSB", "QUARTERLY REPORT FOR PERIOD ENDED 7-31-2001", "VITRO DIAGNOSTICS, INC. QUARTERLY REPORT", "SCHEDULE 13D/A 3/31/2001", "10QSB FOR QUARTER ENDED JANUARY 31, 2001", "DEFINITIVE PROXY STATEMENT", "10QSB/A FOR QUARTER ENDED JULY 31, 2000.", "10QSB/A FOR QUARTER ENDED APRIL 30, 2000.", "10QSB/A #2 FOR QUARTER ENDED JANUARY 31, 2000.", "PRELIMINARY INFORMATION STATEMENT", "AMENDMENT NO. 3 TO SCHEDULE 13D", "PRELIMINARY INFORMATION STATEMENT", "ANNUAL REPORT FOR YEAR ENDED OCTOBER 31, 2000.", "8-K FOR EVENT ON DECEMBER 1, 2000", "8-K/A FOR EVENT ON OCTOBER 9, 2000", "DEFINITIVE PROXY STATEMENT", "PRELIMINARY INFORMATION STATEMENT", "8-K FOR EVENT ON OCTOBER 9, 2000", "10QSB FOR QUARTER ENDED JULY 31, 2000.", "NT 10-Q FOR QUARTER ENDED JULY 31, 2000.", "8-K DATED AUGUST 7, 2000", "10QSB FOR QUARTER ENDED APRIL 30, 2000.", "10QSB/A FOR QUARTER ENDED JANUARY 31, 2000.", "", "SCHEDULE 13D/A", "", "", "", "", "", "", "SCHEDULE 13D/A", "", "", "", "", "", "", "", "", "SCHEDULE 13D", "", "", "", "", "SCHEDULE 13D/A", "SC 13D", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]}, "files": []}}