{"cik": "**********", "entityType": "other", "sic": "", "sicDescription": "", "ownerOrg": "", "insiderTransactionForOwnerExists": 0, "insiderTransactionForIssuerExists": 0, "name": "Nationwide PPVUL Separate Account 7", "tickers": [], "exchanges": [], "ein": "*********", "lei": null, "description": "", "website": "", "investorWebsite": "", "category": "", "fiscalYearEnd": "1231", "stateOfIncorporation": "OH", "stateOfIncorporationDescription": "OH", "addresses": {"mailing": {"street1": "ONE NATIONWIDE PLAZA", "street2": null, "city": "COLUMBUS", "stateOrCountry": "OH", "zipCode": "43215", "stateOrCountryDescription": "OH", "isForeignLocation": 0, "foreignStateTerritory": null, "country": null, "countryCode": null}, "business": {"street1": "ONE NATIONWIDE PLAZA", "street2": null, "city": "COLUMBUS", "stateOrCountry": "OH", "zipCode": "43215", "stateOrCountryDescription": "OH", "isForeignLocation": null, "foreignStateTerritory": null, "country": null, "countryCode": null}}, "phone": "************", "flags": "", "formerNames": [], "filings": {"recent": {"accessionNumber": ["**********-25-000006", "**********-25-000005", "**********-25-000003", "**********-25-000002", "**********-25-000001", "**********-24-000012", "**********-24-000011", "**********-24-000010", "**********-24-000009", "**********-24-000008", "**********-24-000007", "**********-24-000006", "**********-24-000005", "**********-24-000004", "**********-24-000003", "**********-24-000002", "**********-24-000001", "**********-23-000003", "**********-23-000002", "**********-23-000001", "0001135856-23-000064", "0001135856-23-000056", "0001804211-23-000005", "0001135856-23-000044", "0001135856-23-000036", "0001804435-23-000004", "0001135856-23-000024", "0001135856-23-000016", "0001135856-23-000008", "**********-22-000002", "0001804211-22-000011", "**********-22-000001"], "filingDate": ["2025-05-19", "2025-04-18", "2025-03-19", "2025-02-19", "2025-01-15", "2024-12-26", "2024-11-20", "2024-10-15", "2024-09-18", "2024-08-16", "2024-07-19", "2024-06-18", "2024-05-17", "2024-04-17", "2024-03-20", "2024-02-16", "2024-01-17", "2023-12-19", "2023-11-16", "2023-10-25", "2023-09-19", "2023-08-22", "2023-07-21", "2023-06-20", "2023-05-22", "2023-04-21", "2023-03-20", "2023-02-23", "2023-01-23", "2022-12-16", "2022-11-16", "2022-10-31"], "reportDate": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "acceptanceDateTime": ["2025-05-19T12:50:06.000Z", "2025-04-18T13:37:40.000Z", "2025-03-19T14:37:58.000Z", "2025-02-19T21:23:23.000Z", "2025-01-15T21:50:12.000Z", "2024-12-26T14:57:26.000Z", "2024-11-20T21:40:19.000Z", "2024-10-15T18:43:17.000Z", "2024-09-18T19:54:25.000Z", "2024-08-16T20:22:56.000Z", "2024-07-19T13:11:52.000Z", "2024-06-18T15:13:26.000Z", "2024-05-17T20:53:51.000Z", "2024-04-17T19:41:54.000Z", "2024-03-20T15:15:52.000Z", "2024-02-16T19:52:34.000Z", "2024-01-17T21:58:16.000Z", "2023-12-19T16:20:51.000Z", "2023-11-16T18:17:39.000Z", "2023-10-25T20:40:34.000Z", "2023-09-19T19:20:28.000Z", "2023-08-22T14:10:02.000Z", "2023-07-21T14:51:27.000Z", "2023-06-20T13:59:09.000Z", "2023-05-22T18:28:29.000Z", "2023-04-21T18:12:40.000Z", "2023-03-20T18:06:59.000Z", "2023-02-23T13:51:10.000Z", "2023-01-23T14:17:04.000Z", "2022-12-16T18:32:19.000Z", "2022-11-16T13:55:37.000Z", "2022-10-31T16:53:11.000Z"], "act": ["33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33"], "form": ["D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D"], "fileNumber": ["021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317", "021-463317"], "filmNumber": ["25962816", "25849338", "25751184", "25640463", "25533455", "241577772", "241480938", "241371416", "241307364", "241216998", "241126310", "241050557", "24960785", "24850750", "24766858", "24647671", "24539130", "231496046", "231413786", "231346506", "231263497", "231191297", "231101556", "231023770", "23943963", "23835888", "23745867", "23656234", "23542719", "221467479", "221393127", "221345029"], "items": ["06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7"], "core_type": ["D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D"], "size": [7183, 7183, 7190, 7190, 7190, 7191, 7191, 7191, 7191, 7191, 7191, 7191, 7190, 7190, 7190, 7190, 7190, 7190, 7157, 7157, 7157, 7157, 7157, 7157, 7147, 7156, 7156, 7156, 7156, 7157, 7157, 7062], "isXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "isInlineXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "primaryDocument": ["xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml"], "primaryDocDescription": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]}, "files": []}}