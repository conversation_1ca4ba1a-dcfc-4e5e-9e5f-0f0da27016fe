{"cik": "0002000723", "entityType": "other", "sic": "", "sicDescription": "", "ownerOrg": "", "insiderTransactionForOwnerExists": 0, "insiderTransactionForIssuerExists": 0, "name": "Uni Express Inc.", "tickers": [], "exchanges": [], "ein": "000000000", "lei": null, "description": "", "website": "", "investorWebsite": "", "category": "", "fiscalYearEnd": "0430", "stateOfIncorporation": "A1", "stateOfIncorporationDescription": "British Columbia, Canada", "addresses": {"mailing": {"street1": "10851 SHELBRIDGE WAY #100", "street2": null, "city": "RICHMOND", "stateOrCountry": "A1", "zipCode": "V6X 2W8", "stateOrCountryDescription": "British Columbia, Canada", "isForeignLocation": 0, "foreignStateTerritory": null, "country": null, "countryCode": null}, "business": {"street1": "10851 SHELBRIDGE WAY #100", "street2": null, "city": "RICHMOND", "stateOrCountry": "A1", "zipCode": "V6X 2W8", "stateOrCountryDescription": "British Columbia, Canada", "isForeignLocation": null, "foreignStateTerritory": null, "country": null, "countryCode": null}}, "phone": "************", "flags": "", "formerNames": [], "filings": {"recent": {"accessionNumber": ["0002000723-25-000003", "0002000723-24-000003", "0002000723-24-000002", "0002000723-24-000001", "0002000723-23-000001"], "filingDate": ["2025-06-20", "2024-08-22", "2024-07-23", "2024-03-21", "2023-12-15"], "reportDate": ["", "", "", "", ""], "acceptanceDateTime": ["2025-06-20T16:35:27.000Z", "2024-08-22T10:02:20.000Z", "2024-07-23T13:00:08.000Z", "2024-03-21T11:22:24.000Z", "2023-12-14T19:09:49.000Z"], "act": ["33", "33", "33", "33", "33"], "form": ["D", "D/A", "D", "D", "D"], "fileNumber": ["021-549490", "021-519498", "021-519498", "021-508203", "021-499767"], "filmNumber": ["251061955", "241230437", "241133667", "24769996", "231488445"], "items": ["06b", "06b", "06b", "06b", "06b"], "core_type": ["D", "D/A", "D", "D", "D"], "size": [13119, 11546, 11451, 11679, 10794], "isXBRL": [0, 0, 0, 0, 0], "isInlineXBRL": [0, 0, 0, 0, 0], "primaryDocument": ["xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml"], "primaryDocDescription": ["", "", "", "", ""]}, "files": []}}