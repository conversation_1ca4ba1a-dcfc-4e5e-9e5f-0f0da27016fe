{"cik": "**********", "entityType": "other", "sic": "", "sicDescription": "", "ownerOrg": "", "insiderTransactionForOwnerExists": 0, "insiderTransactionForIssuerExists": 0, "name": "Nationwide Private Placement Variable Account - E", "tickers": [], "exchanges": [], "ein": "*********", "lei": null, "description": "", "website": "", "investorWebsite": "", "category": "", "fiscalYearEnd": "1231", "stateOfIncorporation": "OH", "stateOfIncorporationDescription": "OH", "addresses": {"mailing": {"street1": "ONE NATIONWIDE PLAZA", "street2": null, "city": "COLUMBUS", "stateOrCountry": "OH", "zipCode": "43215", "stateOrCountryDescription": "OH", "isForeignLocation": 0, "foreignStateTerritory": null, "country": null, "countryCode": null}, "business": {"street1": "ONE NATIONWIDE PLAZA", "street2": null, "city": "COLUMBUS", "stateOrCountry": "OH", "zipCode": "43215", "stateOrCountryDescription": "OH", "isForeignLocation": null, "foreignStateTerritory": null, "country": null, "countryCode": null}}, "phone": "************", "flags": "", "formerNames": [], "filings": {"recent": {"accessionNumber": ["**********-25-000006", "**********-25-000005", "**********-25-000003", "**********-25-000002", "**********-25-000001", "**********-24-000012", "**********-24-000011", "**********-24-000010", "**********-24-000009", "**********-24-000008", "**********-24-000007", "**********-24-000006", "**********-24-000005", "**********-24-000004", "**********-24-000003", "**********-24-000002", "**********-24-000001", "**********-23-000003", "**********-23-000002", "**********-23-000001", "0001135856-23-000059", "0001135856-23-000050", "0001135856-23-000047", "0001135856-23-000039", "0001135856-23-000031", "0001135856-23-000027", "0001135856-23-000019", "0001135856-23-000011", "0001135856-23-000003", "**********-22-000007", "0001135856-22-000035", "0001135856-22-000028", "0001135856-22-000021", "0001135856-22-000016", "0001135856-22-000009", "**********-22-000006", "**********-22-000005", "**********-22-000004", "**********-22-000003", "**********-22-000002", "**********-22-000001", "**********-21-000012", "**********-21-000011", "**********-21-000010", "**********-21-000009", "**********-21-000008", "**********-21-000007", "**********-21-000006", "**********-21-000005", "**********-21-000004", "**********-21-000003", "**********-21-000002", "**********-21-000001", "**********-20-000009", "**********-20-000008", "**********-20-000007", "**********-20-000006", "**********-20-000005", "**********-20-000004", "**********-20-000003", "**********-20-000002", "**********-20-000001", "0001343394-20-000005", "0001343394-20-000003", "**********-19-000005", "**********-19-000004", "**********-19-000003", "**********-19-000002", "**********-18-000004", "**********-18-000003", "**********-18-000002", "**********-18-000001", "**********-17-000001"], "filingDate": ["2025-05-19", "2025-04-18", "2025-03-19", "2025-02-19", "2025-01-15", "2024-12-26", "2024-11-20", "2024-10-15", "2024-09-18", "2024-08-16", "2024-07-19", "2024-06-18", "2024-05-17", "2024-04-17", "2024-03-20", "2024-02-16", "2024-01-17", "2023-12-19", "2023-11-16", "2023-10-25", "2023-09-19", "2023-08-22", "2023-07-21", "2023-06-20", "2023-05-22", "2023-04-21", "2023-03-20", "2023-02-23", "2023-01-23", "2022-12-16", "2022-11-16", "2022-10-31", "2022-09-21", "2022-08-22", "2022-07-19", "2022-06-27", "2022-05-27", "2022-04-25", "2022-03-22", "2022-02-23", "2022-01-25", "2021-12-21", "2021-11-22", "2021-10-27", "2021-09-17", "2021-08-26", "2021-07-23", "2021-06-24", "2021-05-26", "2021-04-21", "2021-03-24", "2021-02-23", "2021-01-19", "2020-12-18", "2020-11-20", "2020-10-21", "2020-09-18", "2020-08-24", "2020-07-17", "2020-06-22", "2020-05-21", "2020-04-20", "2020-03-20", "2020-02-26", "2019-07-16", "2019-04-16", "2019-02-26", "2019-01-16", "2018-10-16", "2018-08-17", "2018-02-20", "2018-01-12", "2017-03-08"], "reportDate": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "acceptanceDateTime": ["2025-05-19T12:32:03.000Z", "2025-04-18T13:26:31.000Z", "2025-03-19T14:22:55.000Z", "2025-02-19T21:04:52.000Z", "2025-01-15T21:29:14.000Z", "2024-12-26T14:30:47.000Z", "2024-11-20T21:25:45.000Z", "2024-10-15T18:30:47.000Z", "2024-09-18T19:39:53.000Z", "2024-08-16T19:39:09.000Z", "2024-07-19T12:56:58.000Z", "2024-06-18T14:31:52.000Z", "2024-05-17T20:39:06.000Z", "2024-04-17T19:23:51.000Z", "2024-03-20T14:38:45.000Z", "2024-02-16T19:38:37.000Z", "2024-01-17T21:41:34.000Z", "2023-12-19T15:58:12.000Z", "2023-11-16T17:58:09.000Z", "2023-10-25T17:55:30.000Z", "2023-09-19T18:36:02.000Z", "2023-08-22T13:38:29.000Z", "2023-07-21T13:59:09.000Z", "2023-06-20T13:20:41.000Z", "2023-05-22T17:57:09.000Z", "2023-04-21T16:09:18.000Z", "2023-03-20T16:44:13.000Z", "2023-02-23T13:22:07.000Z", "2023-01-23T13:50:33.000Z", "2022-12-16T18:08:22.000Z", "2022-11-16T13:18:54.000Z", "2022-10-31T14:25:39.000Z", "2022-09-21T11:51:58.000Z", "2022-08-22T12:51:38.000Z", "2022-07-19T11:59:44.000Z", "2022-06-27T15:36:29.000Z", "2022-05-27T19:29:35.000Z", "2022-04-25T14:47:01.000Z", "2022-03-22T16:58:48.000Z", "2022-02-23T18:18:52.000Z", "2022-01-25T12:53:05.000Z", "2021-12-21T16:46:14.000Z", "2021-11-22T18:27:40.000Z", "2021-10-27T17:17:04.000Z", "2021-09-17T14:08:44.000Z", "2021-08-26T20:13:55.000Z", "2021-07-23T13:02:20.000Z", "2021-06-24T14:35:43.000Z", "2021-05-26T15:07:25.000Z", "2021-04-21T15:20:55.000Z", "2021-03-24T18:29:36.000Z", "2021-02-23T14:56:57.000Z", "2021-01-19T16:06:50.000Z", "2020-12-18T16:44:33.000Z", "2020-11-20T20:02:48.000Z", "2020-10-21T19:11:32.000Z", "2020-09-18T14:10:49.000Z", "2020-08-24T13:41:05.000Z", "2020-07-17T15:18:17.000Z", "2020-06-22T17:47:08.000Z", "2020-05-21T13:23:22.000Z", "2020-04-20T19:55:11.000Z", "2020-03-20T16:45:43.000Z", "2020-02-26T16:58:32.000Z", "2019-07-16T14:39:13.000Z", "2019-04-16T13:11:44.000Z", "2019-02-26T11:40:22.000Z", "2019-01-16T14:34:06.000Z", "2018-10-16T14:29:19.000Z", "2018-08-17T13:01:59.000Z", "2018-02-20T13:46:43.000Z", "2018-01-12T12:26:20.000Z", "2017-03-08T14:11:37.000Z"], "act": ["33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33"], "form": ["D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D"], "fileNumber": ["021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-303083", "021-282005"], "filmNumber": ["25962707", "25849315", "25751143", "25639865", "25533236", "241577150", "241480731", "241371360", "241307314", "241216350", "241126172", "241050467", "24960513", "24850635", "24766698", "24647598", "24538976", "231495930", "231413689", "231345046", "231263342", "231191136", "231101294", "231023717", "23943789", "23835343", "23745524", "23656039", "23542627", "221467381", "221392992", "221344214", "221255020", "221182311", "221090413", "221042338", "22976553", "22847825", "22758275", "22662324", "22551135", "211507902", "211431256", "211352053", "211259422", "211212513", "211109208", "211041204", "21963791", "21840064", "21767883", "21663426", "21534560", "201398599", "201332040", "201250445", "201182924", "201124936", "201033092", "20978112", "20900225", "20802548", "20731153", "20654040", "19956415", "19750020", "19631583", "19528399", "181123713", "181024814", "18623203", "18524680", "17674020"], "items": ["06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1,3C.7", "06b,3C,3C.1"], "core_type": ["D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D/A", "D", "D"], "size": [7387, 7387, 7394, 7394, 7394, 7395, 7395, 7395, 7395, 7395, 7395, 7395, 7394, 7394, 7394, 7394, 7394, 7395, 7362, 7362, 7362, 7362, 7362, 7362, 7361, 7361, 7361, 7361, 7361, 7362, 7362, 7362, 7362, 7362, 7362, 7362, 7361, 7361, 7361, 7361, 7361, 7362, 7362, 7362, 7362, 7362, 7362, 7362, 7361, 7361, 7361, 7361, 7361, 7362, 7362, 7362, 7362, 7362, 7362, 7361, 7367, 7365, 7367, 7368, 7366, 7366, 7366, 7366, 7367, 7367, 7365, 7270, 12244], "isXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "isInlineXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "primaryDocument": ["xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml"], "primaryDocDescription": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]}, "files": []}}