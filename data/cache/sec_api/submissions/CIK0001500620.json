{"cik": "0001500620", "entityType": "other", "sic": "", "sicDescription": "", "ownerOrg": "", "insiderTransactionForOwnerExists": 0, "insiderTransactionForIssuerExists": 0, "name": "Bravada Gold Corp", "tickers": ["BGAVF"], "exchanges": ["OTC"], "ein": "000000000", "lei": null, "description": "", "website": "", "investorWebsite": "", "category": "", "fiscalYearEnd": "0731", "stateOfIncorporation": "A1", "stateOfIncorporationDescription": "British Columbia, Canada", "addresses": {"mailing": {"street1": "1100 - 1199 WEST HASTINGS STREET", "street2": null, "city": "VANCOUVER", "stateOrCountry": "A1", "zipCode": "V6E 3T5", "stateOrCountryDescription": "British Columbia, Canada", "isForeignLocation": 0, "foreignStateTerritory": null, "country": null, "countryCode": null}, "business": {"street1": "1100 - 1199 WEST HASTINGS STREET", "street2": null, "city": "VANCOUVER", "stateOrCountry": "A1", "zipCode": "V6E 3T5", "stateOrCountryDescription": "British Columbia, Canada", "isForeignLocation": null, "foreignStateTerritory": null, "country": null, "countryCode": null}}, "phone": "(*************", "flags": "", "formerNames": [], "filings": {"recent": {"accessionNumber": ["0001500620-25-000002", "0001500620-23-000001", "0001500620-22-000001", "0001500620-21-000001", "0001500620-20-000002", "0001500620-20-000001", "0001500620-19-000002", "0001500620-19-000001", "0001500620-18-000001", "0001500620-17-000003", "0001500620-17-000002", "0001500620-17-000001", "0001500620-16-000004", "0001500620-16-000003", "0001500620-16-000002", "0001500620-15-000001", "0001500620-13-000001", "0001500620-11-000003", "0001500620-11-000002", "0001500620-11-000001", "0001500620-10-000001"], "filingDate": ["2025-06-11", "2023-12-20", "2022-05-10", "2021-10-05", "2020-09-11", "2020-06-19", "2019-07-26", "2019-05-28", "2018-07-31", "2017-11-27", "2017-03-01", "2017-02-06", "2016-07-14", "2016-04-22", "2016-04-07", "2015-09-18", "2013-10-21", "2011-11-18", "2011-05-26", "2011-05-26", "2010-09-02"], "reportDate": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "acceptanceDateTime": ["2025-06-11T15:16:22.000Z", "2023-12-20T14:22:19.000Z", "2022-05-09T18:04:07.000Z", "2021-10-05T17:20:30.000Z", "2020-09-10T19:28:09.000Z", "2020-06-18T20:23:35.000Z", "2019-07-26T14:06:19.000Z", "2019-05-28T13:38:41.000Z", "2018-07-31T13:57:10.000Z", "2017-11-27T16:33:44.000Z", "2017-03-01T14:47:42.000Z", "2017-02-03T18:00:34.000Z", "2016-07-14T15:51:47.000Z", "2016-04-22T16:54:34.000Z", "2016-04-06T20:16:37.000Z", "2015-09-18T14:25:24.000Z", "2013-10-21T17:09:27.000Z", "2011-11-18T15:57:06.000Z", "2011-05-25T18:25:39.000Z", "2011-05-25T17:50:14.000Z", "2010-09-02T15:25:46.000Z"], "act": ["33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33", "33"], "form": ["D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D"], "fileNumber": ["021-548666", "021-500275", "021-445063", "021-415796", "021-375275", "021-369293", "021-345163", "021-340495", "021-318151", "021-299518", "021-281570", "021-280045", "021-267014", "021-261914", "021-260793", "021-247897", "021-204978", "021-168980", "021-160351", "021-160346", "021-147250"], "filmNumber": ["251039756", "231501002", "22906504", "211307755", "201169631", "20973961", "19977263", "19856958", "18980600", "171223219", "17653441", "17573426", "161767476", "161587143", "161558637", "151115195", "131161964", "111216121", "11872166", "11872051", "101054902"], "items": ["06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06b", "06c", "06c", "06b", "06b", "06b", "06c", "06b", "06", "06", "06", "06"], "core_type": ["D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D", "D"], "size": [11120, 11121, 11104, 11101, 10806, 10879, 10877, 10866, 10804, 10804, 10798, 10796, 10787, 10010, 10748, 10791, 10720, 12212, 12262, 12266, 10208], "isXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "isInlineXBRL": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "primaryDocument": ["xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml", "xslFormDX01/primary_doc.xml"], "primaryDocDescription": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]}, "files": []}}