# Setup Instructions for SEC Form D Analysis System - Phase 3

This document provides setup instructions for the SEC Form D Analysis System with Phase 3 Advanced LLM Integration features.

## Prerequisites

- Python 3.10 or higher
- macOS (for MLX support)
- At least 16GB RAM (32GB recommended for Mixtral-8x7B)
- At least 10GB free disk space for models and data

## Installation

### 1. <PERSON>lone the Repository

```bash
git clone https://github.com/yourusername/private-signals.git
cd private-signals
```

### 2. Create and Activate Virtual Environment

```bash
python -m venv .venv
source .venv/bin/activate
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

If `requirements.txt` doesn't exist or needs updating, create it with the following content:

```
# Core dependencies
requests>=2.28.0
beautifulsoup4>=4.11.0
numpy>=1.23.0
pandas>=1.5.0
matplotlib>=3.5.0
seaborn>=0.12.0

# MLX for Apple Silicon
mlx>=0.0.5
mlx-lm>=0.0.3

# Web scraping and data processing
lxml>=4.9.0
html5lib>=1.1
feedparser>=6.0.0

# Database
psycopg2-binary>=2.9.0
sqlalchemy>=2.0.0

# Email
sendgrid>=6.9.0

# Utilities
tqdm>=4.64.0
python-dotenv>=0.20.0
```

Install with:

```bash
pip install -r requirements.txt
```

### 4. Install MLX and MLX-LM

MLX is required for running the Mixtral model locally on Apple Silicon:

```bash
pip install mlx mlx-lm
```

### 5. Download the Mixtral Model

The system will automatically download the model on first run, but you can pre-download it:

```bash
mkdir -p models
python -c "from mlx_lm import load; load('mlx-community/Mixtral-8x7B-Instruct-v0.1-4bit')"
```

This will download the 4-bit quantized version of Mixtral-8x7B, which requires about 8GB of disk space.

### 6. Set Up Environment Variables

Create a `.env` file in the project root with the following content:

```
# API Keys
SENDGRID_API_KEY=your_sendgrid_api_key
GITHUB_API_KEY=your_github_api_key
POSTGRES_CONNECTION_STRING=postgresql://username:password@localhost:5432/database

# Email Configuration
EMAIL_FROM=<EMAIL>
EMAIL_TO=<EMAIL>

# SEC API Configuration
SEC_API_USER_AGENT="<NAME_EMAIL>"

# Model Configuration
MODEL_PATH=mlx-community/Mixtral-8x7B-Instruct-v0.1-4bit
```

### 7. Create Required Directories

```bash
mkdir -p data/raw data/processed data/cache/news logs
```

## Tool Setup

### News Scraper Tool

The News Scraper tool requires BeautifulSoup4 and requests:

```bash
pip install beautifulsoup4 requests
```

### PostgreSQL Tool

If you're using the PostgreSQL tool, install PostgreSQL and create a database:

```bash
brew install postgresql
brew services start postgresql
createdb form_d_analysis
```

Then update your `.env` file with the connection string.

### Microsoft 365 Tool

For the Microsoft 365 tool, you need to register an application in Azure AD and get the required credentials. Add them to your `.env` file:

```
MS365_CLIENT_ID=your_client_id
MS365_CLIENT_SECRET=your_client_secret
MS365_TENANT_ID=your_tenant_id
```

### GitHub Tool

For the GitHub tool, create a personal access token with the required permissions and add it to your `.env` file.

## Running the System

### Basic Run

```bash
python run_all.py
```

### Run with Specific Options

```bash
# Run with custom thresholds
python run_all.py --relevance-threshold 0.6 --email-threshold 0.75 --screening-threshold 0.4

# Run in continuous mode
python run_all.py --continuous --poll-interval 30

# Run with legacy pipeline (without Phase 3 features)
python run_all.py --legacy

# Run with specific Phase 3 features disabled
python run_all.py --disable-multi-stage --disable-prompt-evaluation
```

### Available Command-Line Options

- `--legacy`: Use legacy pipeline instead of MCP
- `--relevance-threshold`: Threshold for considering a filing relevant (0.0-1.0)
- `--email-threshold`: Threshold for triggering email alerts (0.0-1.0)
- `--screening-threshold`: Threshold for proceeding from screening to detailed analysis (0.0-1.0)
- `--continuous`: Run in continuous mode, polling at regular intervals
- `--poll-interval`: Minutes between polls in continuous mode
- `--skip-zip`: Skip ZIP downloads and use placeholders for faster development
- `--disable-multi-stage`: Disable multi-stage analysis pipeline
- `--disable-prompt-evaluation`: Disable prompt evaluation and improvement
- `--disable-specialized-analysis`: Disable specialized industry/offering analysis

## Troubleshooting

### Memory Issues

If you encounter memory issues with the Mixtral model:

1. Use the 4-bit quantized version (default)
2. Reduce `max_tokens` in the MCP initialization
3. Switch to the smaller Mistral-7B model by setting `MODEL_PATH=mlx-community/Mistral-7B-Instruct-v0.1-4bit` in your `.env` file

### Model Loading Errors

If the model fails to load:

1. Ensure you have enough disk space
2. Check that MLX and MLX-LM are properly installed
3. Try manually downloading the model as described above

### Tool Errors

If tools fail to execute:

1. Check that all required environment variables are set in your `.env` file
2. Ensure all required directories exist
3. Check the logs in the `logs` directory for specific error messages

## Additional Resources

- MLX Documentation: https://ml-explore.github.io/mlx/build/html/index.html
- MLX-LM Documentation: https://github.com/ml-explore/mlx-examples/tree/main/llms
- SEC EDGAR API: https://www.sec.gov/edgar/sec-api-documentation
