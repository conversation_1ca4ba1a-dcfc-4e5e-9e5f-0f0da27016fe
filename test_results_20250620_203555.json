{"timestamp": "2025-06-20T20:35:37.987108", "tests": {"knowledge_base": {"status": "PASS", "tests": {"initialization": {"status": "PASS", "details": "Loaded 4 valuation frameworks"}, "framework_retrieval": {"status": "PASS", "details": "Retrieved frameworks: ['valuation_methods', 'risk_assessment', 'industry_analysis', 'financial_metrics', 'market_analysis']"}, "context_generation": {"status": "PASS", "details": "Generated context length: 2239 characters"}}, "errors": []}, "rag_system": {"status": "PASS", "tests": {"initialization": {"status": "PASS", "details": "RAG system initialized with knowledge base"}, "context_retrieval": {"status": "PASS", "details": "Retrieved context keys: ['frameworks', 'similar_filings', 'market_context', 'semantic_knowledge']"}, "prompt_enhancement": {"status": "PASS", "details": "Enhanced prompt length: 3301 vs base: 28"}}, "errors": []}, "prompt_manager": {"status": "FAIL", "tests": {}, "errors": ["Enhanced prompt manager test error: 'AnalysisTemplates' object has no attribute '_get_competitive_analysis_template'"]}, "personas": {"status": "PASS", "tests": {"personas_loading": {"status": "PASS", "details": "Loaded 10 personas: ['growth_equity', 'venture_capital', 'private_equity', 'value_investor', 'biotech_specialist', 'fintech_specialist', 'real_estate_specialist', 'energy_specialist', 'credit_analyst', 'distressed_specialist']"}, "persona_selection": {"status": "PASS", "details": "Selections: ['Biotechnology: biotech_specialist', 'Financial Technology: fintech_specialist', 'Real Estate: real_estate_specialist', 'Technology: venture_capital']"}, "context_generation": {"status": "PASS", "details": "Context length: 1421 characters"}}, "errors": []}, "templates": {"status": "FAIL", "tests": {}, "errors": ["Analysis templates test error: 'AnalysisTemplates' object has no attribute '_get_competitive_analysis_template'"]}, "enhanced_mcp": {"status": "PASS", "tests": {"initialization": {"status": "PASS", "details": "Enhanced MCP class available for initialization"}, "configuration": {"status": "PASS", "details": "Configuration options available: ['enable_financial_rag', 'financial_analysis_mode']"}}, "errors": []}, "end_to_end": {"status": "FAIL", "tests": {}, "errors": ["End-to-end test error: 'AnalysisTemplates' object has no attribute '_get_competitive_analysis_template'"]}}, "summary": {"total_tests": 11, "passed": 11, "failed": 0, "skipped": 0, "success_rate": 100.0, "overall_status": "PASS"}}