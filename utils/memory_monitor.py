#!/usr/bin/env python3
"""
Memory Monitor Utility

Provides memory monitoring and leak detection for the SEC Form D analysis pipeline.
"""

import gc
import logging
import time
import resource
import os
from typing import Dict, Any, Optional
from functools import wraps
import tracemalloc

class MemoryMonitor:
    """
    Memory monitoring and leak detection utility.
    """
    
    def __init__(self, enable_tracemalloc: bool = True):
        """
        Initialize memory monitor.

        Args:
            enable_tracemalloc: Whether to enable detailed memory tracing
        """
        self.baseline_memory = None
        self.checkpoints = {}

        if enable_tracemalloc:
            tracemalloc.start()
            self.tracemalloc_enabled = True
        else:
            self.tracemalloc_enabled = False
    
    def get_memory_info(self) -> Dict[str, Any]:
        """
        Get current memory usage information using built-in Python modules.

        Returns:
            Dictionary with memory statistics
        """
        try:
            # Use resource module for memory info (Unix/macOS)
            import resource
            import platform
            memory_usage = resource.getrusage(resource.RUSAGE_SELF)

            # macOS reports in bytes, Linux in KB
            if platform.system() == 'Darwin':  # macOS
                rss_mb = memory_usage.ru_maxrss / 1024 / 1024  # Convert bytes to MB
            else:  # Linux
                rss_mb = memory_usage.ru_maxrss / 1024  # Convert KB to MB

            info = {
                'rss_mb': rss_mb,
                'percent': 0.0,  # Not available without psutil
                'available_gb': 0.0,  # Not available without psutil
                'total_gb': 0.0,  # Not available without psutil
                'system_percent': 0.0  # Not available without psutil
            }
        except:
            # Fallback to basic info
            info = {
                'rss_mb': 0.0,
                'percent': 0.0,
                'available_gb': 0.0,
                'total_gb': 0.0,
                'system_percent': 0.0
            }

        if self.tracemalloc_enabled:
            current, peak = tracemalloc.get_traced_memory()
            info['traced_current_mb'] = current / 1024 / 1024
            info['traced_peak_mb'] = peak / 1024 / 1024

        return info
    
    def set_baseline(self) -> None:
        """Set the baseline memory usage."""
        self.baseline_memory = self.get_memory_info()
        logging.info(f"Memory baseline set: {self.baseline_memory['rss_mb']:.1f} MB RSS")
    
    def checkpoint(self, name: str) -> Dict[str, Any]:
        """
        Create a memory checkpoint.
        
        Args:
            name: Name of the checkpoint
            
        Returns:
            Memory info at checkpoint
        """
        info = self.get_memory_info()
        self.checkpoints[name] = info
        
        if self.baseline_memory:
            delta_mb = info['rss_mb'] - self.baseline_memory['rss_mb']
            logging.info(f"Memory checkpoint '{name}': {info['rss_mb']:.1f} MB RSS (+{delta_mb:.1f} MB)")
        else:
            logging.info(f"Memory checkpoint '{name}': {info['rss_mb']:.1f} MB RSS")
        
        return info
    
    def check_memory_limit(self, limit_gb: float = 8.0) -> bool:
        """
        Check if memory usage exceeds limit.
        
        Args:
            limit_gb: Memory limit in GB
            
        Returns:
            True if under limit, False if over
        """
        info = self.get_memory_info()
        current_gb = info['rss_mb'] / 1024
        
        if current_gb > limit_gb:
            logging.error(f"Memory limit exceeded: {current_gb:.1f} GB > {limit_gb:.1f} GB")
            return False
        
        return True
    
    def force_cleanup(self) -> Dict[str, Any]:
        """
        Force garbage collection and return memory info.
        
        Returns:
            Memory info after cleanup
        """
        before = self.get_memory_info()
        
        # Force garbage collection
        collected = gc.collect()
        
        after = self.get_memory_info()
        freed_mb = before['rss_mb'] - after['rss_mb']
        
        logging.info(f"Garbage collection: freed {freed_mb:.1f} MB, collected {collected} objects")
        
        return {
            'before': before,
            'after': after,
            'freed_mb': freed_mb,
            'collected_objects': collected
        }
    
    def get_top_memory_objects(self, limit: int = 10) -> Optional[list]:
        """
        Get top memory-consuming objects (requires tracemalloc).
        
        Args:
            limit: Number of top objects to return
            
        Returns:
            List of top memory objects or None if tracemalloc disabled
        """
        if not self.tracemalloc_enabled:
            return None
        
        snapshot = tracemalloc.take_snapshot()
        top_stats = snapshot.statistics('lineno')
        
        return [
            {
                'filename': stat.traceback.format()[0],
                'size_mb': stat.size / 1024 / 1024,
                'count': stat.count
            }
            for stat in top_stats[:limit]
        ]

def memory_monitor(checkpoint_name: str = None, memory_limit_gb: float = 8.0):
    """
    Decorator to monitor memory usage of functions.
    
    Args:
        checkpoint_name: Name for memory checkpoint
        memory_limit_gb: Memory limit in GB
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            monitor = MemoryMonitor()
            
            # Set checkpoint name
            name = checkpoint_name or f"{func.__name__}"
            
            # Before execution
            monitor.checkpoint(f"{name}_start")
            
            try:
                # Execute function
                result = func(*args, **kwargs)
                
                # After execution
                monitor.checkpoint(f"{name}_end")
                
                # Check memory limit
                if not monitor.check_memory_limit(memory_limit_gb):
                    logging.warning(f"Memory limit exceeded in {name}")
                    monitor.force_cleanup()
                
                return result
                
            except Exception as e:
                # On error, still check memory
                monitor.checkpoint(f"{name}_error")
                monitor.force_cleanup()
                raise
        
        return wrapper
    return decorator

# Global memory monitor instance
global_monitor = MemoryMonitor()
