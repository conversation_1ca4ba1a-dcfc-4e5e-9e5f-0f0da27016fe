import os
import requests
from pathlib import Path
import logging
from tqdm import tqdm

logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s %(message)s')

def download_file(url, destination):
    """Download a file with progress bar."""
    response = requests.get(url, stream=True)
    response.raise_for_status()
    
    total_size = int(response.headers.get('content-length', 0))
    block_size = 8192
    
    with open(destination, 'wb') as f:
        with tqdm(total=total_size, unit='B', unit_scale=True, desc=os.path.basename(destination)) as pbar:
            for chunk in response.iter_content(chunk_size=block_size):
                if chunk:
                    f.write(chunk)
                    pbar.update(len(chunk))
    
    return destination

def download_quarterly_data(year, quarter, output_dir="data/raw"):
    """Download quarterly Form D data."""
    # Create output directory
    Path(output_dir).mkdir(exist_ok=True, parents=True)
    
    # Construct URL
    url = f"https://www.sec.gov/dera/data/form-d/form-d-{year}-q{quarter}.zip"
    
    # Construct output path
    output_path = os.path.join(output_dir, f"form-d-{year}-q{quarter}.zip")
    
    # Download file
    try:
        logging.info(f"Downloading Form D data for {year} Q{quarter}...")
        download_file(url, output_path)
        logging.info(f"Successfully downloaded to {output_path}")
        return output_path
    except Exception as e:
        logging.error(f"Failed to download {url}: {e}")
        return None

if __name__ == "__main__":
    # Download the last 4 quarters
    # Adjust these values based on the current date
    quarters = [
        (2023, 1), (2023, 2), (2023, 3), (2023, 4),
        (2024, 1)  # Add more as needed
    ]
    
    for year, quarter in quarters:
        download_quarterly_data(year, quarter)
