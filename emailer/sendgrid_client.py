# emailer/sendgrid_client.py
"""
Send emails via SendGrid API or save to disk as fallback.

Supports:
1. HTML content with embedded images
2. File attachments
3. Multiple recipients
4. SendGrid API integration (when API key is available)
"""

import os
import base64
import logging
from pathlib import Path
import json
import datetime as dt
import shutil
from typing import List, Dict, Any, Optional, Union

# Try to import SendGrid libraries with fallback
try:
    from sendgrid import SendGridAPIClient
    from sendgrid.helpers.mail import (
        Mail, Attachment, FileContent, FileName,
        FileType, Disposition, ContentId, Content, To
    )
    SENDGRID_AVAILABLE = True
except ImportError:
    logging.warning("SendGrid libraries not available. Using local file fallback.")
    SENDGRID_AVAILABLE = False

def send_email(
    subject: str,
    html_content: str,
    to_email: Union[str, List[str]] = "<EMAIL>",
    from_email: str = "<EMAIL>",
    attachments: Optional[List[str]] = None,
    inline_images: Optional[Dict[str, str]] = None
):
    """
    Send an email via SendGrid API or save to disk as fallback.

    Args:
        subject: Email subject
        html_content: HTML content of the email
        to_email: Recipient email(s) - string or list of strings
        from_email: Sender email
        attachments: List of file paths to attach
        inline_images: Dict of {content_id: file_path} for inline images

    Returns:
        Boolean indicating success
    """
    # Normalize inputs
    if isinstance(to_email, str):
        to_emails = [to_email]
    else:
        to_emails = to_email

    attachments = attachments or []
    inline_images = inline_images or {}

    # Try to send via SendGrid if API key is available
    api_key = os.environ.get("SENDGRID_API_KEY")

    # Check if we should attempt SendGrid
    should_try_sendgrid = (
        api_key and
        SENDGRID_AVAILABLE and
        api_key != "your_sendgrid_api_key" and
        from_email not in ["<EMAIL>", "<EMAIL>"] and
        not any(email in ["<EMAIL>", "<EMAIL>"] for email in to_emails)
    )

    if should_try_sendgrid:
        try:
            success = _send_via_sendgrid(
                api_key=api_key,
                subject=subject,
                html_content=html_content,
                to_emails=to_emails,
                from_email=from_email,
                attachments=attachments,
                inline_images=inline_images
            )
            if success:
                return True
            else:
                logging.info("SendGrid failed, falling back to local file storage")
        except Exception as e:
            logging.error(f"SendGrid API error: {e}")
            logging.info("Falling back to local file storage")
    else:
        if not api_key or api_key == "your_sendgrid_api_key":
            logging.info("SendGrid API key not configured, using local file storage")
        elif not SENDGRID_AVAILABLE:
            logging.info("SendGrid libraries not available, using local file storage")
        else:
            logging.info("Email addresses not configured, using local file storage")

    # Fallback: Save email to disk
    return _save_email_to_disk(
        subject=subject,
        html_content=html_content,
        to_emails=to_emails,
        from_email=from_email,
        attachments=attachments,
        inline_images=inline_images
    )

def _send_via_sendgrid(
    api_key: str,
    subject: str,
    html_content: str,
    to_emails: List[str],
    from_email: str,
    attachments: List[str],
    inline_images: Dict[str, str]
) -> bool:
    """
    Send email via SendGrid API with improved error handling.

    Args:
        api_key: SendGrid API key
        subject: Email subject
        html_content: HTML content of the email
        to_emails: List of recipient emails
        from_email: Sender email
        attachments: List of file paths to attach
        inline_images: Dict of {content_id: file_path} for inline images

    Returns:
        Boolean indicating success
    """
    # Validate API key
    if not api_key or api_key == "your_sendgrid_api_key":
        logging.warning("SendGrid API key not configured properly")
        return False

    # Validate email addresses
    if from_email in ["<EMAIL>", "<EMAIL>"]:
        logging.warning("From email not configured properly")
        return False

    if any(email in ["<EMAIL>", "<EMAIL>"] for email in to_emails):
        logging.warning("To email not configured properly")
        return False

    try:
        # Create message
        message = Mail(
            from_email=from_email,
            to_emails=[To(email) for email in to_emails],
            subject=subject,
            html_content=Content("text/html", html_content)
        )

        # Add attachments
        for file_path in attachments:
            try:
                with open(file_path, 'rb') as f:
                    file_data = f.read()
                    file_name = Path(file_path).name
                    file_type = _get_mime_type(file_path)

                    attachment = Attachment()
                    attachment.file_content = FileContent(base64.b64encode(file_data).decode())
                    attachment.file_name = FileName(file_name)
                    attachment.file_type = FileType(file_type)
                    attachment.disposition = Disposition('attachment')

                    message.add_attachment(attachment)
            except Exception as e:
                logging.warning(f"Failed to attach file {file_path}: {e}")

        # Add inline images
        for content_id, file_path in inline_images.items():
            try:
                with open(file_path, 'rb') as f:
                    file_data = f.read()
                    file_name = Path(file_path).name
                    file_type = _get_mime_type(file_path)

                    attachment = Attachment()
                    attachment.file_content = FileContent(base64.b64encode(file_data).decode())
                    attachment.file_name = FileName(file_name)
                    attachment.file_type = FileType(file_type)
                    attachment.disposition = Disposition('inline')
                    attachment.content_id = ContentId(content_id)

                    message.add_attachment(attachment)
            except Exception as e:
                logging.warning(f"Failed to attach inline image {file_path}: {e}")

        # Send message with improved error handling
        sg = SendGridAPIClient(api_key)
        response = sg.send(message)

        logging.info(f"Email sent via SendGrid: {response.status_code}")
        return response.status_code in (200, 201, 202)

    except Exception as e:
        # Log specific error types for better debugging
        error_msg = str(e).lower()
        if "certificate" in error_msg or "ssl" in error_msg:
            logging.error(f"SendGrid SSL/Certificate error: {e}")
        elif "unauthorized" in error_msg or "401" in error_msg:
            logging.error(f"SendGrid authentication error (check API key): {e}")
        elif "forbidden" in error_msg or "403" in error_msg:
            logging.error(f"SendGrid permission error: {e}")
        else:
            logging.error(f"SendGrid send error: {e}")
        return False

def _save_email_to_disk(
    subject: str,
    html_content: str,
    to_emails: List[str],
    from_email: str,
    attachments: List[str],
    inline_images: Dict[str, str]
) -> bool:
    """
    Save email to disk as fallback.

    Args:
        subject: Email subject
        html_content: HTML content of the email
        to_emails: List of recipient emails
        from_email: Sender email
        attachments: List of file paths to attach
        inline_images: Dict of {content_id: file_path} for inline images

    Returns:
        Boolean indicating success
    """
    try:
        # Create email directory
        email_dir = Path("logs/emails")
        email_dir.mkdir(parents=True, exist_ok=True)

        # Create a unique timestamp
        timestamp = dt.datetime.now().strftime("%Y%m%d_%H%M%S")

        # Create a directory for this email
        email_specific_dir = email_dir / f"email_{timestamp}"
        email_specific_dir.mkdir(exist_ok=True)

        # Save the HTML content
        html_path = email_specific_dir / "email.html"
        with open(html_path, 'w') as f:
            f.write(html_content)

        # Save metadata
        meta_path = email_specific_dir / "metadata.json"
        with open(meta_path, 'w') as f:
            json.dump({
                "to": to_emails,
                "from": from_email,
                "subject": subject,
                "timestamp": timestamp,
                "attachments": [Path(a).name for a in attachments],
                "inline_images": {k: Path(v).name for k, v in inline_images.items()}
            }, f, indent=2)

        # Copy attachments
        attachments_dir = email_specific_dir / "attachments"
        if attachments:
            attachments_dir.mkdir(exist_ok=True)
            for file_path in attachments:
                dest = attachments_dir / Path(file_path).name
                shutil.copy2(file_path, dest)

        # Copy inline images
        images_dir = email_specific_dir / "images"
        if inline_images:
            images_dir.mkdir(exist_ok=True)
            for content_id, file_path in inline_images.items():
                dest = images_dir / Path(file_path).name
                shutil.copy2(file_path, dest)

        logging.info(f"Email saved to {email_specific_dir}")

        # Also print a confirmation to the console
        print(f"=== Email Saved ===")
        print(f"Subject: {subject}")
        print(f"To: {', '.join(to_emails)}")
        print(f"Saved to: {email_specific_dir}")
        print(f"Attachments: {len(attachments)}")
        print(f"Inline images: {len(inline_images)}")

        return True
    except Exception as e:
        logging.error(f"Error saving email to disk: {e}")
        return False

def _get_mime_type(file_path: str) -> str:
    """
    Get MIME type for a file based on extension.

    Args:
        file_path: Path to the file

    Returns:
        MIME type string
    """
    extension = Path(file_path).suffix.lower()

    mime_types = {
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif',
        '.pdf': 'application/pdf',
        '.txt': 'text/plain',
        '.html': 'text/html',
        '.htm': 'text/html',
        '.json': 'application/json',
        '.csv': 'text/csv',
        '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    }

    return mime_types.get(extension, 'application/octet-stream')
