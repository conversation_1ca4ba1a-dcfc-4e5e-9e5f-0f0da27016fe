# emailer/build_html.py
"""
HTML Email Builder

Generates HTML content for email alerts with heatmap visualizations.
"""

import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

def build_heatmap_html(ranked, narrative):
    """
    Legacy HTML builder for backward compatibility.

    Args:
        ranked: List of ranked entries
        narrative: Text narrative summary

    Returns:
        HTML content as string
    """
    # Create a basic HTML wrapper
    html = f"""
    <html>
    <head>
        <style>
            body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
            h1 {{ color: #2c3e50; }}
            .container {{ max-width: 800px; margin: 0 auto; padding: 20px; }}
            .summary {{ background-color: #f8f9fa; padding: 15px; border-left: 4px solid #4285f4; margin-bottom: 20px; }}
            .footer {{ font-size: 12px; color: #777; margin-top: 30px; border-top: 1px solid #eee; padding-top: 10px; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>SEC Form D Filing Heat Map</h1>

            <div class="summary">
                <h2>Summary</h2>
                <p>{narrative}</p>
            </div>

            <h2>Top Filings</h2>
            <ul>
    """

    # Add top 5 filings
    for entry in ranked[:5]:
        title = entry.get("title", "Untitled")
        score = entry.get("final_score", 0)
        rationale = entry.get("rationale", "No rationale provided")

        html += f"""
            <li>
                <strong>{title}</strong> - Score: {score:.2f}<br>
                <em>{rationale}</em>
            </li>
        """

    # Close HTML
    html += """
            </ul>

            <div class="footer">
                <p>Generated by Private-Signals AI on {}</p>
            </div>
        </div>
    </body>
    </html>
    """.format(datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

    return html

def build_enhanced_html(processed_entries, visualization_path=None, narrative=None):
    """
    Enhanced HTML builder with visualization support.

    Args:
        processed_entries: List of processed entries
        visualization_path: Path to visualization image
        narrative: Text narrative summary

    Returns:
        HTML content as string
    """
    # Create a more sophisticated HTML template
    html = f"""
    <html>
    <head>
        <style>
            body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }}
            h1, h2, h3 {{ color: #2c3e50; }}
            .container {{ max-width: 800px; margin: 0 auto; padding: 20px; }}
            .header {{ background-color: #4285f4; color: white; padding: 20px; text-align: center; }}
            .header h1 {{ color: white; margin: 0; }}
            .summary {{ background-color: #f8f9fa; padding: 15px; border-left: 4px solid #4285f4; margin: 20px 0; }}
            .visualization {{ text-align: center; margin: 30px 0; }}
            .filing {{ background-color: #fff; border: 1px solid #ddd; border-radius: 4px; padding: 15px; margin-bottom: 15px; }}
            .filing-header {{ display: flex; justify-content: space-between; }}
            .filing-title {{ font-weight: bold; font-size: 18px; }}
            .filing-score {{ background-color: #4285f4; color: white; padding: 5px 10px; border-radius: 20px; }}
            .filing-summary {{ margin-top: 10px; }}
            .footer {{ font-size: 12px; color: #777; margin-top: 30px; border-top: 1px solid #eee; padding-top: 10px; text-align: center; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>SEC Form D Filing Analysis</h1>
            <p>Powered by MLX AI Analysis</p>
        </div>

        <div class="container">
    """

    # Add narrative summary if provided
    if narrative:
        html += f"""
            <div class="summary">
                <h2>Executive Summary</h2>
                <p>{narrative}</p>
            </div>
        """

    # Add visualization if provided
    if visualization_path:
        html += f"""
            <div class="visualization">
                <h2>Visualization</h2>
                <img src="cid:heatmap" alt="Form D Filings Heatmap" style="max-width: 100%; height: auto;">
                <p><em>Heatmap of recent Form D filings by industry and offering amount</em></p>
            </div>
        """

    # Add top filings
    html += "<h2>Top Relevant Filings</h2>"

    # Sort by relevance score
    sorted_entries = sorted(
        processed_entries,
        key=lambda x: x.get("relevance_score", 0),
        reverse=True
    )

    # Add top 5 filings
    for entry in sorted_entries[:5]:
        title = entry.get("title", "Untitled")
        score = entry.get("relevance_score", 0)
        summary = entry.get("summary", "No summary available")

        html += f"""
            <div class="filing">
                <div class="filing-header">
                    <div class="filing-title">{title}</div>
                    <div class="filing-score">{score:.2f}</div>
                </div>
                <div class="filing-summary">{summary}</div>
            </div>
        """

    # Close HTML
    html += f"""
            <div class="footer">
                <p>Generated by Private-Signals AI on {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
                <p>This email was automatically generated based on SEC Form D filings that met relevance criteria.</p>
            </div>
        </div>
    </body>
    </html>
    """

    return html
