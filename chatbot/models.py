#!/usr/bin/env python3
"""
Data Models for SEC Form D Data Librarian Chatbot

Defines data structures for chatbot conversations, intents, and responses.
"""

from datetime import datetime
from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field
from enum import Enum

class IntentType(str, Enum):
    """Enumeration of supported intent types."""
    GREETING = "greeting"
    HELP = "help"
    SEARCH_FILINGS = "search_filings"
    TREND_ANALYSIS = "trend_analysis"
    COMPANY_ANALYSIS = "company_analysis"
    MARKET_INTELLIGENCE = "market_intelligence"
    EMAIL_ALERT = "email_alert"
    CONFIGURATION = "configuration"
    UNKNOWN = "unknown"

class EntityType(str, Enum):
    """Enumeration of entity types that can be extracted from user queries."""
    COMPANY_NAME = "company_name"
    INDUSTRY = "industry"
    AMOUNT = "amount"
    DATE_RANGE = "date_range"
    LOCATION = "location"
    FILING_TYPE = "filing_type"
    THRESHOLD = "threshold"
    METRIC = "metric"

class Entity(BaseModel):
    """Represents an extracted entity from user input."""
    type: EntityType = Field(..., description="Type of entity")
    value: str = Field(..., description="Extracted value")
    confidence: float = Field(0.0, description="Confidence score (0.0-1.0)")
    normalized_value: Optional[Any] = Field(None, description="Normalized/parsed value")
    
    class Config:
        schema_extra = {
            "example": {
                "type": "amount",
                "value": "$10M",
                "confidence": 0.95,
                "normalized_value": 10000000
            }
        }

class Intent(BaseModel):
    """Represents a parsed user intent with extracted entities."""
    type: IntentType = Field(..., description="Classified intent type")
    confidence: float = Field(0.0, description="Intent classification confidence")
    entities: List[Entity] = Field([], description="Extracted entities")
    raw_query: str = Field(..., description="Original user query")
    
    class Config:
        schema_extra = {
            "example": {
                "type": "search_filings",
                "confidence": 0.92,
                "entities": [
                    {
                        "type": "industry",
                        "value": "biotech",
                        "confidence": 0.88,
                        "normalized_value": "biotechnology"
                    },
                    {
                        "type": "amount",
                        "value": "over $10M",
                        "confidence": 0.95,
                        "normalized_value": {"operator": ">", "amount": 10000000}
                    }
                ],
                "raw_query": "Show me biotech filings over $10M"
            }
        }

class ChatMessage(BaseModel):
    """Represents a single message in a conversation."""
    id: str = Field(..., description="Unique message identifier")
    content: str = Field(..., description="Message content")
    sender: str = Field(..., description="Message sender (user/assistant)")
    timestamp: str = Field(..., description="Message timestamp")
    session_id: str = Field(..., description="Session identifier")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional message metadata")
    
    class Config:
        schema_extra = {
            "example": {
                "id": "msg_123",
                "content": "Show me recent biotech filings",
                "sender": "user",
                "timestamp": "2024-01-15T10:30:00Z",
                "session_id": "session_456",
                "metadata": {"intent": "search_filings"}
            }
        }

class Insight(BaseModel):
    """Represents an analytical insight generated from data."""
    type: str = Field(..., description="Type of insight")
    title: str = Field(..., description="Insight title")
    description: str = Field(..., description="Detailed insight description")
    data: Optional[Dict[str, Any]] = Field(None, description="Supporting data")
    confidence: float = Field(0.0, description="Insight confidence score")
    
    class Config:
        schema_extra = {
            "example": {
                "type": "trend",
                "title": "Biotech Funding Surge",
                "description": "Biotech filings increased 23% this quarter, indicating strong investor interest in life sciences.",
                "data": {"growth_rate": 0.23, "filing_count": 127},
                "confidence": 0.87
            }
        }

class ChatResponse(BaseModel):
    """Represents a chatbot response to a user query."""
    text: str = Field(..., description="Natural language response text")
    data: List[Dict[str, Any]] = Field([], description="Structured data results")
    insights: List[Insight] = Field([], description="Generated insights")
    follow_up_questions: List[str] = Field([], description="Suggested follow-up questions")
    session_id: str = Field(..., description="Session identifier")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat(), description="Response timestamp")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional response metadata")
    error: Optional[str] = Field(None, description="Error message if applicable")
    
    class Config:
        schema_extra = {
            "example": {
                "text": "I found 23 biotech filings over $10M in the last quarter...",
                "data": [{"company": "BioTech Inc", "amount": 15000000}],
                "insights": [{"type": "trend", "title": "Growth in Biotech"}],
                "follow_up_questions": ["Show me the largest deals", "Compare with last quarter"],
                "session_id": "session_456",
                "timestamp": "2024-01-15T10:30:00Z"
            }
        }

class ConversationContext(BaseModel):
    """Represents the context of an ongoing conversation."""
    session_id: str = Field(..., description="Session identifier")
    user_id: Optional[str] = Field(None, description="User identifier")
    messages: List[ChatMessage] = Field([], description="Conversation history")
    user_preferences: Dict[str, Any] = Field({}, description="User preferences and settings")
    last_activity: str = Field(default_factory=lambda: datetime.now().isoformat(), description="Last activity timestamp")
    
    def add_message(self, message: ChatMessage) -> None:
        """Add a message to the conversation history."""
        self.messages.append(message)
        self.last_activity = datetime.now().isoformat()
    
    def get_recent_messages(self, count: int = 10) -> List[ChatMessage]:
        """Get the most recent messages."""
        return self.messages[-count:] if len(self.messages) > count else self.messages
    
    def get_user_messages(self) -> List[ChatMessage]:
        """Get only user messages from the conversation."""
        return [msg for msg in self.messages if msg.sender == "user"]
    
    def get_assistant_messages(self) -> List[ChatMessage]:
        """Get only assistant messages from the conversation."""
        return [msg for msg in self.messages if msg.sender == "assistant"]

class QueryParameters(BaseModel):
    """Represents parameters for database queries."""
    table: str = Field(..., description="Target database table")
    filters: Dict[str, Any] = Field({}, description="Query filters")
    sort_by: Optional[str] = Field(None, description="Sort field")
    sort_order: str = Field("desc", description="Sort order (asc/desc)")
    limit: int = Field(50, description="Maximum number of results")
    offset: int = Field(0, description="Query offset for pagination")
    
    class Config:
        schema_extra = {
            "example": {
                "table": "form_d_filings",
                "filters": {
                    "industry_group": "biotechnology",
                    "offering_amount": {"operator": ">", "value": 10000000}
                },
                "sort_by": "filing_date",
                "sort_order": "desc",
                "limit": 50
            }
        }

class DatabaseQuery(BaseModel):
    """Represents a structured database query."""
    type: str = Field(..., description="Query type")
    params: QueryParameters = Field(..., description="Query parameters")
    sql: Optional[str] = Field(None, description="Generated SQL query")
    
class AnalyticsRequest(BaseModel):
    """Represents a request for analytical insights."""
    metric: str = Field(..., description="Metric to analyze")
    dimensions: List[str] = Field([], description="Analysis dimensions")
    filters: Dict[str, Any] = Field({}, description="Data filters")
    time_period: Optional[str] = Field(None, description="Time period for analysis")
    
class EmailAlertConfig(BaseModel):
    """Configuration for email alerts."""
    name: str = Field(..., description="Alert name")
    description: str = Field(..., description="Alert description")
    filters: Dict[str, Any] = Field({}, description="Filtering criteria")
    relevance_threshold: float = Field(0.7, description="Minimum relevance score")
    frequency: str = Field("daily", description="Alert frequency")
    recipients: List[str] = Field([], description="Email recipients")
    active: bool = Field(True, description="Whether alert is active")

class ChatbotStats(BaseModel):
    """Statistics for chatbot performance monitoring."""
    total_queries: int = Field(0, description="Total number of queries processed")
    successful_queries: int = Field(0, description="Number of successful queries")
    average_response_time: float = Field(0.0, description="Average response time in seconds")
    active_sessions: int = Field(0, description="Number of active sessions")
    success_rate: float = Field(0.0, description="Query success rate percentage")
    
class UserPreferences(BaseModel):
    """User preferences for personalized experience."""
    preferred_industries: List[str] = Field([], description="Preferred industries")
    default_date_range: str = Field("30d", description="Default date range for queries")
    relevance_threshold: float = Field(0.7, description="Default relevance threshold")
    email_notifications: bool = Field(True, description="Email notification preference")
    data_visualization: bool = Field(True, description="Show charts and graphs")
    
class SearchResult(BaseModel):
    """Represents a search result item."""
    id: str = Field(..., description="Result identifier")
    title: str = Field(..., description="Result title")
    summary: str = Field(..., description="Result summary")
    relevance_score: float = Field(0.0, description="Relevance score")
    metadata: Dict[str, Any] = Field({}, description="Additional metadata")
    
class TrendAnalysis(BaseModel):
    """Represents trend analysis results."""
    metric: str = Field(..., description="Analyzed metric")
    time_period: str = Field(..., description="Analysis time period")
    trend_direction: str = Field(..., description="Trend direction (up/down/stable)")
    change_percentage: float = Field(0.0, description="Percentage change")
    data_points: List[Dict[str, Any]] = Field([], description="Trend data points")
    insights: List[str] = Field([], description="Key insights from the trend")

class CompanyProfile(BaseModel):
    """Represents a company profile with filing history."""
    name: str = Field(..., description="Company name")
    industry: str = Field(..., description="Primary industry")
    location: str = Field(..., description="Company location")
    total_filings: int = Field(0, description="Total number of filings")
    total_raised: float = Field(0.0, description="Total amount raised")
    recent_filings: List[Dict[str, Any]] = Field([], description="Recent filing data")
    key_metrics: Dict[str, Any] = Field({}, description="Key company metrics")
