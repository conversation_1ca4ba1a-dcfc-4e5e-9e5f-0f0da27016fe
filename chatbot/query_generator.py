#!/usr/bin/env python3
"""
Query Generator for SEC Form D Data Librarian Chatbot

Converts parsed intents and entities into structured database queries
for the Supabase Form D filing database.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from chatbot.models import Intent, Entity, IntentType, EntityType, QueryParameters, DatabaseQuery

logger = logging.getLogger(__name__)

class QueryGenerator:
    """
    Generates structured database queries from parsed user intents and entities.
    """
    
    def __init__(self):
        """Initialize the query generator."""
        # Database schema mapping
        self.schema_mapping = {
            EntityType.COMPANY_NAME: "issuer_name",
            EntityType.INDUSTRY: "industry_group", 
            EntityType.AMOUNT: "offering_amount",
            EntityType.DATE_RANGE: "filing_date",
            EntityType.LOCATION: ["issuer_state", "issuer_city"],
            EntityType.FILING_TYPE: "offering_type"
        }
        
        # Industry normalization mapping
        self.industry_mapping = {
            "biotechnology": ["biotechnology", "pharmaceutical", "life sciences", "biotech"],
            "technology": ["technology", "software", "saas", "artificial intelligence", "ai"],
            "healthcare": ["healthcare", "medical", "clinical", "health"],
            "financial": ["financial", "fintech", "banking", "payments", "insurance"],
            "energy": ["energy", "clean energy", "renewable", "solar", "wind", "oil", "gas"],
            "real estate": ["real estate", "property", "reit", "construction"],
            "retail": ["retail", "e-commerce", "consumer", "fashion", "food"]
        }
        
        logger.info("Query Generator initialized")
    
    async def create_query(self, intent: Intent) -> DatabaseQuery:
        """
        Create a structured database query from a parsed intent.
        
        Args:
            intent: Parsed user intent with entities
            
        Returns:
            DatabaseQuery object with query parameters
        """
        try:
            logger.info(f"Creating query for intent: {intent.type}")
            
            if intent.type == IntentType.SEARCH_FILINGS:
                return await self._create_search_query(intent)
            elif intent.type == IntentType.TREND_ANALYSIS:
                return await self._create_trend_query(intent)
            elif intent.type == IntentType.COMPANY_ANALYSIS:
                return await self._create_company_query(intent)
            elif intent.type == IntentType.MARKET_INTELLIGENCE:
                return await self._create_market_query(intent)
            else:
                # Default to basic search
                return await self._create_search_query(intent)
                
        except Exception as e:
            logger.error(f"Error creating query: {e}")
            # Return basic query as fallback
            return DatabaseQuery(
                type="search_filings",
                params=QueryParameters(
                    table="form_d_filings",
                    filters={},
                    sort_by="filing_date",
                    sort_order="desc",
                    limit=50
                )
            )
    
    async def _create_search_query(self, intent: Intent) -> DatabaseQuery:
        """Create a search query for filings."""
        filters = {}
        
        # Process entities to build filters
        for entity in intent.entities:
            if entity.type == EntityType.COMPANY_NAME:
                # Use ILIKE for case-insensitive partial matching
                filters["issuer_name"] = {"operator": "ILIKE", "value": f"%{entity.value}%"}
                
            elif entity.type == EntityType.INDUSTRY:
                # Map to industry categories and use OR condition
                industry_terms = self._get_industry_terms(entity.normalized_value or entity.value)
                if len(industry_terms) == 1:
                    filters["industry_group"] = {"operator": "ILIKE", "value": f"%{industry_terms[0]}%"}
                else:
                    # Multiple terms - use OR condition
                    filters["industry_group"] = {
                        "operator": "OR",
                        "conditions": [{"operator": "ILIKE", "value": f"%{term}%"} for term in industry_terms]
                    }
                    
            elif entity.type == EntityType.AMOUNT:
                if isinstance(entity.normalized_value, dict):
                    amount_data = entity.normalized_value
                    filters["offering_amount"] = {
                        "operator": amount_data.get("operator", "="),
                        "value": amount_data.get("amount", 0)
                    }
                    
            elif entity.type == EntityType.DATE_RANGE:
                date_filter = self._parse_date_range(entity.value)
                if date_filter:
                    filters.update(date_filter)
                    
            elif entity.type == EntityType.LOCATION:
                # Check both state and city fields
                location_value = entity.value.upper()
                filters["location"] = {
                    "operator": "OR",
                    "conditions": [
                        {"field": "issuer_state", "operator": "ILIKE", "value": f"%{location_value}%"},
                        {"field": "issuer_city", "operator": "ILIKE", "value": f"%{entity.value}%"}
                    ]
                }
        
        # Default date filter if none specified (last 90 days)
        if not any(entity.type == EntityType.DATE_RANGE for entity in intent.entities):
            filters["filing_date"] = {
                "operator": ">=",
                "value": (datetime.now() - timedelta(days=90)).strftime("%Y-%m-%d")
            }
        
        return DatabaseQuery(
            type="search_filings",
            params=QueryParameters(
                table="form_d_filings",
                filters=filters,
                sort_by="filing_date",
                sort_order="desc",
                limit=50
            )
        )
    
    async def _create_trend_query(self, intent: Intent) -> DatabaseQuery:
        """Create a trend analysis query."""
        filters = {}
        group_by = ["DATE_TRUNC('month', filing_date)"]
        metrics = ["COUNT(*) as filing_count", "AVG(offering_amount) as avg_amount"]
        
        # Process entities for trend analysis
        for entity in intent.entities:
            if entity.type == EntityType.INDUSTRY:
                industry_terms = self._get_industry_terms(entity.normalized_value or entity.value)
                if len(industry_terms) == 1:
                    filters["industry_group"] = {"operator": "ILIKE", "value": f"%{industry_terms[0]}%"}
                else:
                    filters["industry_group"] = {
                        "operator": "OR", 
                        "conditions": [{"operator": "ILIKE", "value": f"%{term}%"} for term in industry_terms]
                    }
                    
            elif entity.type == EntityType.DATE_RANGE:
                date_filter = self._parse_date_range(entity.value)
                if date_filter:
                    filters.update(date_filter)
        
        # Default to last 12 months if no date range specified
        if not any(entity.type == EntityType.DATE_RANGE for entity in intent.entities):
            filters["filing_date"] = {
                "operator": ">=",
                "value": (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")
            }
        
        return DatabaseQuery(
            type="trend_analysis",
            params=QueryParameters(
                table="form_d_filings",
                filters=filters,
                sort_by="filing_date",
                sort_order="asc",
                limit=100,
                group_by=group_by,
                metrics=metrics
            )
        )
    
    async def _create_company_query(self, intent: Intent) -> DatabaseQuery:
        """Create a company analysis query."""
        filters = {}
        company_name = None
        
        # Find company name entity
        for entity in intent.entities:
            if entity.type == EntityType.COMPANY_NAME:
                company_name = entity.value
                filters["issuer_name"] = {"operator": "ILIKE", "value": f"%{company_name}%"}
                break
        
        # If no specific company, look for industry to find similar companies
        if not company_name:
            for entity in intent.entities:
                if entity.type == EntityType.INDUSTRY:
                    industry_terms = self._get_industry_terms(entity.normalized_value or entity.value)
                    if len(industry_terms) == 1:
                        filters["industry_group"] = {"operator": "ILIKE", "value": f"%{industry_terms[0]}%"}
                    break
        
        return DatabaseQuery(
            type="company_analysis",
            params=QueryParameters(
                table="form_d_filings",
                filters=filters,
                sort_by="filing_date",
                sort_order="desc",
                limit=25
            )
        )
    
    async def _create_market_query(self, intent: Intent) -> DatabaseQuery:
        """Create a market intelligence query."""
        filters = {}
        
        # Market intelligence typically looks at recent data
        filters["filing_date"] = {
            "operator": ">=",
            "value": (datetime.now() - timedelta(days=90)).strftime("%Y-%m-%d")
        }
        
        # Process any specific filters from entities
        for entity in intent.entities:
            if entity.type == EntityType.INDUSTRY:
                industry_terms = self._get_industry_terms(entity.normalized_value or entity.value)
                if len(industry_terms) == 1:
                    filters["industry_group"] = {"operator": "ILIKE", "value": f"%{industry_terms[0]}%"}
                    
            elif entity.type == EntityType.AMOUNT:
                if isinstance(entity.normalized_value, dict):
                    amount_data = entity.normalized_value
                    filters["offering_amount"] = {
                        "operator": amount_data.get("operator", "="),
                        "value": amount_data.get("amount", 0)
                    }
        
        return DatabaseQuery(
            type="market_intelligence",
            params=QueryParameters(
                table="form_d_filings",
                filters=filters,
                sort_by="offering_amount",
                sort_order="desc",
                limit=100
            )
        )
    
    def _get_industry_terms(self, industry: str) -> List[str]:
        """Get all related terms for an industry."""
        industry_lower = industry.lower()
        
        # Check if it matches any of our mapped categories
        for category, terms in self.industry_mapping.items():
            if industry_lower in terms or any(term in industry_lower for term in terms):
                return terms
        
        # If no mapping found, return the original term
        return [industry_lower]
    
    def _parse_date_range(self, date_text: str) -> Optional[Dict[str, Any]]:
        """Parse date range text into database filters."""
        date_text_lower = date_text.lower()
        now = datetime.now()
        
        # Handle relative dates
        if "last" in date_text_lower or "past" in date_text_lower:
            if "day" in date_text_lower:
                if "30" in date_text_lower:
                    start_date = now - timedelta(days=30)
                elif "7" in date_text_lower:
                    start_date = now - timedelta(days=7)
                else:
                    start_date = now - timedelta(days=1)
            elif "week" in date_text_lower:
                start_date = now - timedelta(weeks=1)
            elif "month" in date_text_lower:
                if "3" in date_text_lower:
                    start_date = now - timedelta(days=90)
                elif "6" in date_text_lower:
                    start_date = now - timedelta(days=180)
                else:
                    start_date = now - timedelta(days=30)
            elif "quarter" in date_text_lower:
                start_date = now - timedelta(days=90)
            elif "year" in date_text_lower:
                start_date = now - timedelta(days=365)
            else:
                start_date = now - timedelta(days=30)  # Default to last month
                
            return {
                "filing_date": {
                    "operator": ">=",
                    "value": start_date.strftime("%Y-%m-%d")
                }
            }
        
        # Handle "this" periods
        elif "this" in date_text_lower:
            if "week" in date_text_lower:
                start_date = now - timedelta(days=now.weekday())
            elif "month" in date_text_lower:
                start_date = now.replace(day=1)
            elif "quarter" in date_text_lower:
                quarter_start_month = ((now.month - 1) // 3) * 3 + 1
                start_date = now.replace(month=quarter_start_month, day=1)
            elif "year" in date_text_lower:
                start_date = now.replace(month=1, day=1)
            else:
                start_date = now - timedelta(days=30)
                
            return {
                "filing_date": {
                    "operator": ">=",
                    "value": start_date.strftime("%Y-%m-%d")
                }
            }
        
        # Handle specific quarters (e.g., "Q3 2024")
        elif "q" in date_text_lower and any(year in date_text_lower for year in ["2020", "2021", "2022", "2023", "2024"]):
            # Extract quarter and year
            import re
            quarter_match = re.search(r'q([1-4])', date_text_lower)
            year_match = re.search(r'(20\d{2})', date_text_lower)
            
            if quarter_match and year_match:
                quarter = int(quarter_match.group(1))
                year = int(year_match.group(1))
                
                # Calculate quarter start and end dates
                quarter_start_month = (quarter - 1) * 3 + 1
                quarter_end_month = quarter * 3
                
                start_date = datetime(year, quarter_start_month, 1)
                if quarter_end_month == 12:
                    end_date = datetime(year, 12, 31)
                else:
                    end_date = datetime(year, quarter_end_month + 1, 1) - timedelta(days=1)
                
                return {
                    "filing_date": {
                        "operator": "BETWEEN",
                        "value": [start_date.strftime("%Y-%m-%d"), end_date.strftime("%Y-%m-%d")]
                    }
                }
        
        # Handle specific years
        elif any(year in date_text_lower for year in ["2020", "2021", "2022", "2023", "2024"]):
            import re
            year_match = re.search(r'(20\d{2})', date_text_lower)
            if year_match:
                year = int(year_match.group(1))
                return {
                    "filing_date": {
                        "operator": "BETWEEN",
                        "value": [f"{year}-01-01", f"{year}-12-31"]
                    }
                }
        
        # If we can't parse it, return None
        return None
