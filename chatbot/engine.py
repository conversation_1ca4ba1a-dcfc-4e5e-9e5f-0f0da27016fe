#!/usr/bin/env python3
"""
Data Librarian Chatbot Engine

Core engine for the SEC Form D intelligent data librarian chatbot.
Provides natural language querying capabilities for the comprehensive
Form D filing database.
"""

import json
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional

from chatbot.nlp import NLPProcessor
from chatbot.query_generator import QueryGenerator
from chatbot.response_generator import ResponseGenerator
from chatbot.context_manager import ContextManager
from chatbot.models import ChatMessage, ChatResponse, Intent, ConversationContext
from db.supabase_manager import SupabaseDatabaseManager
from models.persistent_model_manager import PersistentModelManager

logger = logging.getLogger(__name__)

class DataLibrarianChatbot:
    """
    Intelligent data librarian chatbot for SEC Form D filings.
    
    Provides natural language interface to query and analyze the comprehensive
    Form D filing database with expert-level insights and recommendations.
    """
    
    def __init__(self):
        """Initialize the chatbot engine."""
        try:
            # Initialize core components
            self.nlp_processor = NLPProcessor()
            self.query_generator = QueryGenerator()
            self.response_generator = ResponseGenerator()
            self.context_manager = ContextManager()
            
            # Initialize data access
            self.db_manager = SupabaseDatabaseManager()
            self.persistent_model = PersistentModelManager()
            
            # Chatbot personality and capabilities
            self.personality = {
                "name": "SEC Data Librarian",
                "role": "Expert SEC Form D Filing Analyst",
                "expertise": [
                    "SEC Form D regulations and requirements",
                    "Private market investment trends",
                    "Industry analysis and benchmarking",
                    "Company research and due diligence",
                    "Market intelligence and insights"
                ],
                "tone": "professional, knowledgeable, helpful"
            }
            
            # Performance tracking
            self.stats = {
                "total_queries": 0,
                "successful_queries": 0,
                "average_response_time": 0.0,
                "active_sessions": 0
            }
            
            logger.info("Data Librarian Chatbot initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize chatbot: {e}")
            raise
    
    async def process_message(self, message: str, session_id: Optional[str] = None,
                            user_id: Optional[str] = None) -> ChatResponse:
        """
        Process a user message and generate an intelligent response.
        
        Args:
            message: User's natural language message
            session_id: Session identifier for conversation context
            user_id: User identifier for personalization
            
        Returns:
            ChatResponse with analysis results and insights
        """
        start_time = datetime.now()
        
        # Generate session ID if not provided
        if not session_id:
            session_id = str(uuid.uuid4())
        
        try:
            self.stats["total_queries"] += 1
            logger.info(f"Processing message for session {session_id}: {message[:100]}...")
            
            # Get conversation context
            context = self.context_manager.get_context(session_id)
            
            # Add user message to context
            user_message = ChatMessage(
                id=str(uuid.uuid4()),
                content=message,
                sender="user",
                timestamp=datetime.now().isoformat(),
                session_id=session_id
            )
            context.add_message(user_message)
            
            # Parse user intent and entities
            intent = await self.nlp_processor.parse_intent(message, context)
            logger.info(f"Parsed intent: {intent.type} with entities: {intent.entities}")
            
            # Handle different intent types
            if intent.type == "GREETING":
                response = await self._handle_greeting(intent, context)
            elif intent.type == "HELP":
                response = await self._handle_help_request(intent, context)
            elif intent.type in ["SEARCH_FILINGS", "TREND_ANALYSIS", "COMPANY_ANALYSIS", "MARKET_INTELLIGENCE"]:
                response = await self._handle_data_query(intent, context)
            elif intent.type == "EMAIL_ALERT":
                response = await self._handle_email_alert(intent, context)
            elif intent.type == "CONFIGURATION":
                response = await self._handle_configuration(intent, context)
            else:
                response = await self._handle_unknown_intent(intent, context)
            
            # Add response to context
            bot_message = ChatMessage(
                id=str(uuid.uuid4()),
                content=response.text,
                sender="assistant",
                timestamp=datetime.now().isoformat(),
                session_id=session_id,
                metadata={
                    "intent": intent.type,
                    "entities": intent.entities,
                    "data_count": len(response.data) if response.data else 0
                }
            )
            context.add_message(bot_message)
            
            # Update context
            self.context_manager.update_context(session_id, context)
            
            # Update statistics
            self.stats["successful_queries"] += 1
            response_time = (datetime.now() - start_time).total_seconds()
            self._update_response_time(response_time)
            
            logger.info(f"Successfully processed message in {response_time:.2f}s")
            return response
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            
            # Create error response
            error_response = ChatResponse(
                text=f"I apologize, but I encountered an error while processing your request: {str(e)}. Please try rephrasing your question or contact support if the issue persists.",
                data=[],
                insights=[],
                follow_up_questions=[
                    "Would you like to try a different query?",
                    "Can I help you with something else?",
                    "Would you like to see some example queries?"
                ],
                session_id=session_id,
                error=str(e)
            )
            
            return error_response
    
    async def _handle_greeting(self, intent: Intent, context: ConversationContext) -> ChatResponse:
        """Handle greeting messages."""
        greeting_text = f"""Hello! I'm your SEC Form D Data Librarian, an AI assistant specialized in analyzing private market filings and investment trends.

I have access to comprehensive Form D filing data from 2020-2024 (137,000+ filings) and can help you with:

🔍 **Data Exploration**
• Search filings by company, industry, amount, or date
• Analyze market trends and patterns
• Compare companies and sectors

📊 **Market Intelligence**
• Identify trending industries and hot deals
• Track investment flows and geographic patterns
• Benchmark deal sizes and terms

📧 **Alert Management**
• Set up custom email alerts for new filings
• Configure relevance thresholds
• Preview and customize alert content

💡 **Expert Insights**
• Explain regulatory requirements and filing details
• Provide market context and analysis
• Suggest relevant follow-up research

What would you like to explore today?"""
        
        return ChatResponse(
            text=greeting_text,
            data=[],
            insights=[],
            follow_up_questions=[
                "Show me trending industries this quarter",
                "Find recent biotech filings over $10M",
                "What are the largest deals this month?",
                "Help me set up email alerts"
            ],
            session_id=context.session_id
        )
    
    async def _handle_help_request(self, intent: Intent, context: ConversationContext) -> ChatResponse:
        """Handle help and capability requests."""
        help_text = """Here's what I can help you with:

**🔍 Search & Discovery**
• "Find biotech filings over $50M in 2024"
• "Show me all Series B rounds in California"
• "What companies filed in the last 30 days?"

**📈 Trends & Analysis**
• "What are the trending industries this quarter?"
• "Show me investment patterns in healthcare"
• "Compare deal sizes across sectors"

**🏢 Company Research**
• "Tell me about [Company Name]'s recent filings"
• "Show similar companies to [Company Name]"
• "What's the average deal size in fintech?"

**📧 Email Alerts**
• "Set up alerts for AI companies raising over $25M"
• "Show me what my email alert would look like"
• "Change my relevance threshold to 0.8"

**💡 Tips for Better Results**
• Be specific about amounts, dates, and industries
• Use natural language - I understand context
• Ask follow-up questions to dive deeper
• Request explanations if something isn't clear

What would you like to try?"""
        
        return ChatResponse(
            text=help_text,
            data=[],
            insights=[],
            follow_up_questions=[
                "Show me an example search",
                "What data do you have access to?",
                "How do I set up email alerts?",
                "What industries can you analyze?"
            ],
            session_id=context.session_id
        )
    
    async def _handle_data_query(self, intent: Intent, context: ConversationContext) -> ChatResponse:
        """Handle data queries (search, trends, analysis)."""
        try:
            # Generate database query
            query = await self.query_generator.create_query(intent)
            logger.info(f"Generated query: {query}")
            
            # Execute query
            results = await self._execute_query(query)
            logger.info(f"Query returned {len(results)} results")
            
            # Generate intelligent response
            response = await self.response_generator.create_response(
                intent=intent,
                results=results,
                context=context
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Error handling data query: {e}")
            raise
    
    async def _handle_email_alert(self, intent: Intent, context: ConversationContext) -> ChatResponse:
        """Handle email alert configuration and preview."""
        # Placeholder for email alert handling
        return ChatResponse(
            text="Email alert functionality is being implemented. You can currently configure alerts through the dashboard settings.",
            data=[],
            insights=[],
            follow_up_questions=[
                "Would you like to see the dashboard?",
                "Can I help you with a data query instead?",
                "What type of alerts are you interested in?"
            ],
            session_id=context.session_id
        )
    
    async def _handle_configuration(self, intent: Intent, context: ConversationContext) -> ChatResponse:
        """Handle configuration requests."""
        # Placeholder for configuration handling
        return ChatResponse(
            text="Configuration options are available through the dashboard. I can help you understand what settings are available and their impact on your analysis.",
            data=[],
            insights=[],
            follow_up_questions=[
                "What settings would you like to know about?",
                "Would you like to see current configuration?",
                "Can I help you with something else?"
            ],
            session_id=context.session_id
        )
    
    async def _handle_unknown_intent(self, intent: Intent, context: ConversationContext) -> ChatResponse:
        """Handle unknown or unclear intents."""
        return ChatResponse(
            text="I'm not sure I understand what you're looking for. Could you please rephrase your question or try one of these examples?",
            data=[],
            insights=[],
            follow_up_questions=[
                "Show me recent filings in [industry]",
                "What are the trending sectors this quarter?",
                "Find companies that raised over $[amount]",
                "Help me understand Form D requirements"
            ],
            session_id=context.session_id
        )
    
    async def _execute_query(self, query: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Execute a database query and return results."""
        try:
            if query["type"] == "search_filings":
                return await self.db_manager.search_filings(**query["params"])
            elif query["type"] == "trend_analysis":
                return await self.db_manager.get_filing_trends(**query["params"])
            elif query["type"] == "company_analysis":
                return await self.db_manager.get_company_filings(**query["params"])
            else:
                logger.warning(f"Unknown query type: {query['type']}")
                return []
                
        except Exception as e:
            logger.error(f"Error executing query: {e}")
            raise
    
    def _update_response_time(self, response_time: float) -> None:
        """Update average response time statistics."""
        current_avg = self.stats["average_response_time"]
        total_queries = self.stats["successful_queries"]
        
        # Calculate new average
        self.stats["average_response_time"] = (
            (current_avg * (total_queries - 1) + response_time) / total_queries
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """Get chatbot performance statistics."""
        return {
            **self.stats,
            "success_rate": (
                self.stats["successful_queries"] / self.stats["total_queries"] * 100
                if self.stats["total_queries"] > 0 else 0
            ),
            "active_sessions": self.context_manager.get_active_session_count()
        }
    
    async def get_session_history(self, session_id: str) -> List[ChatMessage]:
        """Get conversation history for a session."""
        context = self.context_manager.get_context(session_id)
        return context.messages if context else []
    
    async def clear_session(self, session_id: str) -> None:
        """Clear conversation history for a session."""
        self.context_manager.clear_context(session_id)
        logger.info(f"Cleared session: {session_id}")
