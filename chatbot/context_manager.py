#!/usr/bin/env python3
"""
Context Manager for SEC Form D Data Librarian Chatbot

Manages conversation context, session state, and user preferences
for personalized chatbot interactions.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Optional, List
import json

from chatbot.models import ConversationContext, ChatMessage, UserPreferences

logger = logging.getLogger(__name__)

class ContextManager:
    """
    Manages conversation context and session state for the chatbot.
    """
    
    def __init__(self, max_session_age_hours: int = 24, max_messages_per_session: int = 100):
        """
        Initialize the context manager.
        
        Args:
            max_session_age_hours: Maximum age of sessions before cleanup
            max_messages_per_session: Maximum messages to keep per session
        """
        self.sessions: Dict[str, ConversationContext] = {}
        self.max_session_age_hours = max_session_age_hours
        self.max_messages_per_session = max_messages_per_session
        
        logger.info("Context Manager initialized")
    
    def get_context(self, session_id: str) -> ConversationContext:
        """
        Get or create conversation context for a session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            ConversationContext for the session
        """
        if session_id not in self.sessions:
            # Create new context
            self.sessions[session_id] = ConversationContext(
                session_id=session_id,
                messages=[],
                user_preferences=self._get_default_preferences(),
                last_activity=datetime.now().isoformat()
            )
            logger.info(f"Created new context for session: {session_id}")
        else:
            # Update last activity
            self.sessions[session_id].last_activity = datetime.now().isoformat()
        
        return self.sessions[session_id]
    
    def update_context(self, session_id: str, context: ConversationContext) -> None:
        """
        Update conversation context for a session.
        
        Args:
            session_id: Session identifier
            context: Updated conversation context
        """
        # Trim messages if too many
        if len(context.messages) > self.max_messages_per_session:
            # Keep the most recent messages
            context.messages = context.messages[-self.max_messages_per_session:]
            logger.info(f"Trimmed messages for session {session_id} to {self.max_messages_per_session}")
        
        # Update context
        context.last_activity = datetime.now().isoformat()
        self.sessions[session_id] = context
    
    def clear_context(self, session_id: str) -> None:
        """
        Clear conversation context for a session.
        
        Args:
            session_id: Session identifier
        """
        if session_id in self.sessions:
            del self.sessions[session_id]
            logger.info(f"Cleared context for session: {session_id}")
    
    def get_active_session_count(self) -> int:
        """
        Get the number of active sessions.
        
        Returns:
            Number of active sessions
        """
        return len(self.sessions)
    
    def cleanup_old_sessions(self) -> int:
        """
        Clean up old inactive sessions.
        
        Returns:
            Number of sessions cleaned up
        """
        cutoff_time = datetime.now() - timedelta(hours=self.max_session_age_hours)
        old_sessions = []
        
        for session_id, context in self.sessions.items():
            last_activity = datetime.fromisoformat(context.last_activity)
            if last_activity < cutoff_time:
                old_sessions.append(session_id)
        
        # Remove old sessions
        for session_id in old_sessions:
            del self.sessions[session_id]
        
        if old_sessions:
            logger.info(f"Cleaned up {len(old_sessions)} old sessions")
        
        return len(old_sessions)
    
    def get_user_preferences(self, session_id: str) -> UserPreferences:
        """
        Get user preferences for a session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            UserPreferences object
        """
        context = self.get_context(session_id)
        return UserPreferences(**context.user_preferences)
    
    def update_user_preferences(self, session_id: str, preferences: UserPreferences) -> None:
        """
        Update user preferences for a session.
        
        Args:
            session_id: Session identifier
            preferences: Updated user preferences
        """
        context = self.get_context(session_id)
        context.user_preferences = preferences.dict()
        self.update_context(session_id, context)
        logger.info(f"Updated preferences for session: {session_id}")
    
    def get_conversation_summary(self, session_id: str) -> Dict[str, any]:
        """
        Get a summary of the conversation for a session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Dictionary with conversation summary
        """
        if session_id not in self.sessions:
            return {"error": "Session not found"}
        
        context = self.sessions[session_id]
        user_messages = context.get_user_messages()
        assistant_messages = context.get_assistant_messages()
        
        # Analyze conversation patterns
        topics_discussed = self._extract_topics(user_messages)
        query_types = self._analyze_query_types(user_messages)
        
        return {
            "session_id": session_id,
            "total_messages": len(context.messages),
            "user_messages": len(user_messages),
            "assistant_messages": len(assistant_messages),
            "session_duration": self._calculate_session_duration(context),
            "topics_discussed": topics_discussed,
            "query_types": query_types,
            "last_activity": context.last_activity
        }
    
    def get_recent_queries(self, session_id: str, count: int = 5) -> List[str]:
        """
        Get recent user queries from a session.
        
        Args:
            session_id: Session identifier
            count: Number of recent queries to return
            
        Returns:
            List of recent user queries
        """
        if session_id not in self.sessions:
            return []
        
        context = self.sessions[session_id]
        user_messages = context.get_user_messages()
        
        # Get the most recent queries
        recent_queries = [msg.content for msg in user_messages[-count:]]
        return recent_queries
    
    def has_discussed_topic(self, session_id: str, topic: str) -> bool:
        """
        Check if a topic has been discussed in the session.
        
        Args:
            session_id: Session identifier
            topic: Topic to check for
            
        Returns:
            True if topic has been discussed
        """
        if session_id not in self.sessions:
            return False
        
        context = self.sessions[session_id]
        
        # Check all messages for topic mentions
        for message in context.messages:
            if topic.lower() in message.content.lower():
                return True
        
        return False
    
    def get_session_statistics(self) -> Dict[str, any]:
        """
        Get overall session statistics.
        
        Returns:
            Dictionary with session statistics
        """
        if not self.sessions:
            return {
                "total_sessions": 0,
                "active_sessions": 0,
                "total_messages": 0,
                "average_messages_per_session": 0
            }
        
        total_messages = sum(len(context.messages) for context in self.sessions.values())
        
        return {
            "total_sessions": len(self.sessions),
            "active_sessions": len(self.sessions),  # All loaded sessions are considered active
            "total_messages": total_messages,
            "average_messages_per_session": total_messages / len(self.sessions) if self.sessions else 0,
            "oldest_session": min(context.last_activity for context in self.sessions.values()),
            "newest_session": max(context.last_activity for context in self.sessions.values())
        }
    
    def _get_default_preferences(self) -> Dict[str, any]:
        """Get default user preferences."""
        return {
            "preferred_industries": [],
            "default_date_range": "30d",
            "relevance_threshold": 0.7,
            "email_notifications": True,
            "data_visualization": True
        }
    
    def _extract_topics(self, messages: List[ChatMessage]) -> List[str]:
        """Extract topics discussed from user messages."""
        topics = set()
        
        # Common topics in SEC Form D queries
        topic_keywords = {
            "biotech": ["biotech", "biotechnology", "pharmaceutical", "pharma", "life sciences"],
            "technology": ["tech", "technology", "software", "saas", "ai", "artificial intelligence"],
            "healthcare": ["healthcare", "health", "medical", "clinical"],
            "fintech": ["fintech", "financial", "banking", "payments"],
            "energy": ["energy", "clean energy", "renewable", "solar", "wind"],
            "real estate": ["real estate", "property", "reit"],
            "funding": ["funding", "raised", "series", "round", "investment"],
            "trends": ["trend", "trending", "pattern", "growth", "analysis"],
            "companies": ["company", "companies", "firm", "corporation"]
        }
        
        for message in messages:
            content_lower = message.content.lower()
            for topic, keywords in topic_keywords.items():
                if any(keyword in content_lower for keyword in keywords):
                    topics.add(topic)
        
        return list(topics)
    
    def _analyze_query_types(self, messages: List[ChatMessage]) -> Dict[str, int]:
        """Analyze types of queries made by the user."""
        query_types = {
            "search": 0,
            "trend_analysis": 0,
            "company_analysis": 0,
            "help": 0,
            "configuration": 0
        }
        
        for message in messages:
            content_lower = message.content.lower()
            
            if any(word in content_lower for word in ["find", "search", "show", "get", "list"]):
                query_types["search"] += 1
            elif any(word in content_lower for word in ["trend", "pattern", "growth", "over time"]):
                query_types["trend_analysis"] += 1
            elif any(word in content_lower for word in ["tell me about", "company", "profile"]):
                query_types["company_analysis"] += 1
            elif any(word in content_lower for word in ["help", "how", "what can"]):
                query_types["help"] += 1
            elif any(word in content_lower for word in ["setting", "config", "threshold"]):
                query_types["configuration"] += 1
        
        return query_types
    
    def _calculate_session_duration(self, context: ConversationContext) -> str:
        """Calculate session duration in human-readable format."""
        if not context.messages:
            return "0 minutes"
        
        first_message_time = datetime.fromisoformat(context.messages[0].timestamp)
        last_message_time = datetime.fromisoformat(context.messages[-1].timestamp)
        
        duration = last_message_time - first_message_time
        
        if duration.total_seconds() < 60:
            return f"{int(duration.total_seconds())} seconds"
        elif duration.total_seconds() < 3600:
            return f"{int(duration.total_seconds() / 60)} minutes"
        else:
            hours = int(duration.total_seconds() / 3600)
            minutes = int((duration.total_seconds() % 3600) / 60)
            return f"{hours} hours, {minutes} minutes"
