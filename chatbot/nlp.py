#!/usr/bin/env python3
"""
Natural Language Processor for SEC Form D Data Librarian Chatbot

Handles intent classification, entity extraction, and natural language
understanding using the existing Mixtral LLM infrastructure.
"""

import json
import logging
import re
from typing import List, Dict, Any, Optional

from chatbot.models import Intent, Entity, IntentType, EntityType, ConversationContext
from models.persistent_model_manager import PersistentModelManager

logger = logging.getLogger(__name__)

class NLPProcessor:
    """
    Natural language processor for understanding user queries and extracting
    structured information for database operations.
    """
    
    def __init__(self):
        """Initialize the NLP processor."""
        self.persistent_model = PersistentModelManager()
        
        # Intent classification patterns
        self.intent_patterns = {
            IntentType.GREETING: [
                r'\b(hello|hi|hey|good morning|good afternoon|good evening)\b',
                r'\b(start|begin|new)\b',
                r'^(what can you|how can you|can you help)'
            ],
            IntentType.HELP: [
                r'\b(help|assistance|guide|tutorial|how to|what can)\b',
                r'\b(capabilities|features|functions|options)\b',
                r'\b(examples|sample|demo)\b'
            ],
            IntentType.SEARCH_FILINGS: [
                r'\b(find|search|show|get|list|display)\b.*\b(filing|company|companies)\b',
                r'\b(who|which|what).*\b(filed|raised|funding)\b',
                r'\b(recent|latest|new)\b.*\b(filing|deal|round)\b'
            ],
            IntentType.TREND_ANALYSIS: [
                r'\b(trend|trending|pattern|growth|decline)\b',
                r'\b(over time|quarterly|monthly|yearly)\b',
                r'\b(compare|comparison|vs|versus)\b.*\b(quarter|year|period)\b'
            ],
            IntentType.COMPANY_ANALYSIS: [
                r'\btell me about\b.*\b(company|corp|inc|ltd)\b',
                r'\b(profile|information|details|background)\b.*\b(company|firm)\b',
                r'\b(similar|like|comparable)\b.*\b(companies|firms)\b'
            ],
            IntentType.EMAIL_ALERT: [
                r'\b(alert|notification|notify|email)\b',
                r'\b(set up|create|configure)\b.*\b(alert|notification)\b',
                r'\b(when|if).*\b(new|filing|match)\b'
            ],
            IntentType.CONFIGURATION: [
                r'\b(setting|config|configure|preference)\b',
                r'\b(threshold|limit|parameter)\b',
                r'\b(change|update|modify|set)\b.*\b(setting|config)\b'
            ]
        }
        
        # Entity extraction patterns
        self.entity_patterns = {
            EntityType.AMOUNT: [
                r'\$(\d+(?:,\d{3})*(?:\.\d{2})?)\s*([kmb]?)',
                r'(\d+(?:,\d{3})*(?:\.\d{2})?)\s*(million|billion|thousand|dollars?)',
                r'(over|above|more than|greater than|>\s*)\$?(\d+(?:,\d{3})*(?:\.\d{2})?)\s*([kmb]?)',
                r'(under|below|less than|<\s*)\$?(\d+(?:,\d{3})*(?:\.\d{2})?)\s*([kmb]?)'
            ],
            EntityType.DATE_RANGE: [
                r'\b(last|past|previous)\s+(\d+)\s+(day|week|month|quarter|year)s?\b',
                r'\b(this|current)\s+(week|month|quarter|year)\b',
                r'\b(q[1-4]|quarter\s+[1-4])\s+(\d{4})\b',
                r'\b(\d{4})\b',
                r'\b(january|february|march|april|may|june|july|august|september|october|november|december)\s+(\d{4})\b'
            ],
            EntityType.INDUSTRY: [
                r'\b(biotech|biotechnology|pharmaceutical|pharma|life sciences)\b',
                r'\b(software|saas|tech|technology|ai|artificial intelligence)\b',
                r'\b(healthcare|health|medical|clinical)\b',
                r'\b(fintech|financial|banking|payments)\b',
                r'\b(energy|clean energy|renewable|solar|wind)\b',
                r'\b(real estate|property|reit)\b',
                r'\b(retail|e-commerce|consumer)\b'
            ],
            EntityType.LOCATION: [
                r'\b(california|ca|san francisco|silicon valley|bay area)\b',
                r'\b(new york|ny|nyc|manhattan)\b',
                r'\b(massachusetts|ma|boston|cambridge)\b',
                r'\b(texas|tx|austin|dallas|houston)\b',
                r'\b(washington|wa|seattle)\b'
            ],
            EntityType.COMPANY_NAME: [
                r'\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s+(Inc|Corp|Corporation|LLC|Ltd|Limited)\b',
                r'\b([A-Z][a-zA-Z]+(?:[A-Z][a-z]+)*)\b(?=\s+(?:filed|raised|announced))'
            ]
        }
        
        logger.info("NLP Processor initialized")
    
    async def parse_intent(self, message: str, context: Optional[ConversationContext] = None) -> Intent:
        """
        Parse user message to extract intent and entities.
        
        Args:
            message: User's natural language message
            context: Conversation context for better understanding
            
        Returns:
            Intent object with classification and extracted entities
        """
        try:
            logger.info(f"Parsing intent for message: {message[:100]}...")
            
            # Clean and normalize message
            normalized_message = self._normalize_message(message)
            
            # First, try pattern-based classification for speed
            pattern_intent = self._classify_intent_patterns(normalized_message)
            
            # Use LLM for more sophisticated understanding
            llm_intent = await self._classify_intent_llm(message, context)
            
            # Combine results (prefer LLM if confidence is high)
            if llm_intent.confidence > 0.8:
                intent_type = llm_intent.type
                confidence = llm_intent.confidence
            else:
                intent_type = pattern_intent.type
                confidence = max(pattern_intent.confidence, llm_intent.confidence)
            
            # Extract entities
            entities = await self._extract_entities(message, intent_type)
            
            intent = Intent(
                type=intent_type,
                confidence=confidence,
                entities=entities,
                raw_query=message
            )
            
            logger.info(f"Parsed intent: {intent.type} (confidence: {intent.confidence:.2f})")
            return intent
            
        except Exception as e:
            logger.error(f"Error parsing intent: {e}")
            # Return unknown intent as fallback
            return Intent(
                type=IntentType.UNKNOWN,
                confidence=0.0,
                entities=[],
                raw_query=message
            )
    
    def _normalize_message(self, message: str) -> str:
        """Normalize message for better pattern matching."""
        # Convert to lowercase
        normalized = message.lower()
        
        # Remove extra whitespace
        normalized = re.sub(r'\s+', ' ', normalized).strip()
        
        # Normalize common abbreviations
        abbreviations = {
            r'\bm\b': 'million',
            r'\bb\b': 'billion',
            r'\bk\b': 'thousand',
            r'\bq1\b': 'quarter 1',
            r'\bq2\b': 'quarter 2',
            r'\bq3\b': 'quarter 3',
            r'\bq4\b': 'quarter 4'
        }
        
        for pattern, replacement in abbreviations.items():
            normalized = re.sub(pattern, replacement, normalized)
        
        return normalized
    
    def _classify_intent_patterns(self, message: str) -> Intent:
        """Classify intent using pattern matching."""
        best_intent = IntentType.UNKNOWN
        best_confidence = 0.0
        
        for intent_type, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, message, re.IGNORECASE):
                    # Simple confidence based on pattern specificity
                    confidence = 0.7 + (len(pattern) / 100)  # More specific patterns get higher confidence
                    if confidence > best_confidence:
                        best_intent = intent_type
                        best_confidence = min(confidence, 0.95)  # Cap at 0.95
        
        return Intent(
            type=best_intent,
            confidence=best_confidence,
            entities=[],
            raw_query=message
        )
    
    async def _classify_intent_llm(self, message: str, context: Optional[ConversationContext] = None) -> Intent:
        """Classify intent using LLM for sophisticated understanding."""
        try:
            # Build context from conversation history
            context_text = ""
            if context and context.messages:
                recent_messages = context.get_recent_messages(3)
                context_text = "\n".join([f"{msg.sender}: {msg.content}" for msg in recent_messages[-3:]])
            
            # Create intent classification prompt
            prompt = f"""You are an expert at understanding user queries about SEC Form D filings and private market data.

Classify the user's intent from this list:
- greeting: User is greeting or starting conversation
- help: User wants help or information about capabilities
- search_filings: User wants to search or find specific filings/companies
- trend_analysis: User wants to analyze trends, patterns, or changes over time
- company_analysis: User wants detailed information about specific companies
- market_intelligence: User wants market insights, industry analysis, or benchmarking
- email_alert: User wants to set up or manage email alerts
- configuration: User wants to change settings or preferences
- unknown: Intent is unclear or doesn't match above categories

Recent conversation context:
{context_text}

User query: "{message}"

Respond with JSON in this format:
{{
    "intent": "intent_name",
    "confidence": 0.95,
    "reasoning": "Brief explanation of why this intent was chosen"
}}"""

            # Use persistent model for classification
            result = await self.persistent_model.analyze_filing(
                filing_data={"query": message, "context": context_text},
                prompt=prompt
            )
            
            # Parse LLM response
            try:
                if isinstance(result, dict) and 'summary' in result:
                    response_text = result['summary']
                else:
                    response_text = str(result)
                
                # Try to extract JSON from response
                json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
                if json_match:
                    parsed = json.loads(json_match.group())
                    intent_type = IntentType(parsed.get('intent', 'unknown'))
                    confidence = float(parsed.get('confidence', 0.5))
                else:
                    # Fallback parsing
                    intent_type = IntentType.UNKNOWN
                    confidence = 0.3
                    
            except (json.JSONDecodeError, ValueError, KeyError) as e:
                logger.warning(f"Failed to parse LLM intent response: {e}")
                intent_type = IntentType.UNKNOWN
                confidence = 0.3
            
            return Intent(
                type=intent_type,
                confidence=confidence,
                entities=[],
                raw_query=message
            )
            
        except Exception as e:
            logger.error(f"Error in LLM intent classification: {e}")
            return Intent(
                type=IntentType.UNKNOWN,
                confidence=0.0,
                entities=[],
                raw_query=message
            )
    
    async def _extract_entities(self, message: str, intent_type: IntentType) -> List[Entity]:
        """Extract entities from the message based on patterns and LLM."""
        entities = []
        
        # Pattern-based entity extraction
        pattern_entities = self._extract_entities_patterns(message)
        entities.extend(pattern_entities)
        
        # LLM-based entity extraction for more complex cases
        if intent_type in [IntentType.SEARCH_FILINGS, IntentType.TREND_ANALYSIS, IntentType.COMPANY_ANALYSIS]:
            llm_entities = await self._extract_entities_llm(message, intent_type)
            
            # Merge entities, avoiding duplicates
            for llm_entity in llm_entities:
                if not any(e.type == llm_entity.type and e.value.lower() == llm_entity.value.lower() for e in entities):
                    entities.append(llm_entity)
        
        return entities
    
    def _extract_entities_patterns(self, message: str) -> List[Entity]:
        """Extract entities using regex patterns."""
        entities = []
        
        for entity_type, patterns in self.entity_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, message, re.IGNORECASE)
                for match in matches:
                    entity_value = match.group().strip()
                    normalized_value = self._normalize_entity_value(entity_type, entity_value, match)
                    
                    entity = Entity(
                        type=entity_type,
                        value=entity_value,
                        confidence=0.8,  # Pattern-based entities get high confidence
                        normalized_value=normalized_value
                    )
                    entities.append(entity)
        
        return entities
    
    def _normalize_entity_value(self, entity_type: EntityType, value: str, match: re.Match) -> Any:
        """Normalize entity values for database queries."""
        if entity_type == EntityType.AMOUNT:
            # Parse amount and convert to number
            amount_text = value.lower()
            
            # Extract number
            number_match = re.search(r'(\d+(?:,\d{3})*(?:\.\d{2})?)', amount_text)
            if number_match:
                number = float(number_match.group().replace(',', ''))
                
                # Apply multipliers
                if 'million' in amount_text or 'm' in amount_text:
                    number *= 1_000_000
                elif 'billion' in amount_text or 'b' in amount_text:
                    number *= 1_000_000_000
                elif 'thousand' in amount_text or 'k' in amount_text:
                    number *= 1_000
                
                # Determine operator
                operator = "="
                if any(word in amount_text for word in ['over', 'above', 'more than', 'greater than', '>']):
                    operator = ">"
                elif any(word in amount_text for word in ['under', 'below', 'less than', '<']):
                    operator = "<"
                
                return {"operator": operator, "amount": number}
            
        elif entity_type == EntityType.INDUSTRY:
            # Normalize industry names
            industry_map = {
                'biotech': 'biotechnology',
                'pharma': 'pharmaceutical',
                'tech': 'technology',
                'ai': 'artificial intelligence',
                'saas': 'software',
                'fintech': 'financial technology'
            }
            normalized = value.lower()
            for key, mapped_value in industry_map.items():
                if key in normalized:
                    return mapped_value
            return value.lower()
            
        elif entity_type == EntityType.DATE_RANGE:
            # Parse date ranges
            # This is a simplified implementation
            return value.lower()
            
        return value
    
    async def _extract_entities_llm(self, message: str, intent_type: IntentType) -> List[Entity]:
        """Extract entities using LLM for complex cases."""
        try:
            prompt = f"""Extract structured information from this user query about SEC Form D filings.

User query: "{message}"
Intent: {intent_type}

Extract any of these entity types if present:
- company_name: Specific company names
- industry: Industry sectors (biotech, tech, healthcare, etc.)
- amount: Dollar amounts with operators (>, <, =)
- date_range: Time periods (quarters, years, months)
- location: Geographic locations (states, cities)

Respond with JSON array of entities:
[
    {{
        "type": "entity_type",
        "value": "extracted_text",
        "confidence": 0.95,
        "normalized_value": "processed_value"
    }}
]

If no entities found, return empty array: []"""

            result = await self.persistent_model.analyze_filing(
                filing_data={"query": message},
                prompt=prompt
            )
            
            # Parse LLM response
            try:
                if isinstance(result, dict) and 'summary' in result:
                    response_text = result['summary']
                else:
                    response_text = str(result)
                
                # Extract JSON array
                json_match = re.search(r'\[.*\]', response_text, re.DOTALL)
                if json_match:
                    entities_data = json.loads(json_match.group())
                    entities = []
                    
                    for entity_data in entities_data:
                        entity = Entity(
                            type=EntityType(entity_data.get('type')),
                            value=entity_data.get('value', ''),
                            confidence=float(entity_data.get('confidence', 0.5)),
                            normalized_value=entity_data.get('normalized_value')
                        )
                        entities.append(entity)
                    
                    return entities
                    
            except (json.JSONDecodeError, ValueError, KeyError) as e:
                logger.warning(f"Failed to parse LLM entity response: {e}")
            
            return []
            
        except Exception as e:
            logger.error(f"Error in LLM entity extraction: {e}")
            return []
