#!/usr/bin/env python3
"""
Response Generator for SEC Form D Data Librarian Chatbot

Generates intelligent, contextual responses from database query results
using the existing Mixtral LLM infrastructure.
"""

import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from chatbot.models import Intent, ChatResponse, Insight, ConversationContext, IntentType
from models.persistent_model_manager import PersistentModelManager

logger = logging.getLogger(__name__)

class ResponseGenerator:
    """
    Generates natural language responses from structured data results.
    """
    
    def __init__(self):
        """Initialize the response generator."""
        self.persistent_model = PersistentModelManager()
        
        # Response templates for different scenarios
        self.templates = {
            "no_results": "I didn't find any filings matching your criteria. Try adjusting your search parameters or broadening your query.",
            "single_result": "I found one filing that matches your search:",
            "multiple_results": "I found {count} filings matching your criteria. Here are the highlights:",
            "trend_analysis": "Based on the data analysis, here are the key trends:",
            "company_profile": "Here's what I found about {company}:",
            "market_intelligence": "Current market intelligence shows:"
        }
        
        logger.info("Response Generator initialized")
    
    async def create_response(self, intent: Intent, results: List[Dict[str, Any]], 
                            context: ConversationContext) -> ChatResponse:
        """
        Create an intelligent response from query results.
        
        Args:
            intent: Original user intent
            results: Database query results
            context: Conversation context
            
        Returns:
            ChatResponse with natural language text and structured data
        """
        try:
            logger.info(f"Generating response for {len(results)} results")
            
            if not results:
                return await self._create_no_results_response(intent, context)
            
            # Generate insights from results
            insights = await self._generate_insights(results, intent)
            
            # Create natural language response using LLM
            response_text = await self._generate_response_text(intent, results, insights, context)
            
            # Generate follow-up questions
            follow_ups = self._generate_follow_up_questions(intent, results, context)
            
            return ChatResponse(
                text=response_text,
                data=results[:10],  # Limit data in response to first 10 results
                insights=insights,
                follow_up_questions=follow_ups,
                session_id=context.session_id,
                metadata={
                    "total_results": len(results),
                    "intent_type": intent.type,
                    "response_generated_at": datetime.now().isoformat()
                }
            )
            
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return ChatResponse(
                text="I encountered an error while analyzing the results. Please try your query again.",
                data=[],
                insights=[],
                follow_up_questions=["Would you like to try a different search?"],
                session_id=context.session_id,
                error=str(e)
            )
    
    async def _create_no_results_response(self, intent: Intent, context: ConversationContext) -> ChatResponse:
        """Create response when no results are found."""
        suggestions = []
        
        # Provide helpful suggestions based on intent
        if intent.type == IntentType.SEARCH_FILINGS:
            suggestions = [
                "Try searching for a broader industry category",
                "Expand your date range",
                "Lower the minimum amount threshold",
                "Check your company name spelling"
            ]
        elif intent.type == IntentType.TREND_ANALYSIS:
            suggestions = [
                "Try a different time period",
                "Look at a broader industry category",
                "Check recent market activity instead"
            ]
        
        response_text = self.templates["no_results"]
        if suggestions:
            response_text += f"\n\nSuggestions:\n" + "\n".join(f"• {s}" for s in suggestions)
        
        return ChatResponse(
            text=response_text,
            data=[],
            insights=[],
            follow_up_questions=[
                "Show me recent filings in any industry",
                "What are the trending sectors this quarter?",
                "Help me refine my search criteria"
            ],
            session_id=context.session_id
        )
    
    async def _generate_insights(self, results: List[Dict[str, Any]], intent: Intent) -> List[Insight]:
        """Generate analytical insights from the results."""
        insights = []
        
        try:
            if not results:
                return insights
            
            # Calculate basic statistics
            total_filings = len(results)
            
            # Amount-based insights
            amounts = [r.get('offering_amount', 0) for r in results if r.get('offering_amount')]
            if amounts:
                avg_amount = sum(amounts) / len(amounts)
                max_amount = max(amounts)
                min_amount = min(amounts)
                
                insights.append(Insight(
                    type="financial",
                    title="Funding Statistics",
                    description=f"Average deal size: ${avg_amount:,.0f}. Range: ${min_amount:,.0f} - ${max_amount:,.0f}",
                    data={
                        "average_amount": avg_amount,
                        "max_amount": max_amount,
                        "min_amount": min_amount,
                        "total_amount": sum(amounts)
                    },
                    confidence=0.95
                ))
            
            # Industry distribution insights
            industries = {}
            for result in results:
                industry = result.get('industry_group', 'Unknown')
                industries[industry] = industries.get(industry, 0) + 1
            
            if len(industries) > 1:
                top_industry = max(industries, key=industries.get)
                insights.append(Insight(
                    type="industry",
                    title="Industry Distribution",
                    description=f"Most active industry: {top_industry} ({industries[top_industry]} filings)",
                    data=industries,
                    confidence=0.90
                ))
            
            # Geographic insights
            states = {}
            for result in results:
                state = result.get('issuer_state', 'Unknown')
                if state and state != 'Unknown':
                    states[state] = states.get(state, 0) + 1
            
            if states:
                top_state = max(states, key=states.get)
                insights.append(Insight(
                    type="geographic",
                    title="Geographic Distribution",
                    description=f"Most active state: {top_state} ({states[top_state]} filings)",
                    data=states,
                    confidence=0.85
                ))
            
            # Temporal insights (if filing dates available)
            filing_dates = [r.get('filing_date') for r in results if r.get('filing_date')]
            if filing_dates:
                recent_filings = sum(1 for date in filing_dates if date and date >= '2024-01-01')
                if recent_filings > 0:
                    insights.append(Insight(
                        type="temporal",
                        title="Recent Activity",
                        description=f"{recent_filings} of {total_filings} filings are from 2024",
                        data={"recent_count": recent_filings, "total_count": total_filings},
                        confidence=0.90
                    ))
            
        except Exception as e:
            logger.error(f"Error generating insights: {e}")
        
        return insights
    
    async def _generate_response_text(self, intent: Intent, results: List[Dict[str, Any]], 
                                    insights: List[Insight], context: ConversationContext) -> str:
        """Generate natural language response text using LLM."""
        try:
            # Prepare data summary for LLM
            data_summary = self._create_data_summary(results, insights)
            
            # Get conversation context
            recent_queries = context.get_recent_messages(3)
            context_text = "\n".join([f"{msg.sender}: {msg.content}" for msg in recent_queries])
            
            # Create response generation prompt
            prompt = f"""You are an expert SEC Form D filing analyst helping a user understand private market data.

User's original query: "{intent.raw_query}"
Intent type: {intent.type}

Data found: {len(results)} filings
{data_summary}

Key insights:
{self._format_insights_for_prompt(insights)}

Recent conversation context:
{context_text}

Generate a helpful, professional response that:
1. Directly addresses the user's query
2. Highlights the most important findings
3. Provides context and interpretation
4. Uses specific numbers and examples
5. Maintains a conversational but expert tone

Keep the response concise but informative (2-3 paragraphs maximum).
Focus on actionable insights and interesting patterns in the data."""

            # Use persistent model to generate response
            result = await self.persistent_model.analyze_filing(
                filing_data={"query": intent.raw_query, "results_count": len(results)},
                prompt=prompt
            )
            
            # Extract response text
            if isinstance(result, dict) and 'summary' in result:
                response_text = result['summary']
            else:
                response_text = str(result)
            
            # Clean up response text
            response_text = self._clean_response_text(response_text)
            
            return response_text
            
        except Exception as e:
            logger.error(f"Error generating response text: {e}")
            # Fallback to template-based response
            return self._create_fallback_response(intent, results, insights)
    
    def _create_data_summary(self, results: List[Dict[str, Any]], insights: List[Insight]) -> str:
        """Create a concise summary of the data for the LLM prompt."""
        if not results:
            return "No results found."
        
        summary_parts = []
        
        # Basic count
        summary_parts.append(f"Found {len(results)} filings")
        
        # Sample companies
        companies = [r.get('issuer_name', 'Unknown') for r in results[:3] if r.get('issuer_name')]
        if companies:
            summary_parts.append(f"Including companies like: {', '.join(companies)}")
        
        # Amount range
        amounts = [r.get('offering_amount', 0) for r in results if r.get('offering_amount')]
        if amounts:
            min_amount = min(amounts)
            max_amount = max(amounts)
            summary_parts.append(f"Offering amounts range from ${min_amount:,.0f} to ${max_amount:,.0f}")
        
        # Date range
        dates = [r.get('filing_date') for r in results if r.get('filing_date')]
        if dates:
            dates.sort()
            summary_parts.append(f"Filing dates from {dates[0]} to {dates[-1]}")
        
        return ". ".join(summary_parts) + "."
    
    def _format_insights_for_prompt(self, insights: List[Insight]) -> str:
        """Format insights for inclusion in LLM prompt."""
        if not insights:
            return "No specific insights generated."
        
        formatted = []
        for insight in insights:
            formatted.append(f"- {insight.title}: {insight.description}")
        
        return "\n".join(formatted)
    
    def _clean_response_text(self, text: str) -> str:
        """Clean up LLM-generated response text."""
        # Remove any JSON artifacts
        if text.startswith('{') and text.endswith('}'):
            try:
                parsed = json.loads(text)
                if 'response' in parsed:
                    text = parsed['response']
                elif 'summary' in parsed:
                    text = parsed['summary']
            except json.JSONDecodeError:
                pass
        
        # Clean up formatting
        text = text.strip()
        
        # Remove any markdown artifacts that might confuse users
        text = text.replace('**', '').replace('*', '').replace('##', '')
        
        return text
    
    def _create_fallback_response(self, intent: Intent, results: List[Dict[str, Any]], 
                                insights: List[Insight]) -> str:
        """Create a fallback response when LLM generation fails."""
        if not results:
            return self.templates["no_results"]
        
        count = len(results)
        
        if count == 1:
            result = results[0]
            company = result.get('issuer_name', 'Unknown Company')
            amount = result.get('offering_amount', 0)
            date = result.get('filing_date', 'Unknown Date')
            
            return f"I found one filing: {company} filed for ${amount:,.0f} on {date}."
        
        else:
            # Multiple results
            response = f"I found {count} filings matching your criteria."
            
            # Add top insights
            if insights:
                response += "\n\nKey findings:"
                for insight in insights[:2]:  # Top 2 insights
                    response += f"\n• {insight.description}"
            
            return response
    
    def _generate_follow_up_questions(self, intent: Intent, results: List[Dict[str, Any]], 
                                    context: ConversationContext) -> List[str]:
        """Generate relevant follow-up questions based on results and context."""
        follow_ups = []
        
        if not results:
            return [
                "Show me recent filings in any industry",
                "What are the trending sectors this quarter?",
                "Help me refine my search criteria"
            ]
        
        # Generate follow-ups based on intent type
        if intent.type == IntentType.SEARCH_FILINGS:
            follow_ups.extend([
                "Show me trends for these companies over time",
                "Find similar companies in the same industry",
                "What's the average deal size in this sector?"
            ])
            
            # Add industry-specific follow-ups
            industries = set(r.get('industry_group', '') for r in results if r.get('industry_group'))
            if industries:
                top_industry = list(industries)[0]
                follow_ups.append(f"Show me all {top_industry} filings this year")
        
        elif intent.type == IntentType.TREND_ANALYSIS:
            follow_ups.extend([
                "Compare this with the previous period",
                "Show me the largest deals in this trend",
                "Which companies are driving this trend?"
            ])
        
        elif intent.type == IntentType.COMPANY_ANALYSIS:
            follow_ups.extend([
                "Find competitors to these companies",
                "Show me their funding history",
                "What's their industry outlook?"
            ])
        
        # Add amount-based follow-ups if amounts are present
        amounts = [r.get('offering_amount', 0) for r in results if r.get('offering_amount')]
        if amounts:
            avg_amount = sum(amounts) / len(amounts)
            follow_ups.append(f"Show me deals larger than ${avg_amount:,.0f}")
        
        # Add geographic follow-ups if location data is present
        states = set(r.get('issuer_state', '') for r in results if r.get('issuer_state'))
        if states and len(states) > 1:
            follow_ups.append("Compare activity across different states")
        
        # Limit to 4 follow-up questions
        return follow_ups[:4]
