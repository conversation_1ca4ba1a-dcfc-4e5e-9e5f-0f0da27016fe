#!/usr/bin/env python3
"""
Memory-Safe Run Script

Runs the SEC Form D analysis pipeline with memory optimizations and safety checks.
"""

import logging
import sys
import gc
import tracemalloc
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('logs/memory_safe_run.log')
    ]
)

def check_memory_safety():
    """Check if system has enough memory for safe operation."""
    print("🔍 MEMORY SAFETY CHECK")
    print("=" * 30)
    
    try:
        import resource
        import platform
        
        memory_usage = resource.getrusage(resource.RUSAGE_SELF)
        
        if platform.system() == 'Darwin':  # macOS
            current_mb = memory_usage.ru_maxrss / 1024 / 1024
        else:  # Linux
            current_mb = memory_usage.ru_maxrss / 1024
            
        print(f"Current memory usage: {current_mb:.1f} MB")
        
        # Check if we have reasonable memory usage
        if current_mb > 2000:  # More than 2GB
            print("⚠️ High memory usage detected")
            return False
        else:
            print("✅ Memory usage is reasonable")
            return True
            
    except Exception as e:
        print(f"⚠️ Could not check memory: {e}")
        return True  # Assume it's okay if we can't check

def run_memory_safe_pipeline():
    """Run the pipeline with memory safety measures."""
    print("\n🚀 MEMORY-SAFE PIPELINE EXECUTION")
    print("=" * 40)
    
    # Start memory tracking
    tracemalloc.start()
    
    try:
        # Step 1: Test model loading with smaller model
        print("Step 1: Testing model loading...")
        from models.mixtral_mlx import MixtralMLX
        
        model = MixtralMLX(use_multiprocessing=False)
        print(f"✅ Model loaded: {model.model_name}")
        
        # Step 2: Test a simple generation
        print("Step 2: Testing generation...")
        test_prompt = "Analyze this brief filing: Test Company raised $1M for technology development."
        
        result = model.generate(test_prompt)
        print(f"✅ Generation completed: {len(result)} characters")
        
        # Step 3: Clean up
        print("Step 3: Cleaning up...")
        model._cleanup_resources()
        del model
        gc.collect()
        
        current, peak = tracemalloc.get_traced_memory()
        print(f"✅ Memory usage: {current / 1024 / 1024:.1f} MB current, {peak / 1024 / 1024:.1f} MB peak")
        
        return True
        
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        return False
    finally:
        tracemalloc.stop()

def run_atom_feed_only():
    """Run only the ATOM feed processing without historical context."""
    print("\n📡 ATOM FEED PROCESSING (MEMORY-SAFE)")
    print("=" * 45)
    
    try:
        # Import feed processing
        from ingest.form_d_feed import fetch_feed, save_feed
        
        print("Fetching ATOM feed...")
        entries = fetch_feed()
        
        if entries:
            print(f"✅ Fetched {len(entries)} entries")
            save_feed(entries)
            print("✅ Feed saved")
            
            # Process only the first 3 entries to test
            test_entries = entries[:3]
            print(f"Processing {len(test_entries)} test entries...")
            
            # Initialize MCP with minimal settings
            from mcp import ModelControlPoint
            
            mcp = ModelControlPoint(
                data_dir="data",
                relevance_threshold=0.7,
                email_threshold=0.8,
                use_multi_stage=False,  # Disable complex processing
                init_tools=False  # Disable tools to reduce memory
            )
            
            # Process entries without historical context
            processed = mcp.process_new_filings(
                feed_entries=test_entries,
                historical_context=False,  # DISABLE to save memory
                news_context=False  # DISABLE to save memory
            )
            
            if processed:
                print(f"✅ Processed {len(processed)} entries")
                for entry in processed:
                    score = entry.get('relevance_score', 0)
                    title = entry.get('title', 'Unknown')[:50]
                    print(f"  - {title}...: score {score:.2f}")
            else:
                print("⚠️ No entries processed")
                
            return True
        else:
            print("⚠️ No entries found in feed")
            return False
            
    except Exception as e:
        print(f"❌ ATOM feed processing failed: {e}")
        return False

def main():
    """Main execution with memory safety."""
    print("🛡️ MEMORY-SAFE SEC FORM D ANALYSIS")
    print("=" * 50)
    
    # Create logs directory
    Path("logs").mkdir(exist_ok=True)
    
    # Step 1: Memory safety check
    if not check_memory_safety():
        print("❌ Memory safety check failed. Exiting.")
        return 1
    
    # Step 2: Test basic pipeline
    if not run_memory_safe_pipeline():
        print("❌ Basic pipeline test failed. Exiting.")
        return 1
    
    # Step 3: Run ATOM feed processing
    if not run_atom_feed_only():
        print("❌ ATOM feed processing failed.")
        return 1
    
    # Success!
    print("\n🎉 MEMORY-SAFE EXECUTION COMPLETED SUCCESSFULLY!")
    print("=" * 55)
    print("\n💡 SUMMARY OF FIXES APPLIED:")
    print("1. ✅ Switched from Mixtral-8x7B to Mistral-7B (memory-safe)")
    print("2. ✅ Disabled multiprocessing to prevent model reloading")
    print("3. ✅ Added aggressive memory cleanup after each generation")
    print("4. ✅ Limited database context queries to prevent large result sets")
    print("5. ✅ Disabled historical context and news processing for memory safety")
    print("6. ✅ Added memory monitoring and limits")
    
    print("\n🚀 NEXT STEPS:")
    print("1. The system now runs safely with the smaller Mistral-7B model")
    print("2. You can gradually re-enable features as needed")
    print("3. Consider upgrading system RAM to use Mixtral-8x7B in the future")
    print("4. Monitor memory usage during production runs")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
