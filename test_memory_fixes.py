#!/usr/bin/env python3
"""
Memory Fixes Test Script

Tests the memory optimizations with a small subset of data to ensure
the 260GB memory allocation issue is resolved.
"""

import logging
import sys
import time
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('logs/memory_test.log')
    ]
)

# Import memory monitor
from utils.memory_monitor import MemoryMonitor

def test_model_memory():
    """Test model loading and generation memory usage."""
    print("=== TESTING MODEL MEMORY USAGE ===")
    
    monitor = MemoryMonitor()
    monitor.set_baseline()
    
    try:
        from models.mixtral_mlx import MixtralMLX
        
        # Test model loading
        print("Loading model...")
        model = MixtralMLX(use_multiprocessing=False)
        monitor.checkpoint("model_loaded")
        
        # Test multiple generations to check for memory leaks
        test_prompts = [
            "Analyze this company: Test Corp",
            "What is the market potential for this technology startup?",
            "Evaluate the investment risk for this filing."
        ]
        
        for i, prompt in enumerate(test_prompts):
            print(f"Testing generation {i+1}/3...")
            
            # Check memory before generation
            if not monitor.check_memory_limit(limit_gb=4.0):
                print(f"❌ Memory limit exceeded before generation {i+1}")
                break
                
            result = model.generate(prompt)
            monitor.checkpoint(f"generation_{i+1}")
            
            print(f"✅ Generation {i+1} completed: {len(result)} characters")
            
            # Force cleanup
            model._cleanup_resources()
            monitor.force_cleanup()
        
        print("✅ Model memory test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Model memory test failed: {e}")
        return False

def test_database_context():
    """Test database context retrieval memory usage."""
    print("\n=== TESTING DATABASE CONTEXT MEMORY ===")
    
    monitor = MemoryMonitor()
    monitor.set_baseline()
    
    try:
        from mcp.data_processor import DataProcessor
        
        processor = DataProcessor()
        monitor.checkpoint("processor_initialized")
        
        # Test with sample entries
        test_entries = [
            {
                'issuer_name': 'Test Company A',
                'industry': 'Technology',
                'offering_amount': 5000000.0
            },
            {
                'issuer_name': 'Test Company B', 
                'industry': 'Healthcare',
                'offering_amount': 10000000.0
            }
        ]
        
        for i, entry in enumerate(test_entries):
            print(f"Testing database context {i+1}/2...")
            
            context = processor.get_database_context(entry)
            monitor.checkpoint(f"context_{i+1}")
            
            print(f"✅ Context {i+1}: {len(context)} categories")
            
            # Force cleanup
            monitor.force_cleanup()
        
        print("✅ Database context test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Database context test failed: {e}")
        return False

def test_mcp_processing():
    """Test MCP processing with memory monitoring."""
    print("\n=== TESTING MCP PROCESSING MEMORY ===")
    
    monitor = MemoryMonitor()
    monitor.set_baseline()
    
    try:
        # Create a minimal test entry
        test_entries = [
            {
                'id': 'test-001',
                'title': 'Test Filing 1',
                'summary': 'Test company filing for technology startup',
                'issuer_name': 'Test Corp',
                'industry': 'Technology',
                'offering_amount': 1000000.0
            }
        ]
        
        from mcp import ModelControlPoint
        
        # Initialize MCP with conservative settings
        mcp = ModelControlPoint(
            data_dir="data",
            relevance_threshold=0.7,
            email_threshold=0.8,
            use_multi_stage=False,  # Use simple processing
            init_tools=False  # Skip tool initialization
        )
        
        monitor.checkpoint("mcp_initialized")
        
        # Check memory before processing
        if not monitor.check_memory_limit(limit_gb=4.0):
            print("❌ Memory limit exceeded before MCP processing")
            return False
        
        print("Processing test entries...")
        processed = mcp.process_new_filings(
            feed_entries=test_entries,
            historical_context=True,
            news_context=False  # Skip news to reduce complexity
        )
        
        monitor.checkpoint("mcp_processing_complete")
        
        if processed:
            print(f"✅ MCP processing completed: {len(processed)} entries")
            for entry in processed:
                score = entry.get('relevance_score', 0)
                print(f"  - {entry.get('id')}: score {score:.2f}")
        else:
            print("⚠️ No entries processed")
        
        # Force cleanup
        monitor.force_cleanup()
        
        print("✅ MCP processing test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ MCP processing test failed: {e}")
        return False

def main():
    """Run all memory tests."""
    print("🧪 MEMORY FIXES VALIDATION TEST")
    print("=" * 50)
    
    # Create logs directory
    Path("logs").mkdir(exist_ok=True)
    
    # Global memory monitor
    global_monitor = MemoryMonitor()
    global_monitor.set_baseline()
    
    results = []
    
    # Test 1: Model memory usage
    results.append(test_model_memory())
    global_monitor.checkpoint("test1_complete")
    
    # Test 2: Database context memory
    results.append(test_database_context())
    global_monitor.checkpoint("test2_complete")
    
    # Test 3: MCP processing memory
    results.append(test_mcp_processing())
    global_monitor.checkpoint("test3_complete")
    
    # Final results
    print("\n" + "=" * 50)
    print("🏁 MEMORY TEST RESULTS")
    print("=" * 50)
    
    test_names = ["Model Memory", "Database Context", "MCP Processing"]
    passed = sum(results)
    total = len(results)
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    # Final memory check
    final_info = global_monitor.get_memory_info()
    print(f"Final memory usage: {final_info['rss_mb']:.1f} MB RSS")
    
    if passed == total:
        print("\n🎉 All memory tests passed! The fixes appear to be working.")
        return 0
    else:
        print(f"\n⚠️ {total - passed} tests failed. Memory issues may persist.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
