# MCP Tools Configuration Guide

This document provides configuration instructions for the Model Control Point (MCP) tools integrated into the private-signals project.

## Overview

The MCP tools provide enhanced functionality for the SEC Form D analysis workflow:

- **News Scraper**: Enriches filings with relevant news context
- **JSON MCP Server**: Provides HTTP API access to MCP functionality
- **GitHub MCP Server**: Integrates with GitHub for issue tracking and collaboration
- **PostgreSQL MCP Server**: Provides database operations and data management
- **Microsoft 365 MCP Server**: Enables email alerts and calendar integration

## Environment Variables

### GitHub Integration
```bash
# Required for GitHub MCP Server
export GITHUB_TOKEN="your_github_personal_access_token"
```

### Microsoft 365 Integration
```bash
# Required for Microsoft 365 MCP Server
export MS365_CLIENT_ID="your_azure_app_client_id"
export MS365_CLIENT_SECRET="your_azure_app_client_secret"
export MS365_TENANT_ID="your_azure_tenant_id"
export MS365_REFRESH_TOKEN="your_refresh_token"
```

### PostgreSQL Integration
```bash
# Optional - for PostgreSQL MCP Server
export POSTGRES_CONNECTION_STRING="postgresql://username:password@localhost:5432/dbname"
```

## Tool Configuration

### 1. News Scraper Tool

**Purpose**: Scrapes news articles related to companies in Form D filings

**Configuration**: No additional configuration required

**Usage**:
```python
from mcp.tools.init_registry import init_registry, get_registry

init_registry()
registry = get_registry()

results = registry.execute_tool(
    "news_scraper",
    query="company name funding",
    max_results=5,
    days_back=30
)
```

### 2. JSON MCP Server

**Purpose**: Provides HTTP API access to MCP tools

**Configuration**: 
- Default host: localhost
- Default port: 8080

**Usage**:
```python
# Start server
registry.execute_tool(
    "json_mcp_server",
    action="start",
    host="localhost",
    port=8080
)

# Check status
status = registry.execute_tool("json_mcp_server", action="status")

# Stop server
registry.execute_tool("json_mcp_server", action="stop")
```

**API Endpoints**:
- `GET /` - Server info
- `GET /tools` - List available tools
- `GET /tools/{tool_name}` - Get tool schema
- `POST /tools/{tool_name}` - Execute tool
- `GET /history` - Get execution history

### 3. GitHub MCP Server

**Purpose**: Integrates with GitHub for issue tracking and collaboration

**Setup**:
1. Create a GitHub Personal Access Token
2. Set the `GITHUB_TOKEN` environment variable

**Usage**:
```python
# Search repositories
results = registry.execute_tool(
    "github_mcp_server",
    action="search",
    query="form d sec filing",
    type="repositories"
)

# Get issues from a repository
issues = registry.execute_tool(
    "github_mcp_server",
    action="get_issues",
    repo="username/repo",
    state="open"
)

# Post a comment
registry.execute_tool(
    "github_mcp_server",
    action="post_comment",
    repo="username/repo",
    issue_number=123,
    content="Analysis results..."
)
```

### 4. PostgreSQL MCP Server

**Purpose**: Provides database operations and data management

**Setup**:
1. Install PostgreSQL
2. Set the `POSTGRES_CONNECTION_STRING` environment variable (optional)

**Usage**:
```python
# Connect to database
registry.execute_tool(
    "postgresql_mcp_server",
    action="connect",
    connection_string="postgresql://user:pass@localhost:5432/db",
    connection_id="main_db"
)

# Query data
results = registry.execute_tool(
    "postgresql_mcp_server",
    action="query",
    connection_id="main_db",
    query="SELECT * FROM form_d_filings WHERE relevance_score > 0.7"
)

# Disconnect
registry.execute_tool(
    "postgresql_mcp_server",
    action="disconnect",
    connection_id="main_db"
)
```

### 5. Microsoft 365 MCP Server

**Purpose**: Enables email alerts and calendar integration

**Setup**:
1. Register an Azure application
2. Configure OAuth2 permissions for Microsoft Graph
3. Set the required environment variables

**Usage**:
```python
# Send email
registry.execute_tool(
    "microsoft365_mcp_server",
    action="send_email",
    to=["<EMAIL>"],
    subject="Form D Analysis Alert",
    body="<h1>High relevance filing detected</h1>",
    body_type="html"
)

# Create calendar event
registry.execute_tool(
    "microsoft365_mcp_server",
    action="create_event",
    subject="Review Form D Filing",
    start="2023-06-01T14:00:00Z",
    end="2023-06-01T15:00:00Z",
    body="Review high-priority filing"
)
```

## Integration with Main Application

The MCP tools are automatically integrated with the main application when `ModelControlPoint` is initialized:

```python
from mcp.core import ModelControlPoint

# Initialize with tools enabled
mcp = ModelControlPoint(
    init_tools=True,  # Enable tool registry
    use_multi_stage=True,
    use_prompt_evaluation=True,
    use_specialized_analysis=True
)

# Process filings with news context
processed_entries = mcp.process_new_filings(
    feed_entries=entries,
    historical_context=True,
    news_context=True  # Uses news scraper tool
)
```

## Troubleshooting

### Common Issues

1. **Tool registry not initialized**
   - Ensure all dependencies are installed: `pip install -r requirements.txt`
   - Check for import errors in the logs

2. **News scraper timeouts**
   - Check internet connectivity
   - Some news sites may block automated requests

3. **GitHub API rate limits**
   - Use authenticated requests with a personal access token
   - Respect rate limits (5000 requests/hour for authenticated users)

4. **Microsoft 365 authentication errors**
   - Verify Azure app configuration
   - Check token expiration and refresh

5. **PostgreSQL connection errors**
   - Verify connection string format
   - Ensure PostgreSQL server is running
   - Check firewall and network settings

### Logging

Enable debug logging to troubleshoot issues:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Security Considerations

1. **Environment Variables**: Store sensitive credentials in environment variables, not in code
2. **Token Management**: Regularly rotate API tokens and credentials
3. **Network Security**: Use HTTPS for all external API calls
4. **Database Security**: Use connection pooling and prepared statements
5. **Access Control**: Implement proper authentication for the JSON MCP server

## Performance Optimization

1. **Caching**: News scraper includes built-in caching (24-hour TTL)
2. **Rate Limiting**: Respect API rate limits for external services
3. **Connection Pooling**: PostgreSQL server supports connection pooling
4. **Batch Processing**: Process multiple entries efficiently
5. **Resource Cleanup**: Tools automatically clean up resources after use
