# Private Signals: SEC Form D Analysis System

A local AI agent for analyzing SEC Form D filings using MLX for efficient inference on Mac.

## Overview

This system monitors the SEC's ATOM feed for real-time Form D filings and processes quarterly bulk ZIP files to build a comprehensive database of private market activity. Using MLX-powered Mixtral inference, it analyzes new filings in the context of historical data to identify relevant investment opportunities and automatically generates email alerts with visualizations.

## Architecture

The system is built around a Model Control Point (MCP) architecture that orchestrates:

1. **Data Ingestion**
   - Real-time ATOM feed monitoring
   - Quarterly bulk ZIP processing
   - Vector database storage

2. **Analysis Engine**
   - MLX-powered Mixtral inference
   - Historical context enrichment
   - Relevance scoring

3. **Output Actions**
   - Heatmap visualization
   - Email alert generation
   - Threshold-based notifications

## Installation

### Prerequisites

- Python 3.10+
- Mac with Apple Silicon (for MLX acceleration)

### Setup

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/private-signals.git
   cd private-signals
   ```

2. Create a virtual environment:
   ```
   python -m venv .venv
   source .venv/bin/activate
   ```

3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

4. Set up environment variables:
   ```
   export HUGGINGFACE_TOKEN=your_hf_token  # For model downloads
   export SENDGRID_API_KEY=your_sendgrid_key  # For email sending
   ```

## Usage

### Running the Pipeline

Run the full pipeline with default settings:

```
python run_all.py
```

### Command Line Options

- `--legacy`: Use the legacy pipeline instead of MCP
- `--relevance-threshold`: Set threshold for considering a filing relevant (0.0-1.0)
- `--email-threshold`: Set threshold for triggering email alerts (0.0-1.0)
- `--continuous`: Run in continuous mode, polling at regular intervals
- `--poll-interval`: Minutes between polls in continuous mode (default: 15)

Examples:

Run once with custom thresholds:
```
python run_all.py --relevance-threshold 0.6 --email-threshold 0.8
```

Run in continuous mode with 30-minute polling interval:
```
python run_all.py --continuous --poll-interval 30
```

### SEC ATOM Feed Polling

You can also run the SEC ATOM feed poller directly:

```
python -m ingest.form_d_feed --poll --interval 15
```

This follows SEC best practices:
- Responsible polling (15-30 minute intervals)
- Proper User-Agent headers
- Rate limiting (max 10 requests per second)
- Robust error handling
- Comprehensive logging

### SEC API Integration

The system integrates with SEC APIs following best practices:

```
python -m ingest.sec_api --cik 0001640147 --type submissions
```

Features:
- Submissions API for company information
- Company Facts API for financial data
- Proper rate limiting and caching
- CIK normalization and extraction
- Automatic enrichment of Form D filings

## Components

### Data Ingestion

- `ingest/form_d_feed.py`: Fetches and processes the SEC ATOM feed
- `ingest/form_d.py`: Downloads and processes quarterly bulk ZIP files
- `ingest/zip_on_demand.py`: On-demand ZIP fetching for specific dates
- `ingest/sec_api.py`: SEC API client for enriching filings with company data

### Model Control Point (MCP)

- `mcp/core.py`: Core orchestration logic
- `mcp/data_processor.py`: Data normalization and enrichment
- `mcp/prompt_manager.py`: LLM prompt creation and output parsing

### Models

- `models/mixtral_mlx.py`: MLX-based Mixtral integration

### Visualization

- `visualization/heatmap.py`: Generates heatmap visualizations

### Email

- `emailer/build_html.py`: Builds HTML email content
- `emailer/sendgrid_client.py`: Sends emails via SendGrid or saves locally

## Development

### Adding New Features

1. **New Data Sources**: Extend the `ingest` package with new data source modules
2. **Enhanced Analysis**: Modify the prompt templates in `mcp/prompt_manager.py`
3. **New Visualizations**: Add new visualization types to the `visualization` package

### Testing

Run tests with:
```
python -m unittest discover tests
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- SEC EDGAR system for providing Form D data
- MLX team for the efficient inference library
- Mixtral model creators for the powerful LLM
