# rank/summarise.py
"""
Generate a summary of the top-ranked Form D filings.
"""

import json
from pathlib import Path
from datetime import datetime

def summarise_heatmap(ranked):
    """Generate a narrative summary of the ranked filings."""
    # Return a placeholder narrative
    return "Heat-map summary placeholder: Analysis of recent Form D filings shows increased activity in technology and healthcare sectors."

def generate_summary(top_n=10):
    """Generate a summary of the top-ranked Form D filings."""

    # Find the most recent ranked file
    ranked_dir = Path("data/ranked")
    ranked_dir.mkdir(exist_ok=True, parents=True)

    ranked_files = list(ranked_dir.glob("ranked_*.json"))

    if not ranked_files:
        print("[summary] No ranked files found. Creating a placeholder ranked file.")

        # Create a placeholder ranked file
        placeholder_file = ranked_dir / f"ranked_{datetime.now().strftime('%Y%m%d')}.json"

        # Create placeholder ranked data
        placeholder_data = []
        for i in range(10):
            placeholder_filing = {
                "id": f"placeholder_{i+1}",
                "issuerName": f"Example Corp {i+1}",
                "filingDate": datetime.now().isoformat(),
                "offeringAmount": str(1000000 * (i+1)),
                "industryGroup": "Technology",
                "summary": "This is a placeholder filing for development purposes.",
                "score": 0.9 - (i * 0.05)
            }
            placeholder_data.append(placeholder_filing)

        with open(placeholder_file, 'w') as f:
            json.dump(placeholder_data, f, indent=2)

        print(f"[summary] Created placeholder ranked file at {placeholder_file}")
        ranked_files = [placeholder_file]

    latest_file = sorted(ranked_files)[-1]
    print(f"[summary] Using {latest_file}")

    # Load the ranked data
    with open(latest_file, 'r') as f:
        ranked_data = json.load(f)

    # Take the top N
    top_filings = ranked_data[:top_n]

    # Generate the summary
    summary = {
        "generated_at": datetime.now().isoformat(),
        "top_filings": top_filings,
        "narrative": summarise_heatmap(ranked_data)
    }

    # Save the summary
    summary_dir = Path("data/summaries")
    summary_dir.mkdir(exist_ok=True, parents=True)

    summary_file = summary_dir / f"summary_{datetime.now().strftime('%Y%m%d')}.json"

    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)

    print(f"[summary] ✓ Saved summary to {summary_file}")
    return summary
