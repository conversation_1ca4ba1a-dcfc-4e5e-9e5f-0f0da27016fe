# rank/scorer.py
"""
Compute quantitative importance and then refine with an LLM reranker,
with full error handling and data validation for baseline records.
"""

import json
import numpy as np
import os
from pathlib import Path
from vector_store.embed import embedder, client
from mlx_lm import load, generate
from mlx_lm.sample_utils import make_sampler
from huggingface_hub import login

# === CONFIGURATION ===
K_NEIGHBORS    = 10
WEIGHTS        = {"amt": 0.4, "sec": 0.2, "emb": 0.2, "llm": 0.5}
REQUIRED_BASELINE_KEYS = ["issuerName", "filingDate"]

# Find the most recent JSONL file to use as baseline
def find_baseline_jsonl():
    jsonl_files = list(Path("data/raw").rglob("formd_*.jsonl"))
    if not jsonl_files:
        # Create a placeholder file if none exists
        placeholder_dir = Path("data/raw/placeholder")
        placeholder_dir.mkdir(parents=True, exist_ok=True)
        placeholder_file = placeholder_dir / "formd_placeholder.jsonl"
        placeholder_data = {
            "issuerName": "Example Corp",
            "filingDate": "2025-01-01",
            "offeringAmount": "1000000",
            "industryGroup": "Technology"
        }
        placeholder_file.write_text(json.dumps(placeholder_data))
        return str(placeholder_file)
    return str(sorted(jsonl_files)[-1])

BASELINE_JSONL = find_baseline_jsonl()

# === LOAD & VALIDATE BASELINE METRICS ===
def load_baseline():
    text = Path(BASELINE_JSONL).read_text(encoding="utf-8-sig")
    lines = [ln for ln in text.splitlines() if ln.strip()]
    records = []
    for idx, l in enumerate(lines, start=1):
        if not l.lstrip().startswith("{"):
            print(f"[scorer] SKIP non-JSON line #{idx}")
            continue
        try:
            obj = json.loads(l)
        except json.JSONDecodeError as e:
            print(f"[scorer] SKIP invalid JSON on line #{idx}: {e}")
            continue
        # schema validation
        if not all(obj.get(k) for k in REQUIRED_BASELINE_KEYS):
            print(f"[scorer] SKIP record missing keys on line #{idx}")
            continue
        records.append(obj)

    if not records:
        raise RuntimeError(f"[scorer] No valid baseline records in {BASELINE_JSONL}")

    # 1) Amount stats
    amounts = []
    for r in records:
        amt = r.get("offeringAmount") or r.get("offering", [{}])[0].get("amount", 0)
        try:
            amounts.append(float(amt))
        except:
            print(f"[scorer] WARN bad amount '{amt}', treating as 0")
            amounts.append(0.0)
    arr = np.array(amounts, dtype=float)
    mean_amt, std_amt = arr.mean(), arr.std(ddof=0)

    # 2) Sector counts
    sector_counts = {}
    for r in records:
        sec = r.get("industryGroup") or r.get("sector", "Unknown")
        sector_counts[sec] = sector_counts.get(sec, 0) + 1

    # 3) Embedding distance stats
    col = client.get_collection("formd_bulk")
    dists = []
    for r in records:
        txt = (r.get("issuerName", "") + " " + r.get("summary", ""))[:1000]
        vec = embedder.encode([txt], convert_to_numpy=True)[0]
        try:
            res = col.query(query_embeddings=[vec], n_results=K_NEIGHBORS)
            dists.append(np.mean(res["distances"][0]))
        except Exception as e:
            print(f"[scorer] WARN embedding query error: {e}")
    if not dists:
        raise RuntimeError("[scorer] No distances computed from baseline embeddings")
    mean_dist = float(np.mean(dists))
    std_dist  = float(np.std(dists, ddof=0))

    return mean_amt, std_amt, sector_counts, mean_dist, std_dist

# Preload baseline once
MEAN_AMT, STD_AMT, SECTOR_COUNTS, BASE_MEAN_DIST, BASE_STD_DIST = load_baseline()

# === LOAD LOCAL LLM FOR RERANKING ===
# Define mock classes for fallback
class MockModel:
    def __init__(self):
        pass

    def generate(self, *args, **kwargs):
        return "Mock LLM response"

class MockTokenizer:
    def __init__(self):
        pass

    def decode(self, *args, **kwargs):
        return "Mock decoded text"

try:
    # Try to authenticate with Hugging Face
    hf_token = os.environ.get("HUGGINGFACE_TOKEN")
    if hf_token:
        print("[scorer] Authenticating with Hugging Face using token from environment")
        login(token=hf_token)
    else:
        print("[scorer] No Hugging Face token found in environment")

    # Try multiple model options in case one is not available
    model_options = [
        "mlx-community/Mistral-7B-Instruct-v0.1-4bit-mlx",
        "mlx-community/Mixtral-8x7B-Instruct-v0.1-4bit-mlx",
        "mlx-community/Llama-2-7b-chat-4bit-mlx"
    ]

    _llm_model = None
    _llm_tok = None

    for model_name in model_options:
        try:
            print(f"[scorer] Trying to load model: {model_name}")
            _llm_model, _llm_tok = load(model_name)
            print(f"[scorer] Successfully loaded model: {model_name}")
            break
        except Exception as e:
            print(f"[scorer] Failed to load model {model_name}: {e}")

    # If all models failed, use mock
    if _llm_model is None:
        raise Exception("All model loading attempts failed")

except Exception as e:
    print(f"[scorer] WARNING: Could not load any LLM model: {e}")
    print("[scorer] Using mock LLM model for development")
    _llm_model = MockModel()
    _llm_tok = MockTokenizer()

# === SCORING FUNCTION ===
def rank_entries(entries, top_n_llm=20):
    """
    Given a list of feed entries (dicts with title/summary/etc),
    compute numeric scores, then LLM-rerank, and return sorted by final_score.
    """
    scored = []
    col = client.get_collection("formd_bulk")

    # 1) Numeric scoring
    for e in entries:
        amt = float(e.get("offeringAmount") or 0)
        z_amt = (amt - MEAN_AMT) / STD_AMT if STD_AMT > 0 else 0.0

        sec = e.get("industryGroup") or e.get("sector", "Unknown")
        novelty = 1.0 / (SECTOR_COUNTS.get(sec, 0) + 1)

        txt = (e.get("title", "") + " " + e.get("summary", ""))[:1000]
        vec = embedder.encode([txt], convert_to_numpy=True)[0]
        try:
            res = col.query(query_embeddings=[vec], n_results=K_NEIGHBORS)
            anomaly = (np.mean(res["distances"][0]) - BASE_MEAN_DIST) / BASE_STD_DIST if BASE_STD_DIST > 0 else 0.0
        except Exception as ex:
            print(f"[scorer] WARN anomaly computation error: {ex}")
            anomaly = 0.0

        num_score = WEIGHTS["amt"] * z_amt + WEIGHTS["sec"] * novelty + WEIGHTS["emb"] * anomaly

        # Store diagnostics
        e["_z_amt"]        = z_amt
        e["_novelty"]      = novelty
        e["_anomaly"]      = anomaly
        e["_numeric_score"]= num_score
        scored.append(e)

    if not scored:
        print("[scorer] WARNING no entries to score; returning empty list")
        return []

    # 2) LLM reranking on top-N
    batch = scored[:top_n_llm]
    prompt_items = [{
        "id": item["id"],
        "title": item.get("title", "")[:200],
        "amount": float(item.get("offeringAmount") or 0),
        "numeric": item["_numeric_score"]
    } for item in batch]

    prompt = f"""
You are an expert VC associate. For each JSON object below,
rate on a scale of 0–10 (integer) the "human_importance"
reflecting market signal strength, and provide a one-sentence rationale.
Respond strictly as JSON.

DATA:
{json.dumps(prompt_items, indent=2)}
"""

    try:
        # Check if we're using the mock model
        if isinstance(_llm_model, MockModel):
            # Using mock model
            print("[scorer] Using mock LLM for ranking")
            mock_response = [
                {"id": item["id"], "llm_score": 5, "rationale": "Mock rationale for scoring."}
                for item in prompt_items
            ]
            llm_results = mock_response
        else:
            # Using real model
            try:
                # Create a sampler with the desired temperature
                print("[scorer] Creating sampler with temperature 0.2")
                sampler = make_sampler(temp=0.2)

                # Generate text with the sampler instead of temp parameter
                print("[scorer] Generating text with MLX model")
                out = generate(_llm_model, _llm_tok, prompt, sampler=sampler, max_tokens=500)
                print(f"[scorer] Generation successful, output length: {len(out)}")

                # Parse the JSON response
                llm_results = json.loads(out)
                print(f"[scorer] Successfully parsed JSON response with {len(llm_results)} items")
            except Exception as inner_ex:
                print(f"[scorer] ERROR during MLX generation: {inner_ex}")
                raise inner_ex  # Re-raise to be caught by outer exception handler
    except Exception as ex:
        print(f"[scorer] ERROR LLM rerank: {ex}")
        # Provide mock results on error
        llm_results = [
            {"id": item["id"], "llm_score": 5, "rationale": "Fallback rationale due to error."}
            for item in prompt_items
        ]

    llm_map = {r.get("id"): r for r in llm_results if r.get("id")}

    # 3) Merge and compute final_score
    for e in scored:
        lr = llm_map.get(e["id"], {})
        e["llm_score"] = lr.get("llm_score", 0.0)
        e["rationale"] = lr.get("rationale", "")
        e["final_score"] = WEIGHTS["llm"] * e["llm_score"] + (1 - WEIGHTS["llm"]) * e["_numeric_score"]

    # 4) Sort & return
    return sorted(scored, key=lambda x: x["final_score"], reverse=True)
