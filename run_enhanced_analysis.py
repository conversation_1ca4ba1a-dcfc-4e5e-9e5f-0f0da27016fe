#!/usr/bin/env python3
"""
Enhanced SEC Form D Analysis Pipeline

Main script to run the enhanced financial analysis system with RAG capabilities,
professional analyst personas, and comprehensive financial frameworks.
"""

import sys
import argparse
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

# Add project root to path
sys.path.append('.')

# Import enhanced components
from mcp.enhanced_mcp import EnhancedModelControlPoint
from db.supabase_manager import SupabaseDatabaseManager
from data_processor.sec_data_processor import SECDataProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main enhanced analysis pipeline."""
    
    parser = argparse.ArgumentParser(description="Enhanced SEC Form D Analysis Pipeline")
    parser.add_argument("--mode", choices=["screening", "comprehensive", "risk_focused"], 
                       default="comprehensive", help="Analysis mode")
    parser.add_argument("--enable-rag", action="store_true", default=True,
                       help="Enable RAG-enhanced analysis")
    parser.add_argument("--limit", type=int, default=10,
                       help="Number of recent filings to analyze")
    parser.add_argument("--min-amount", type=int, default=1000000,
                       help="Minimum offering amount to analyze")
    parser.add_argument("--industry", type=str, default=None,
                       help="Filter by industry group")
    parser.add_argument("--continuous", action="store_true",
                       help="Run in continuous monitoring mode")
    parser.add_argument("--demo", action="store_true",
                       help="Run demonstration mode")
    
    args = parser.parse_args()
    
    print("🚀 Enhanced SEC Form D Analysis System")
    print("=" * 50)
    print(f"Mode: {args.mode}")
    print(f"RAG Enhanced: {args.enable_rag}")
    print(f"Analysis Limit: {args.limit}")
    print(f"Min Amount: ${args.min_amount:,}")
    if args.industry:
        print(f"Industry Filter: {args.industry}")
    print("=" * 50)
    
    try:
        # Initialize enhanced MCP system
        logger.info("Initializing Enhanced MCP system...")
        
        enhanced_mcp = EnhancedModelControlPoint(
            enable_financial_rag=args.enable_rag,
            financial_analysis_mode=args.mode,
            use_multi_stage=True,
            screening_threshold=0.3,
            relevance_threshold=0.7
        )
        
        if not enhanced_mcp.model_available:
            logger.error("❌ Model not available. Please check MLX setup.")
            return 1
        
        logger.info("✅ Enhanced MCP system initialized successfully")
        
        # Initialize database
        db = SupabaseDatabaseManager()
        
        if args.demo:
            # Run demonstration mode
            run_demonstration(enhanced_mcp, db, args)
        elif args.continuous:
            # Run continuous monitoring
            run_continuous_monitoring(enhanced_mcp, args)
        else:
            # Run batch analysis
            run_batch_analysis(enhanced_mcp, db, args)
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ Enhanced analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

def run_batch_analysis(enhanced_mcp: EnhancedModelControlPoint, 
                      db: SupabaseDatabaseManager, 
                      args) -> None:
    """Run batch analysis on recent filings."""
    
    logger.info(f"🔍 Searching for recent filings...")
    
    # Get recent filings
    try:
        filings = db.search_filings(
            industry_group=args.industry,
            min_amount=args.min_amount,
            limit=args.limit
        )
        
        if not filings:
            logger.warning("No filings found matching criteria")
            return
        
        logger.info(f"📊 Found {len(filings)} filings for analysis")
        
        # Convert to feed entry format for processing
        feed_entries = []
        for filing in filings:
            feed_entry = {
                "id": filing.get("id"),
                "title": filing.get("title", ""),
                "issuer_name": filing.get("issuer_name"),
                "industry_group": filing.get("industry_group"),
                "offering_amount": filing.get("offering_amount"),
                "filing_date": filing.get("filing_date"),
                "issuer_city": filing.get("issuer_city"),
                "issuer_state": filing.get("issuer_state"),
                "summary": filing.get("summary", "")
            }
            feed_entries.append(feed_entry)
        
        # Process with enhanced analysis
        logger.info("🧠 Running enhanced financial analysis...")
        
        processed_entries = enhanced_mcp.process_new_filings_enhanced(
            feed_entries,
            historical_context=True,
            news_context=False  # Disable news for batch processing
        )
        
        # Display results
        display_analysis_results(processed_entries, args.mode)
        
        # Get summary
        summary = enhanced_mcp.get_enhanced_analysis_summary(processed_entries)
        display_analysis_summary(summary)
        
    except Exception as e:
        logger.error(f"Batch analysis error: {e}")

def run_continuous_monitoring(enhanced_mcp: EnhancedModelControlPoint, args) -> None:
    """Run continuous monitoring mode."""
    
    logger.info("🔄 Starting continuous monitoring mode...")
    logger.info("Press Ctrl+C to stop")
    
    try:
        # Import the existing monitoring components
        from data_processor.sec_data_processor import SECDataProcessor
        
        processor = SECDataProcessor()
        
        while True:
            try:
                # Get new ATOM feed entries
                logger.info("📡 Checking SEC ATOM feed for new entries...")
                
                new_entries = processor.get_new_atom_entries()
                
                if new_entries:
                    logger.info(f"📊 Found {len(new_entries)} new entries")
                    
                    # Process with enhanced analysis
                    processed_entries = enhanced_mcp.process_new_filings_enhanced(
                        new_entries,
                        historical_context=True,
                        news_context=True
                    )
                    
                    # Display results
                    display_analysis_results(processed_entries, args.mode)
                    
                    # Check for high-relevance filings
                    high_relevance_filings = [
                        entry for entry in processed_entries
                        if entry.get("enhanced_analysis", {}).get("relevance_score", 0) > 0.8
                    ]
                    
                    if high_relevance_filings:
                        logger.info(f"🚨 {len(high_relevance_filings)} high-relevance filings detected!")
                        # Here you could trigger email alerts or other notifications
                
                else:
                    logger.info("No new entries found")
                
                # Wait before next check
                import time
                time.sleep(900)  # 15 minutes
                
            except KeyboardInterrupt:
                logger.info("🛑 Continuous monitoring stopped by user")
                break
            except Exception as e:
                logger.error(f"Monitoring error: {e}")
                import time
                time.sleep(60)  # Wait 1 minute before retrying
                
    except Exception as e:
        logger.error(f"Continuous monitoring setup error: {e}")

def run_demonstration(enhanced_mcp: EnhancedModelControlPoint, 
                     db: SupabaseDatabaseManager, 
                     args) -> None:
    """Run demonstration mode."""
    
    logger.info("🎭 Running enhanced analysis demonstration...")
    
    # Get a sample filing
    try:
        filings = db.search_filings(min_amount=5000000, limit=1)
        
        if not filings:
            logger.warning("No sample filings available for demonstration")
            return
        
        sample_filing = filings[0]
        
        print(f"\n📋 DEMONSTRATION FILING")
        print(f"Company: {sample_filing.get('issuer_name', 'Unknown')}")
        print(f"Industry: {sample_filing.get('industry_group', 'Unknown')}")
        print(f"Amount: ${sample_filing.get('offering_amount', 0):,.0f}")
        
        # Show persona selection
        persona = enhanced_mcp.enhanced_prompt_manager.select_analyst_persona(sample_filing)
        print(f"Selected Analyst Persona: {persona}")
        
        # Generate enhanced prompt
        enhanced_prompt = enhanced_mcp.enhanced_prompt_manager.create_enhanced_analysis_prompt(
            sample_filing,
            analysis_type=args.mode
        )
        
        print(f"\nEnhanced Prompt Length: {len(enhanced_prompt):,} characters")
        print(f"Includes: Financial frameworks, market context, analyst expertise")
        
        print(f"\n✅ Enhanced analysis system is ready for production use!")
        
    except Exception as e:
        logger.error(f"Demonstration error: {e}")

def display_analysis_results(processed_entries: List[Dict[str, Any]], mode: str) -> None:
    """Display analysis results."""
    
    print(f"\n📊 ENHANCED ANALYSIS RESULTS ({mode.upper()} MODE)")
    print("=" * 60)
    
    for i, entry in enumerate(processed_entries, 1):
        enhanced_analysis = entry.get("enhanced_analysis", {})
        
        print(f"\n{i}. {entry.get('issuer_name', 'Unknown Company')}")
        print(f"   Industry: {entry.get('industry_group', 'Unknown')}")
        print(f"   Amount: ${entry.get('offering_amount', 0):,.0f}")
        
        relevance_score = enhanced_analysis.get("relevance_score", 0.0)
        print(f"   Relevance Score: {relevance_score:.2f}")
        
        if relevance_score > 0.8:
            print(f"   🚨 HIGH RELEVANCE - Requires immediate attention")
        elif relevance_score > 0.6:
            print(f"   ⚠️  MEDIUM RELEVANCE - Worth monitoring")
        else:
            print(f"   ℹ️  LOW RELEVANCE - Standard filing")
        
        # Show key analysis points if available
        if "investment_thesis" in enhanced_analysis:
            thesis = enhanced_analysis["investment_thesis"]
            if thesis and len(thesis) > 50:
                print(f"   Investment Thesis: {thesis[:100]}...")
        
        if "recommendation" in enhanced_analysis:
            recommendation = enhanced_analysis["recommendation"]
            if recommendation:
                print(f"   Recommendation: {recommendation}")

def display_analysis_summary(summary: Dict[str, Any]) -> None:
    """Display analysis summary."""
    
    print(f"\n📈 ANALYSIS SUMMARY")
    print("=" * 30)
    print(f"Total Entries Processed: {summary['total_entries_processed']}")
    print(f"Enhanced Analysis Count: {summary['enhanced_analysis_count']}")
    print(f"RAG Enhancement Rate: {summary['rag_enabled_percentage']:.1f}%")
    print(f"Analysis Mode: {summary['analysis_mode']}")
    print(f"Average Relevance Score: {summary['average_relevance_score']:.2f}")
    
    distribution = summary['relevance_distribution']
    print(f"\nRelevance Distribution:")
    print(f"  High (>0.7): {distribution['high_relevance']}")
    print(f"  Medium (0.4-0.7): {distribution['medium_relevance']}")
    print(f"  Low (<0.4): {distribution['low_relevance']}")

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
