#!/usr/bin/env python3
"""
Performance Monitor for SEC Form D Analysis System

Tracks and reports performance metrics for the optimized system:
- Analysis time per filing
- Cache hit rates
- Model load times
- Memory usage
- End-to-end pipeline performance
"""

import json
import time
import logging
import psutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional

from models.persistent_model_manager import PersistentModelManager
from db.database import DatabaseManager

class PerformanceMonitor:
    """
    Monitor and track performance metrics for the SEC Form D analysis system.
    """
    
    def __init__(self, data_dir: str = "data"):
        """
        Initialize the performance monitor.
        
        Args:
            data_dir: Directory for data storage
        """
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Performance tracking
        self.metrics = {
            'analysis_times': [],
            'cache_hits': 0,
            'cache_misses': 0,
            'model_load_time': None,
            'memory_usage': [],
            'pipeline_times': [],
            'error_count': 0
        }
        
        # Database for persistent metrics
        self.db_manager = DatabaseManager()
        
        # Set up logging
        self.logger = logging.getLogger(__name__)
        
        # Performance targets
        self.targets = {
            'analysis_time_per_filing': 30,  # seconds
            'cache_hit_rate': 80,  # percent
            'model_load_time': 120,  # seconds
            'memory_usage_peak': 4,  # GB
            'pipeline_time': 300  # seconds (5 minutes)
        }
    
    def start_monitoring(self) -> None:
        """Start performance monitoring session."""
        self.session_start = time.time()
        self.initial_memory = psutil.virtual_memory().used / (1024**3)  # GB
        self.logger.info("Performance monitoring started")
    
    def track_analysis_time(self, filing_id: str, start_time: float, end_time: float, 
                           cached: bool = False) -> None:
        """
        Track analysis time for a filing.
        
        Args:
            filing_id: ID of the filing
            start_time: Start timestamp
            end_time: End timestamp
            cached: Whether result was cached
        """
        analysis_time = end_time - start_time
        
        self.metrics['analysis_times'].append({
            'filing_id': filing_id,
            'time_seconds': analysis_time,
            'cached': cached,
            'timestamp': datetime.now().isoformat()
        })
        
        if cached:
            self.metrics['cache_hits'] += 1
        else:
            self.metrics['cache_misses'] += 1
        
        # Log if analysis time exceeds target
        if analysis_time > self.targets['analysis_time_per_filing']:
            self.logger.warning(f"Analysis time exceeded target: {analysis_time:.2f}s > {self.targets['analysis_time_per_filing']}s")
    
    def track_model_load_time(self, load_time: float) -> None:
        """
        Track model loading time.
        
        Args:
            load_time: Time taken to load model in seconds
        """
        self.metrics['model_load_time'] = load_time
        
        if load_time > self.targets['model_load_time']:
            self.logger.warning(f"Model load time exceeded target: {load_time:.2f}s > {self.targets['model_load_time']}s")
        else:
            self.logger.info(f"Model loaded in {load_time:.2f}s (target: {self.targets['model_load_time']}s)")
    
    def track_memory_usage(self) -> None:
        """Track current memory usage."""
        current_memory = psutil.virtual_memory().used / (1024**3)  # GB
        
        self.metrics['memory_usage'].append({
            'memory_gb': current_memory,
            'timestamp': datetime.now().isoformat()
        })
        
        # Check if memory usage exceeds target
        if current_memory > self.targets['memory_usage_peak']:
            self.logger.warning(f"Memory usage exceeded target: {current_memory:.2f}GB > {self.targets['memory_usage_peak']}GB")
    
    def track_pipeline_time(self, start_time: float, end_time: float, 
                           entries_processed: int) -> None:
        """
        Track end-to-end pipeline performance.
        
        Args:
            start_time: Pipeline start timestamp
            end_time: Pipeline end timestamp
            entries_processed: Number of entries processed
        """
        pipeline_time = end_time - start_time
        
        self.metrics['pipeline_times'].append({
            'time_seconds': pipeline_time,
            'entries_processed': entries_processed,
            'time_per_entry': pipeline_time / entries_processed if entries_processed > 0 else 0,
            'timestamp': datetime.now().isoformat()
        })
        
        if pipeline_time > self.targets['pipeline_time']:
            self.logger.warning(f"Pipeline time exceeded target: {pipeline_time:.2f}s > {self.targets['pipeline_time']}s")
        else:
            self.logger.info(f"Pipeline completed in {pipeline_time:.2f}s (target: {self.targets['pipeline_time']}s)")
    
    def track_error(self, error_type: str, error_message: str) -> None:
        """
        Track errors during processing.
        
        Args:
            error_type: Type of error
            error_message: Error message
        """
        self.metrics['error_count'] += 1
        self.logger.error(f"Error tracked: {error_type} - {error_message}")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """
        Generate a comprehensive performance report.
        
        Returns:
            Performance report dictionary
        """
        # Calculate cache hit rate
        total_requests = self.metrics['cache_hits'] + self.metrics['cache_misses']
        cache_hit_rate = (self.metrics['cache_hits'] / total_requests * 100) if total_requests > 0 else 0
        
        # Calculate average analysis time
        analysis_times = [a['time_seconds'] for a in self.metrics['analysis_times'] if not a['cached']]
        avg_analysis_time = sum(analysis_times) / len(analysis_times) if analysis_times else 0
        
        # Calculate peak memory usage
        memory_readings = [m['memory_gb'] for m in self.metrics['memory_usage']]
        peak_memory = max(memory_readings) if memory_readings else 0
        
        # Calculate average pipeline time
        pipeline_times = [p['time_seconds'] for p in self.metrics['pipeline_times']]
        avg_pipeline_time = sum(pipeline_times) / len(pipeline_times) if pipeline_times else 0
        
        # Performance vs targets
        performance_vs_targets = {
            'analysis_time_per_filing': {
                'actual': avg_analysis_time,
                'target': self.targets['analysis_time_per_filing'],
                'status': 'PASS' if avg_analysis_time <= self.targets['analysis_time_per_filing'] else 'FAIL'
            },
            'cache_hit_rate': {
                'actual': cache_hit_rate,
                'target': self.targets['cache_hit_rate'],
                'status': 'PASS' if cache_hit_rate >= self.targets['cache_hit_rate'] else 'FAIL'
            },
            'model_load_time': {
                'actual': self.metrics['model_load_time'],
                'target': self.targets['model_load_time'],
                'status': 'PASS' if (self.metrics['model_load_time'] or 0) <= self.targets['model_load_time'] else 'FAIL'
            },
            'memory_usage_peak': {
                'actual': peak_memory,
                'target': self.targets['memory_usage_peak'],
                'status': 'PASS' if peak_memory <= self.targets['memory_usage_peak'] else 'FAIL'
            },
            'pipeline_time': {
                'actual': avg_pipeline_time,
                'target': self.targets['pipeline_time'],
                'status': 'PASS' if avg_pipeline_time <= self.targets['pipeline_time'] else 'FAIL'
            }
        }
        
        return {
            'timestamp': datetime.now().isoformat(),
            'session_duration': time.time() - self.session_start if hasattr(self, 'session_start') else 0,
            'total_analyses': len(self.metrics['analysis_times']),
            'cache_hit_rate_percent': cache_hit_rate,
            'average_analysis_time_seconds': avg_analysis_time,
            'model_load_time_seconds': self.metrics['model_load_time'],
            'peak_memory_usage_gb': peak_memory,
            'average_pipeline_time_seconds': avg_pipeline_time,
            'error_count': self.metrics['error_count'],
            'performance_vs_targets': performance_vs_targets,
            'raw_metrics': self.metrics
        }
    
    def save_report(self, filename: Optional[str] = None) -> str:
        """
        Save performance report to file.
        
        Args:
            filename: Optional filename (defaults to timestamp-based name)
            
        Returns:
            Path to saved report file
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"performance_report_{timestamp}.json"
        
        report_path = self.data_dir / "performance_reports" / filename
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        report = self.get_performance_report()
        
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        self.logger.info(f"Performance report saved to: {report_path}")
        return str(report_path)
    
    def print_summary(self) -> None:
        """Print a summary of performance metrics."""
        report = self.get_performance_report()
        
        print("\n" + "="*60)
        print("SEC FORM D ANALYSIS - PERFORMANCE SUMMARY")
        print("="*60)
        
        print(f"Session Duration: {report['session_duration']:.2f} seconds")
        print(f"Total Analyses: {report['total_analyses']}")
        print(f"Cache Hit Rate: {report['cache_hit_rate_percent']:.1f}%")
        print(f"Average Analysis Time: {report['average_analysis_time_seconds']:.2f}s")
        print(f"Model Load Time: {report['model_load_time_seconds']:.2f}s" if report['model_load_time_seconds'] else "Model Load Time: Not measured")
        print(f"Peak Memory Usage: {report['peak_memory_usage_gb']:.2f}GB")
        print(f"Average Pipeline Time: {report['average_pipeline_time_seconds']:.2f}s")
        print(f"Error Count: {report['error_count']}")
        
        print("\nPERFORMANCE VS TARGETS:")
        print("-" * 40)
        for metric, data in report['performance_vs_targets'].items():
            status_icon = "✅" if data['status'] == 'PASS' else "❌"
            print(f"{status_icon} {metric}: {data['actual']:.2f} (target: {data['target']:.2f})")
        
        print("="*60)

def main():
    """Main function for standalone performance monitoring."""
    monitor = PerformanceMonitor()
    
    # Example usage
    monitor.start_monitoring()
    
    # Simulate some metrics
    monitor.track_model_load_time(45.0)  # 45 seconds
    monitor.track_analysis_time("test_filing_1", time.time(), time.time() + 25, cached=False)
    monitor.track_analysis_time("test_filing_2", time.time(), time.time() + 2, cached=True)
    monitor.track_memory_usage()
    monitor.track_pipeline_time(time.time(), time.time() + 180, 2)
    
    # Print summary and save report
    monitor.print_summary()
    report_path = monitor.save_report()
    print(f"\nDetailed report saved to: {report_path}")

if __name__ == "__main__":
    main()
