#!/usr/bin/env python3
"""
Database-First Analysis Pipeline

This script analyzes existing Form D filings from the Supabase database
instead of fetching new data from the SEC ATOM feed. This is more efficient
for testing and analysis of historical data.

Usage:
    python run_database_analysis.py --limit 20 --days-back 30
    python run_database_analysis.py --min-amount 10000000 --industry Technology
"""

import logging
import sys
import os
import argparse
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import database and MCP components
from db.supabase_manager import SupabaseDatabaseManager
from mcp.core import ModelControlPoint

def convert_db_filing_to_feed_format(db_filing: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert database filing format to the format expected by MCP.
    
    Args:
        db_filing: Filing data from database
        
    Returns:
        Filing data in feed format
    """
    # Extract JSON data if available
    json_data = db_filing.get('json_data', {})
    
    # Create feed-format entry
    feed_entry = {
        "id": f"db-filing-{db_filing['id']}",
        "title": f"Form D Filing - {db_filing.get('issuer_name', 'Unknown Issuer')}",
        "link": f"https://www.sec.gov/Archives/edgar/data/{db_filing.get('accession_number', '')}",
        "updated": db_filing.get('filing_date', datetime.now().isoformat()),
        "summary": f"Form D filing by {db_filing.get('issuer_name', 'Unknown')}",
        
        # Core filing data
        "accession_number": db_filing.get('accession_number'),
        "issuer_name": db_filing.get('issuer_name'),
        "filing_date": db_filing.get('filing_date'),
        "offering_amount": db_filing.get('offering_amount'),
        "industry_group": db_filing.get('industry_group'),
        "issuer_city": db_filing.get('issuer_city'),
        "issuer_state": db_filing.get('issuer_state'),
        "issuer_zip": db_filing.get('issuer_zip'),
        "offering_type": db_filing.get('offering_type'),
        "minimum_investment": db_filing.get('minimum_investment'),
        "total_amount_sold": db_filing.get('total_amount_sold'),
        "total_remaining": db_filing.get('total_remaining'),
        
        # Additional metadata
        "source": "database",
        "database_id": db_filing['id'],
        "created_at": db_filing.get('created_at'),
        "updated_at": db_filing.get('updated_at'),
        
        # Include full JSON data for detailed analysis
        "raw_data": json_data
    }
    
    return feed_entry

def get_filings_from_database(
    db_manager: SupabaseDatabaseManager,
    limit: int = 50,
    days_back: int = 30,
    min_amount: Optional[float] = None,
    max_amount: Optional[float] = None,
    industry: Optional[str] = None,
    issuer_name: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Retrieve filings from database with filtering options.

    Args:
        db_manager: Database manager instance
        limit: Maximum number of filings to retrieve
        days_back: Number of days back to search
        min_amount: Minimum offering amount
        max_amount: Maximum offering amount
        industry: Industry group filter
        issuer_name: Issuer name filter (partial match)

    Returns:
        List of filings in feed format
    """
    # Get the latest filing date from database
    cursor = db_manager.get_cursor()
    cursor.execute("SELECT MAX(filing_date::date) as latest_date FROM form_d_filings WHERE filing_date IS NOT NULL")
    result = cursor.fetchone()
    latest_date = result['latest_date'] if result and result['latest_date'] else datetime.now().date()

    # Calculate date range from latest filing date
    end_date = latest_date
    start_date = end_date - timedelta(days=days_back)
    
    logger.info(f"Searching database for filings from {start_date} to {end_date}")
    logger.info(f"Filters: limit={limit}, min_amount={min_amount}, max_amount={max_amount}")
    logger.info(f"         industry={industry}, issuer_name={issuer_name}")
    
    # Search database (skip date filters for now due to mixed date formats)
    db_filings = db_manager.search_filings(
        issuer_name=issuer_name,
        industry_group=industry,
        min_amount=min_amount,
        max_amount=max_amount,
        date_from=None,  # Skip date filtering due to mixed formats
        date_to=None,    # Skip date filtering due to mixed formats
        limit=limit * 2  # Get more results to filter manually
    )
    
    logger.info(f"Found {len(db_filings)} filings in database")

    # Manual date filtering due to mixed date formats
    filtered_filings = []
    for filing in db_filings:
        filing_date_str = filing.get('filing_date', '')
        if filing_date_str:
            try:
                # Try to parse different date formats
                if 'T' in filing_date_str or ' ' in filing_date_str:
                    # Format: YYYY-MM-DD HH:MM:SS or YYYY-MM-DDTHH:MM:SS
                    filing_date = datetime.fromisoformat(filing_date_str.replace('T', ' ').split(' ')[0]).date()
                else:
                    # Format: DD-MON-YYYY
                    filing_date = datetime.strptime(filing_date_str, '%d-%b-%Y').date()

                # Check if within date range
                if start_date <= filing_date <= end_date:
                    filtered_filings.append(filing)

            except (ValueError, TypeError) as e:
                logger.warning(f"Could not parse date '{filing_date_str}': {e}")
                # Include filing anyway if we can't parse the date
                filtered_filings.append(filing)

    # Limit results
    filtered_filings = filtered_filings[:limit]
    logger.info(f"After date filtering: {len(filtered_filings)} filings")

    # Convert to feed format
    feed_entries = []
    for db_filing in filtered_filings:
        try:
            feed_entry = convert_db_filing_to_feed_format(db_filing)
            feed_entries.append(feed_entry)
        except Exception as e:
            logger.warning(f"Failed to convert filing {db_filing.get('id', 'unknown')}: {e}")

    logger.info(f"Successfully converted {len(feed_entries)} filings to feed format")
    return feed_entries

def analyze_database_filings(
    limit: int = 50,
    days_back: int = 30,
    min_amount: Optional[float] = None,
    max_amount: Optional[float] = None,
    industry: Optional[str] = None,
    issuer_name: Optional[str] = None,
    relevance_threshold: float = 0.7,
    email_threshold: float = 0.8,
    screening_threshold: float = 0.3,
    use_multi_stage: bool = True,
    use_tools: bool = True,
    use_recent_data: bool = False
) -> List[Dict[str, Any]]:
    """
    Analyze filings from database using MCP.
    
    Args:
        limit: Maximum number of filings to analyze
        days_back: Number of days back to search
        min_amount: Minimum offering amount filter
        max_amount: Maximum offering amount filter
        industry: Industry group filter
        issuer_name: Issuer name filter
        relevance_threshold: Threshold for relevance scoring
        email_threshold: Threshold for email alerts
        screening_threshold: Threshold for screening stage
        use_multi_stage: Whether to use multi-stage analysis
        use_tools: Whether to use MCP tools
        
    Returns:
        List of analyzed filings
    """
    # Initialize database connection
    logger.info("Connecting to database...")
    try:
        db_manager = SupabaseDatabaseManager()
        
        # Get database statistics
        stats = db_manager.get_stats()
        logger.info("Database Statistics:")
        for key, value in stats.items():
            logger.info(f"  {key}: {value}")
        
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        return []
    
    # Retrieve filings from database
    try:
        # Adjust date range for historical data if needed
        if not use_recent_data:
            # Use the most recent data available (2023)
            days_back = 365 * 2  # Look back 2 years from latest filing
            logger.info(f"Using historical data mode - looking back {days_back} days from latest filing")

        feed_entries = get_filings_from_database(
            db_manager=db_manager,
            limit=limit,
            days_back=days_back,
            min_amount=min_amount,
            max_amount=max_amount,
            industry=industry,
            issuer_name=issuer_name
        )
        
        if not feed_entries:
            logger.warning("No filings found matching criteria")
            return []
        
    except Exception as e:
        logger.error(f"Failed to retrieve filings from database: {e}")
        return []
    finally:
        db_manager.close()
    
    # Initialize MCP for analysis
    logger.info("Initializing MCP for analysis...")
    try:
        mcp = ModelControlPoint(
            data_dir="data",
            relevance_threshold=relevance_threshold,
            email_threshold=email_threshold,
            screening_threshold=screening_threshold,
            use_multi_stage=use_multi_stage,
            init_tools=use_tools,
            use_prompt_evaluation=False,  # Disable for performance
            use_specialized_analysis=True,
            generation_timeout=45,  # Increased timeout
            max_tokens=512  # Reduced for performance
        )
        
    except Exception as e:
        logger.error(f"Failed to initialize MCP: {e}")
        return []
    
    # Process filings through MCP
    logger.info(f"Processing {len(feed_entries)} filings through MCP...")
    try:
        processed_entries = mcp.process_new_filings(
            feed_entries=feed_entries,
            historical_context=True,  # Use database for historical context
            news_context=use_tools  # Use news tools if enabled
        )
        
        if not processed_entries:
            logger.warning("No entries were processed by MCP")
            return []
        
        logger.info(f"Successfully processed {len(processed_entries)} filings")
        
        # Analyze results
        high_relevance = [e for e in processed_entries if e.get('relevance_score', 0) > relevance_threshold]
        medium_relevance = [e for e in processed_entries if screening_threshold <= e.get('relevance_score', 0) <= relevance_threshold]
        low_relevance = [e for e in processed_entries if e.get('relevance_score', 0) < screening_threshold]
        
        logger.info("Analysis Results:")
        logger.info(f"  High relevance (>{relevance_threshold}): {len(high_relevance)} filings")
        logger.info(f"  Medium relevance ({screening_threshold}-{relevance_threshold}): {len(medium_relevance)} filings")
        logger.info(f"  Low relevance (<{screening_threshold}): {len(low_relevance)} filings")
        
        # Show top results
        if high_relevance:
            logger.info("\nTop High-Relevance Filings:")
            for entry in sorted(high_relevance, key=lambda x: x.get('relevance_score', 0), reverse=True)[:5]:
                score = entry.get('relevance_score', 0)
                issuer = entry.get('issuer_name', 'Unknown')
                amount = entry.get('offering_amount', 0)
                amount_str = f"${amount:,.0f}" if amount else "N/A"
                logger.info(f"  - {issuer} (Score: {score:.2f}, Amount: {amount_str})")
        
        return processed_entries
        
    except Exception as e:
        logger.error(f"Failed to process filings through MCP: {e}")
        return []

def main():
    """Main function with command line argument parsing."""
    parser = argparse.ArgumentParser(description="Analyze Form D filings from database")
    
    # Database query parameters
    parser.add_argument("--limit", type=int, default=50,
                       help="Maximum number of filings to analyze")
    parser.add_argument("--days-back", type=int, default=30,
                       help="Number of days back to search")
    parser.add_argument("--min-amount", type=float,
                       help="Minimum offering amount")
    parser.add_argument("--max-amount", type=float,
                       help="Maximum offering amount")
    parser.add_argument("--industry", type=str,
                       help="Industry group filter")
    parser.add_argument("--issuer-name", type=str,
                       help="Issuer name filter (partial match)")
    
    # Analysis parameters
    parser.add_argument("--relevance-threshold", type=float, default=0.7,
                       help="Threshold for relevance scoring")
    parser.add_argument("--email-threshold", type=float, default=0.8,
                       help="Threshold for email alerts")
    parser.add_argument("--screening-threshold", type=float, default=0.3,
                       help="Threshold for screening stage")
    
    # Feature flags
    parser.add_argument("--disable-multi-stage", action="store_true",
                       help="Disable multi-stage analysis")
    parser.add_argument("--disable-tools", action="store_true",
                       help="Disable MCP tools")
    
    args = parser.parse_args()
    
    # Run analysis
    logger.info("Starting database-first Form D analysis...")
    logger.info("=" * 60)
    
    processed_entries = analyze_database_filings(
        limit=args.limit,
        days_back=args.days_back,
        min_amount=args.min_amount,
        max_amount=args.max_amount,
        industry=args.industry,
        issuer_name=args.issuer_name,
        relevance_threshold=args.relevance_threshold,
        email_threshold=args.email_threshold,
        screening_threshold=args.screening_threshold,
        use_multi_stage=not args.disable_multi_stage,
        use_tools=not args.disable_tools,
        use_recent_data=False  # Use historical data
    )
    
    if processed_entries:
        logger.info("=" * 60)
        logger.info("✅ Database analysis completed successfully!")
        logger.info(f"Processed {len(processed_entries)} filings from database")
        
        # Save results
        output_file = Path("data") / "database_analysis_results.json"
        output_file.parent.mkdir(exist_ok=True)
        
        import json
        with open(output_file, 'w') as f:
            json.dump(processed_entries, f, indent=2, default=str)
        
        logger.info(f"Results saved to: {output_file}")
        
    else:
        logger.error("❌ Database analysis failed or returned no results")
        sys.exit(1)

if __name__ == "__main__":
    main()
