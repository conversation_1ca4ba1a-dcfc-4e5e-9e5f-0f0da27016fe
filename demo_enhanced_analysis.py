#!/usr/bin/env python3
"""
Enhanced Financial Analysis System - Live Demonstration

Demonstrates the enhanced RAG-powered financial analysis capabilities
with real Form D filings from the database.
"""

import sys
import logging
from typing import Dict, List, Any
from datetime import datetime

# Add project root to path
sys.path.append('.')

# Import enhanced components
from mcp.enhanced_prompt_manager import EnhancedPromptManager
from mcp.financial_personas import FinancialPersonas
from mcp.financial_rag_system import FinancialRAGSystem
from db.supabase_manager import SupabaseDatabaseManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedAnalysisDemo:
    """
    Demonstration of enhanced financial analysis capabilities.
    """
    
    def __init__(self):
        """Initialize the demo system."""
        self.db = SupabaseDatabaseManager()
        self.enhanced_prompt_manager = EnhancedPromptManager(enable_rag=True)
        self.personas = FinancialPersonas()
        self.rag_system = FinancialRAGSystem()
        
    def run_demo(self):
        """Run the enhanced analysis demonstration."""
        
        print("🚀 Enhanced Financial Analysis System - Live Demo")
        print("=" * 60)
        
        # Get sample filings from different industries
        sample_filings = self.get_diverse_sample_filings()
        
        if not sample_filings:
            print("❌ No sample filings available for demonstration")
            return
        
        print(f"\n📊 Analyzing {len(sample_filings)} diverse Form D filings...")
        
        for i, filing in enumerate(sample_filings, 1):
            print(f"\n{'='*60}")
            print(f"ANALYSIS {i}: {filing.get('issuer_name', 'Unknown Company')}")
            print(f"{'='*60}")
            
            self.demonstrate_enhanced_analysis(filing)
            
            if i < len(sample_filings):
                input("\nPress Enter to continue to next analysis...")
        
        print(f"\n🎉 Demo completed! Enhanced analysis system successfully demonstrated.")
    
    def get_diverse_sample_filings(self) -> List[Dict[str, Any]]:
        """Get a diverse set of sample filings for demonstration."""
        
        sample_filings = []
        
        # Try to get filings from different industries
        target_industries = [
            "Technology",
            "Biotechnology", 
            "Financial",
            "Real Estate",
            "Energy"
        ]
        
        for industry in target_industries:
            try:
                filings = self.db.search_filings(
                    industry_group=industry,
                    min_amount=1000000,  # At least $1M
                    limit=1
                )
                if filings:
                    sample_filings.append(filings[0])
                    if len(sample_filings) >= 3:  # Limit to 3 for demo
                        break
            except Exception as e:
                logger.warning(f"Could not get {industry} filing: {e}")
        
        # If we don't have enough diverse filings, get any recent ones
        if len(sample_filings) < 3:
            try:
                additional_filings = self.db.search_filings(
                    min_amount=5000000,  # At least $5M
                    limit=3 - len(sample_filings)
                )
                sample_filings.extend(additional_filings)
            except Exception as e:
                logger.warning(f"Could not get additional filings: {e}")
        
        return sample_filings
    
    def demonstrate_enhanced_analysis(self, filing: Dict[str, Any]):
        """Demonstrate enhanced analysis for a single filing."""
        
        # Display basic filing information
        self.display_filing_info(filing)
        
        # Demonstrate persona selection
        self.demonstrate_persona_selection(filing)
        
        # Demonstrate RAG context retrieval
        self.demonstrate_rag_context(filing)
        
        # Demonstrate enhanced prompt generation
        self.demonstrate_enhanced_prompts(filing)
        
        # Show analysis comparison
        self.demonstrate_analysis_comparison(filing)
    
    def display_filing_info(self, filing: Dict[str, Any]):
        """Display basic filing information."""
        
        print("\n📋 FILING INFORMATION")
        print("-" * 30)
        print(f"Company: {filing.get('issuer_name', 'Unknown')}")
        print(f"Industry: {filing.get('industry_group', 'Unknown')}")
        print(f"Offering Amount: ${filing.get('offering_amount', 0):,.0f}")
        print(f"Filing Date: {filing.get('filing_date', 'Unknown')}")
        print(f"Location: {filing.get('issuer_city', 'Unknown')}, {filing.get('issuer_state', 'Unknown')}")
    
    def demonstrate_persona_selection(self, filing: Dict[str, Any]):
        """Demonstrate intelligent persona selection."""
        
        print("\n🎭 ANALYST PERSONA SELECTION")
        print("-" * 30)
        
        # Show persona selection logic
        selected_persona = self.personas.select_best_persona(filing)
        persona_obj = self.personas.get_persona(selected_persona)
        
        print(f"Selected Persona: {persona_obj.name}")
        print(f"Expertise: {', '.join(persona_obj.expertise[:3])}...")
        print(f"Risk Tolerance: {persona_obj.risk_tolerance}")
        print(f"Decision Timeframe: {persona_obj.decision_timeframe}")
        
        # Show why this persona was selected
        industry = filing.get('industry_group', '').lower()
        amount = filing.get('offering_amount', 0)
        
        print(f"\nSelection Rationale:")
        if amount < 5000000:
            print(f"- Early stage company (${amount:,.0f} < $5M) → Venture Capital focus")
        elif amount > *********:
            print(f"- Large offering (${amount:,.0f} > $100M) → Private Equity focus")
        
        if any(term in industry for term in ["biotech", "pharma"]):
            print(f"- Biotech/Pharma industry → Specialized biotech expertise")
        elif any(term in industry for term in ["fintech", "financial"]):
            print(f"- Financial services → Fintech specialization")
    
    def demonstrate_rag_context(self, filing: Dict[str, Any]):
        """Demonstrate RAG context retrieval."""
        
        print("\n🧠 RAG CONTEXT RETRIEVAL")
        print("-" * 30)
        
        try:
            # Get RAG context
            context = self.rag_system.retrieve_relevant_context(filing)
            
            print("Retrieved Context Components:")
            for key, value in context.items():
                if key == "frameworks":
                    print(f"✅ {key}: {list(value.keys())}")
                elif key == "similar_filings":
                    print(f"✅ {key}: {len(value)} comparable filings found")
                elif key == "market_context":
                    print(f"✅ {key}: Market analysis and trends")
                elif key == "semantic_knowledge":
                    print(f"✅ {key}: {len(value)} relevant knowledge items")
            
            # Show sample of similar filings
            if context.get("similar_filings"):
                print(f"\nSample Comparable Filings:")
                for filing_comp in context["similar_filings"][:2]:
                    print(f"- {filing_comp.get('issuer_name', 'Unknown')}: ${filing_comp.get('offering_amount', 0):,.0f}")
            
        except Exception as e:
            print(f"❌ RAG context retrieval error: {e}")
    
    def demonstrate_enhanced_prompts(self, filing: Dict[str, Any]):
        """Demonstrate enhanced prompt generation."""
        
        print("\n📝 ENHANCED PROMPT GENERATION")
        print("-" * 30)
        
        try:
            # Generate different types of prompts
            prompt_types = ["quick_screen", "comprehensive_analysis", "risk_focused"]
            
            for prompt_type in prompt_types:
                prompt = self.enhanced_prompt_manager.create_enhanced_analysis_prompt(
                    filing, 
                    analysis_type=prompt_type
                )
                
                print(f"\n{prompt_type.upper()} Prompt:")
                print(f"- Length: {len(prompt):,} characters")
                print(f"- Includes: Financial frameworks, market context, persona expertise")
                
                # Show a snippet of the prompt
                snippet = prompt[:200] + "..." if len(prompt) > 200 else prompt
                print(f"- Preview: {snippet}")
                
        except Exception as e:
            print(f"❌ Enhanced prompt generation error: {e}")
    
    def demonstrate_analysis_comparison(self, filing: Dict[str, Any]):
        """Demonstrate comparison between base and enhanced analysis."""
        
        print("\n⚖️ ANALYSIS COMPARISON")
        print("-" * 30)
        
        try:
            # Base prompt (simplified)
            base_prompt = f"""
Analyze this Form D filing:
Company: {filing.get('issuer_name', 'Unknown')}
Industry: {filing.get('industry_group', 'Unknown')}
Amount: ${filing.get('offering_amount', 0):,.0f}

Provide a relevance score and brief analysis.
"""
            
            # Enhanced prompt
            enhanced_prompt = self.enhanced_prompt_manager.create_enhanced_analysis_prompt(
                filing,
                analysis_type="quick_screen"
            )
            
            print("COMPARISON METRICS:")
            print(f"Base Prompt Length: {len(base_prompt):,} characters")
            print(f"Enhanced Prompt Length: {len(enhanced_prompt):,} characters")
            print(f"Enhancement Factor: {len(enhanced_prompt) / len(base_prompt):.1f}x more comprehensive")
            
            print(f"\nENHANCED FEATURES:")
            print(f"✅ Industry-specific analysis frameworks")
            print(f"✅ Professional analyst persona and expertise")
            print(f"✅ Comparable transaction context")
            print(f"✅ Market intelligence and trends")
            print(f"✅ Risk assessment frameworks")
            print(f"✅ Valuation methodologies")
            print(f"✅ Structured output templates")
            
        except Exception as e:
            print(f"❌ Analysis comparison error: {e}")
    
    def show_system_capabilities(self):
        """Show overall system capabilities."""
        
        print("\n🎯 ENHANCED SYSTEM CAPABILITIES")
        print("=" * 50)
        
        print("\n📚 KNOWLEDGE BASE:")
        print("- 4 comprehensive valuation frameworks")
        print("- 4 risk assessment methodologies") 
        print("- 3 industry-specific templates")
        print("- Financial metrics and benchmarks")
        print("- Market analysis guides")
        
        print("\n👥 ANALYST PERSONAS:")
        personas = self.personas.get_all_personas()
        for persona_name, persona_obj in personas.items():
            print(f"- {persona_obj.name} ({persona_obj.risk_tolerance} risk)")
        
        print("\n📊 ANALYSIS TEMPLATES:")
        templates = self.enhanced_prompt_manager.analysis_templates.get_all_templates()
        for template_name in templates.keys():
            print(f"- {template_name.replace('_', ' ').title()}")
        
        print("\n🔍 RAG CAPABILITIES:")
        print("- Semantic search of financial knowledge")
        print("- Comparable transaction analysis")
        print("- Market context and trends")
        print("- Industry-specific insights")
        print("- Real-time data integration")

def main():
    """Main demonstration function."""
    
    try:
        # Initialize demo
        demo = EnhancedAnalysisDemo()
        
        # Show system capabilities
        demo.show_system_capabilities()
        
        # Run interactive demo
        print("\n" + "="*60)
        response = input("Would you like to run the live analysis demo? (y/n): ")
        
        if response.lower().startswith('y'):
            demo.run_demo()
        else:
            print("Demo skipped. Enhanced system is ready for use!")
            
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
