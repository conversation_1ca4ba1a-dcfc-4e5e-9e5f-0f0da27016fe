{"timestamp": "2025-06-20T20:37:35.601049", "tests": {"knowledge_base": {"status": "PASS", "tests": {"initialization": {"status": "PASS", "details": "Loaded 4 valuation frameworks"}, "framework_retrieval": {"status": "PASS", "details": "Retrieved frameworks: ['valuation_methods', 'risk_assessment', 'industry_analysis', 'financial_metrics', 'market_analysis']"}, "context_generation": {"status": "PASS", "details": "Generated context length: 2239 characters"}}, "errors": []}, "rag_system": {"status": "PASS", "tests": {"initialization": {"status": "PASS", "details": "RAG system initialized with knowledge base"}, "context_retrieval": {"status": "PASS", "details": "Retrieved context keys: ['frameworks', 'similar_filings', 'market_context', 'semantic_knowledge']"}, "prompt_enhancement": {"status": "PASS", "details": "Enhanced prompt length: 3301 vs base: 28"}}, "errors": []}, "prompt_manager": {"status": "PASS", "tests": {"initialization": {"status": "PASS", "details": "Enhanced prompt manager initialized"}, "persona_selection": {"status": "PASS", "details": "Selected persona: fintech_specialist"}, "prompt_creation": {"status": "PASS", "details": "Created prompt length: 2480 characters"}}, "errors": []}, "personas": {"status": "PASS", "tests": {"personas_loading": {"status": "PASS", "details": "Loaded 10 personas: ['growth_equity', 'venture_capital', 'private_equity', 'value_investor', 'biotech_specialist', 'fintech_specialist', 'real_estate_specialist', 'energy_specialist', 'credit_analyst', 'distressed_specialist']"}, "persona_selection": {"status": "PASS", "details": "Selections: ['Biotechnology: biotech_specialist', 'Financial Technology: fintech_specialist', 'Real Estate: real_estate_specialist', 'Technology: venture_capital']"}, "context_generation": {"status": "PASS", "details": "Context length: 1421 characters"}}, "errors": []}, "templates": {"status": "PASS", "tests": {"templates_loading": {"status": "PASS", "details": "Loaded 10 templates: ['executive_summary', 'investment_memo', 'risk_assessment', 'market_analysis', 'financial_analysis', 'competitive_analysis', 'management_assessment', 'quick_screen', 'sector_deep_dive', 'valuation_analysis']"}, "template_retrieval": {"status": "PASS", "details": "Retrieved executive summary and investment memo templates"}, "template_formatting": {"status": "PASS", "details": "Template formatting successful"}}, "errors": []}, "enhanced_mcp": {"status": "PASS", "tests": {"initialization": {"status": "PASS", "details": "Enhanced MCP class available for initialization"}, "configuration": {"status": "PASS", "details": "Configuration options available: ['enable_financial_rag', 'financial_analysis_mode']"}}, "errors": []}, "end_to_end": {"status": "PASS", "tests": {"prompt_generation": {"status": "PASS", "details": "Generated prompt for SI-0707 Fund I, a series of Double AA Ventures, LP"}, "persona_selection": {"status": "PASS", "details": "Selected persona: venture_capital"}, "sample_data": {"status": "PASS", "details": "Processed filing: SI-0707 Fund I, a series of Double AA Ventures, LP"}}, "errors": []}}, "summary": {"total_tests": 20, "passed": 20, "failed": 0, "skipped": 0, "success_rate": 100.0, "overall_status": "PASS"}}