#!/usr/bin/env python3
"""
Pydantic Models for SEC Form D Analysis API

Defines data models for API requests and responses, ensuring type safety
and automatic validation for the web dashboard interface.
"""

from datetime import datetime
from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field, validator

class Filing(BaseModel):
    """Model for SEC Form D filing data."""
    
    id: str = Field(..., description="Unique filing identifier")
    accession_number: str = Field(..., description="SEC accession number")
    title: str = Field(..., description="Filing title")
    issuer_name: str = Field(..., description="Name of the issuing company")
    filing_date: str = Field(..., description="Date of filing")
    offering_amount: Optional[float] = Field(None, description="Offering amount in USD")
    industry_group: Optional[str] = Field(None, description="Industry classification")
    issuer_city: Optional[str] = Field(None, description="Issuer city")
    issuer_state: Optional[str] = Field(None, description="Issuer state")
    offering_type: Optional[str] = Field(None, description="Type of offering")
    minimum_investment: Optional[float] = Field(None, description="Minimum investment amount")
    total_amount_sold: Optional[float] = Field(None, description="Total amount sold")
    total_remaining: Optional[float] = Field(None, description="Total remaining to be sold")
    
    # Analysis results
    relevance_score: Optional[float] = Field(None, description="AI relevance score (0.0-1.0)")
    summary: Optional[str] = Field(None, description="AI-generated summary")
    email_draft: Optional[str] = Field(None, description="AI-generated email draft")
    analysis_timestamp: Optional[str] = Field(None, description="Timestamp of analysis")
    cached: Optional[bool] = Field(False, description="Whether result was cached")
    
    # Additional context
    news_context: Optional[List[Dict[str, Any]]] = Field(None, description="Related news articles")
    similar_filings: Optional[List[str]] = Field(None, description="IDs of similar filings")
    
    class Config:
        schema_extra = {
            "example": {
                "id": "filing_123",
                "accession_number": "0001234567-23-000001",
                "title": "Form D Notice of Exempt Offering of Securities",
                "issuer_name": "TechCorp Inc.",
                "filing_date": "2024-01-15",
                "offering_amount": 50000000.0,
                "industry_group": "Technology",
                "issuer_city": "San Francisco",
                "issuer_state": "CA",
                "relevance_score": 0.85,
                "summary": "TechCorp Inc. filed for a $50M Series B funding round...",
                "analysis_timestamp": "2024-01-15T10:30:00Z"
            }
        }

class AnalysisResult(BaseModel):
    """Model for analysis results and metrics."""
    
    filing_id: str = Field(..., description="Filing identifier")
    relevance_score: float = Field(..., description="Relevance score (0.0-1.0)")
    summary: str = Field(..., description="Analysis summary")
    email_draft: Optional[str] = Field(None, description="Email draft content")
    analysis_timestamp: str = Field(..., description="Analysis timestamp")
    model_name: Optional[str] = Field(None, description="Model used for analysis")
    processing_time: Optional[float] = Field(None, description="Processing time in seconds")
    cached: bool = Field(False, description="Whether result was cached")
    
    # Multi-stage analysis results
    screening_results: Optional[Dict[str, Any]] = Field(None, description="Screening stage results")
    detailed_results: Optional[Dict[str, Any]] = Field(None, description="Detailed analysis results")
    action_results: Optional[Dict[str, Any]] = Field(None, description="Action generation results")

class SystemStatus(BaseModel):
    """Model for system status and health metrics."""
    
    status: str = Field(..., description="Overall system status")
    timestamp: str = Field(..., description="Status timestamp")
    
    # ATOM feed monitoring
    atom_feed_status: str = Field(..., description="ATOM feed monitoring status")
    last_feed_check: Optional[str] = Field(None, description="Last feed check timestamp")
    feed_entries_processed: int = Field(0, description="Number of feed entries processed")
    
    # Model performance
    model_status: str = Field(..., description="Model status")
    model_load_time: Optional[float] = Field(None, description="Model load time in seconds")
    cache_hit_rate: float = Field(0.0, description="Cache hit rate percentage")
    
    # System resources
    memory_usage_gb: float = Field(0.0, description="Memory usage in GB")
    cpu_usage_percent: float = Field(0.0, description="CPU usage percentage")
    disk_usage_gb: float = Field(0.0, description="Disk usage in GB")
    
    # Processing queue
    queue_size: int = Field(0, description="Number of items in processing queue")
    processing_rate: float = Field(0.0, description="Processing rate (items/minute)")
    
    # Error tracking
    error_count: int = Field(0, description="Number of errors in last hour")
    last_error: Optional[str] = Field(None, description="Last error message")

class Configuration(BaseModel):
    """Model for system configuration settings."""
    
    # Analysis thresholds
    relevance_threshold: float = Field(0.7, description="Relevance threshold for filings")
    email_threshold: float = Field(0.8, description="Threshold for email alerts")
    screening_threshold: float = Field(0.3, description="Screening stage threshold")
    
    # Monitoring settings
    atom_feed_interval: int = Field(15, description="ATOM feed check interval in minutes")
    auto_email_enabled: bool = Field(True, description="Whether to send automatic emails")
    
    # Model settings
    model_temperature: float = Field(0.1, description="Model sampling temperature")
    max_tokens: int = Field(768, description="Maximum tokens to generate")
    use_persistent_model: bool = Field(True, description="Use persistent model manager")
    
    # Cache settings
    cache_ttl_hours: int = Field(24, description="Cache TTL in hours")
    
    # Email settings
    email_recipients: List[str] = Field([], description="Email recipient addresses")
    email_subject_prefix: str = Field("SEC Form D Alert", description="Email subject prefix")
    
    @validator('relevance_threshold', 'email_threshold', 'screening_threshold')
    def validate_thresholds(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError('Threshold must be between 0.0 and 1.0')
        return v
    
    @validator('model_temperature')
    def validate_temperature(cls, v):
        if not 0.0 <= v <= 2.0:
            raise ValueError('Temperature must be between 0.0 and 2.0')
        return v

class FilingResponse(BaseModel):
    """Response model for filing endpoints."""
    
    filings: List[Filing] = Field(..., description="List of filings")
    total_count: int = Field(..., description="Total number of filings")
    timestamp: str = Field(..., description="Response timestamp")

class AnalysisResponse(BaseModel):
    """Response model for analysis endpoints."""
    
    results: List[AnalysisResult] = Field(..., description="Analysis results")
    performance_stats: Dict[str, Any] = Field(..., description="Performance statistics")
    timestamp: str = Field(..., description="Response timestamp")

class StatusResponse(BaseModel):
    """Response model for status endpoints."""
    
    status: str = Field(..., description="Overall system status")
    timestamp: str = Field(..., description="Status timestamp")
    details: SystemStatus = Field(..., description="Detailed status information")

class SearchRequest(BaseModel):
    """Request model for search operations."""
    
    query: str = Field(..., description="Search query")
    limit: int = Field(50, description="Maximum number of results")
    min_relevance: float = Field(0.0, description="Minimum relevance score")
    date_from: Optional[str] = Field(None, description="Start date filter (YYYY-MM-DD)")
    date_to: Optional[str] = Field(None, description="End date filter (YYYY-MM-DD)")
    industry: Optional[str] = Field(None, description="Industry filter")
    
    @validator('limit')
    def validate_limit(cls, v):
        if not 1 <= v <= 1000:
            raise ValueError('Limit must be between 1 and 1000')
        return v

class WebSocketMessage(BaseModel):
    """Model for WebSocket messages."""
    
    type: str = Field(..., description="Message type")
    data: Dict[str, Any] = Field(..., description="Message data")
    timestamp: str = Field(..., description="Message timestamp")
    
    class Config:
        schema_extra = {
            "example": {
                "type": "filing_update",
                "data": {
                    "filing_id": "filing_123",
                    "relevance_score": 0.85,
                    "status": "analyzed"
                },
                "timestamp": "2024-01-15T10:30:00Z"
            }
        }

class PerformanceMetrics(BaseModel):
    """Model for performance metrics."""
    
    total_analyses: int = Field(0, description="Total number of analyses performed")
    cache_hit_rate: float = Field(0.0, description="Cache hit rate percentage")
    average_analysis_time: float = Field(0.0, description="Average analysis time in seconds")
    model_load_time: Optional[float] = Field(None, description="Model load time in seconds")
    peak_memory_usage: float = Field(0.0, description="Peak memory usage in GB")
    error_rate: float = Field(0.0, description="Error rate percentage")
    
class TrendData(BaseModel):
    """Model for analytics trend data."""
    
    date: str = Field(..., description="Date")
    filing_count: int = Field(0, description="Number of filings")
    average_relevance: float = Field(0.0, description="Average relevance score")
    high_relevance_count: int = Field(0, description="Number of high relevance filings")
    industry_distribution: Dict[str, int] = Field({}, description="Industry distribution")
    offering_amount_total: float = Field(0.0, description="Total offering amount")

class EmailPreview(BaseModel):
    """Model for email preview."""
    
    subject: str = Field(..., description="Email subject")
    body_html: str = Field(..., description="Email body HTML")
    recipients: List[str] = Field(..., description="Email recipients")
    filing_count: int = Field(..., description="Number of filings included")
    timestamp: str = Field(..., description="Preview timestamp")
