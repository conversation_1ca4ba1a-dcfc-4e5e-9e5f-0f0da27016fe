#!/usr/bin/env python3
"""
WebSocket Manager for SEC Form D Analysis Dashboard

Handles real-time communication between the backend and frontend dashboard,
providing live updates for filing analysis, system status, and configuration changes.
"""

import json
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional

from fastapi import WebSocket, WebSocketDisconnect

logger = logging.getLogger(__name__)

class WebSocketManager:
    """
    Manages WebSocket connections for real-time dashboard updates.
    """
    
    def __init__(self):
        """Initialize the WebSocket manager."""
        self.active_connections: List[WebSocket] = []
        self.connection_count = 0
        logger.info("WebSocket manager initialized")
    
    async def connect(self, websocket: WebSocket) -> None:
        """
        Accept a new WebSocket connection.
        
        Args:
            websocket: WebSocket connection to accept
        """
        await websocket.accept()
        self.active_connections.append(websocket)
        self.connection_count += 1
        
        logger.info(f"WebSocket connection accepted. Total connections: {len(self.active_connections)}")
        
        # Send welcome message with current status
        await self._send_to_connection(websocket, {
            "type": "connection_established",
            "data": {
                "connection_id": self.connection_count,
                "timestamp": datetime.now().isoformat(),
                "message": "Connected to SEC Form D Analysis Dashboard"
            },
            "timestamp": datetime.now().isoformat()
        })
    
    def disconnect(self, websocket: WebSocket) -> None:
        """
        Remove a WebSocket connection.
        
        Args:
            websocket: WebSocket connection to remove
        """
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
            logger.info(f"WebSocket connection removed. Total connections: {len(self.active_connections)}")
    
    async def broadcast_filing_update(self, filing_data: Dict[str, Any]) -> None:
        """
        Broadcast a filing update to all connected clients.
        
        Args:
            filing_data: Filing data to broadcast
        """
        message = {
            "type": "filing_update",
            "data": {
                "filing_id": filing_data.get("id"),
                "issuer_name": filing_data.get("issuer_name"),
                "relevance_score": filing_data.get("relevance_score"),
                "summary": filing_data.get("summary"),
                "analysis_timestamp": filing_data.get("analysis_timestamp"),
                "cached": filing_data.get("cached", False)
            },
            "timestamp": datetime.now().isoformat()
        }
        
        await self._broadcast(message)
        logger.info(f"Broadcasted filing update for {filing_data.get('issuer_name', 'Unknown')}")
    
    async def broadcast_system_status(self, status: Dict[str, Any]) -> None:
        """
        Broadcast system status update to all connected clients.
        
        Args:
            status: System status data
        """
        message = {
            "type": "system_status",
            "data": status,
            "timestamp": datetime.now().isoformat()
        }
        
        await self._broadcast(message)
        logger.info("Broadcasted system status update")
    
    async def broadcast_analysis_complete(self, analysis_result: Dict[str, Any]) -> None:
        """
        Broadcast analysis completion to all connected clients.
        
        Args:
            analysis_result: Analysis result data
        """
        message = {
            "type": "analysis_complete",
            "data": {
                "filing_id": analysis_result.get("filing_id"),
                "relevance_score": analysis_result.get("relevance_score"),
                "processing_time": analysis_result.get("processing_time"),
                "cached": analysis_result.get("cached", False),
                "timestamp": analysis_result.get("analysis_timestamp")
            },
            "timestamp": datetime.now().isoformat()
        }
        
        await self._broadcast(message)
        logger.info(f"Broadcasted analysis completion for filing {analysis_result.get('filing_id')}")
    
    async def broadcast_config_update(self, config: Dict[str, Any]) -> None:
        """
        Broadcast configuration update to all connected clients.
        
        Args:
            config: Updated configuration data
        """
        message = {
            "type": "config_update",
            "data": config,
            "timestamp": datetime.now().isoformat()
        }
        
        await self._broadcast(message)
        logger.info("Broadcasted configuration update")
    
    async def broadcast_error(self, error_type: str, error_message: str, 
                             context: Optional[Dict[str, Any]] = None) -> None:
        """
        Broadcast error notification to all connected clients.
        
        Args:
            error_type: Type of error
            error_message: Error message
            context: Additional error context
        """
        message = {
            "type": "error",
            "data": {
                "error_type": error_type,
                "message": error_message,
                "context": context or {},
                "severity": "error"
            },
            "timestamp": datetime.now().isoformat()
        }
        
        await self._broadcast(message)
        logger.warning(f"Broadcasted error: {error_type} - {error_message}")
    
    async def broadcast_performance_update(self, metrics: Dict[str, Any]) -> None:
        """
        Broadcast performance metrics update to all connected clients.
        
        Args:
            metrics: Performance metrics data
        """
        message = {
            "type": "performance_update",
            "data": metrics,
            "timestamp": datetime.now().isoformat()
        }
        
        await self._broadcast(message)
        logger.debug("Broadcasted performance metrics update")
    
    async def broadcast_queue_status(self, queue_info: Dict[str, Any]) -> None:
        """
        Broadcast processing queue status to all connected clients.
        
        Args:
            queue_info: Queue status information
        """
        message = {
            "type": "queue_status",
            "data": {
                "queue_size": queue_info.get("size", 0),
                "processing_rate": queue_info.get("rate", 0.0),
                "estimated_completion": queue_info.get("estimated_completion"),
                "current_item": queue_info.get("current_item")
            },
            "timestamp": datetime.now().isoformat()
        }
        
        await self._broadcast(message)
        logger.debug(f"Broadcasted queue status: {queue_info.get('size', 0)} items")
    
    async def send_notification(self, notification_type: str, title: str, 
                               message: str, severity: str = "info") -> None:
        """
        Send a notification to all connected clients.
        
        Args:
            notification_type: Type of notification
            title: Notification title
            message: Notification message
            severity: Severity level (info, warning, error, success)
        """
        notification = {
            "type": "notification",
            "data": {
                "notification_type": notification_type,
                "title": title,
                "message": message,
                "severity": severity
            },
            "timestamp": datetime.now().isoformat()
        }
        
        await self._broadcast(notification)
        logger.info(f"Sent notification: {title}")
    
    async def _broadcast(self, message: Dict[str, Any]) -> None:
        """
        Broadcast a message to all connected clients.
        
        Args:
            message: Message to broadcast
        """
        if not self.active_connections:
            logger.debug("No active connections to broadcast to")
            return
        
        # Convert message to JSON
        message_json = json.dumps(message)
        
        # Send to all connections, removing any that fail
        disconnected_connections = []
        
        for connection in self.active_connections:
            try:
                await connection.send_text(message_json)
            except Exception as e:
                logger.warning(f"Failed to send message to connection: {e}")
                disconnected_connections.append(connection)
        
        # Remove failed connections
        for connection in disconnected_connections:
            self.disconnect(connection)
        
        if disconnected_connections:
            logger.info(f"Removed {len(disconnected_connections)} failed connections")
    
    async def _send_to_connection(self, websocket: WebSocket, message: Dict[str, Any]) -> None:
        """
        Send a message to a specific connection.
        
        Args:
            websocket: WebSocket connection
            message: Message to send
        """
        try:
            message_json = json.dumps(message)
            await websocket.send_text(message_json)
        except Exception as e:
            logger.warning(f"Failed to send message to specific connection: {e}")
            self.disconnect(websocket)
    
    def get_connection_count(self) -> int:
        """
        Get the number of active connections.
        
        Returns:
            Number of active WebSocket connections
        """
        return len(self.active_connections)
    
    async def ping_all_connections(self) -> None:
        """
        Send a ping to all connections to check if they're still alive.
        """
        ping_message = {
            "type": "ping",
            "data": {"timestamp": datetime.now().isoformat()},
            "timestamp": datetime.now().isoformat()
        }
        
        await self._broadcast(ping_message)
        logger.debug(f"Sent ping to {len(self.active_connections)} connections")
    
    async def broadcast_feed_status(self, feed_status: Dict[str, Any]) -> None:
        """
        Broadcast ATOM feed status update.
        
        Args:
            feed_status: Feed status information
        """
        message = {
            "type": "feed_status",
            "data": {
                "status": feed_status.get("status"),
                "last_check": feed_status.get("last_check"),
                "entries_processed": feed_status.get("entries_processed", 0),
                "next_check": feed_status.get("next_check"),
                "error_message": feed_status.get("error_message")
            },
            "timestamp": datetime.now().isoformat()
        }
        
        await self._broadcast(message)
        logger.info(f"Broadcasted feed status: {feed_status.get('status', 'unknown')}")
    
    async def broadcast_model_status(self, model_status: Dict[str, Any]) -> None:
        """
        Broadcast model status update.
        
        Args:
            model_status: Model status information
        """
        message = {
            "type": "model_status",
            "data": {
                "status": model_status.get("status"),
                "model_name": model_status.get("model_name"),
                "load_time": model_status.get("load_time"),
                "cache_hit_rate": model_status.get("cache_hit_rate"),
                "total_analyses": model_status.get("total_analyses", 0),
                "memory_usage": model_status.get("memory_usage")
            },
            "timestamp": datetime.now().isoformat()
        }
        
        await self._broadcast(message)
        logger.info("Broadcasted model status update")
