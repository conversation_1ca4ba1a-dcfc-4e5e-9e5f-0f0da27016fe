#!/usr/bin/env python3
"""
Background Monitor for SEC Form D Analysis Dashboard

Integrates with the existing MCP system to provide real-time monitoring
and analysis updates for the web dashboard.
"""

import asyncio
import logging
import time
import psutil
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

from mcp.core import ModelControlPoint
from ingest.form_d_feed import fetch_feed
from api.websocket import WebSocketManager
from api.database import DatabaseService

logger = logging.getLogger(__name__)

class BackgroundMonitor:
    """
    Background monitoring service that integrates with existing MCP system.
    """
    
    def __init__(self, websocket_manager: WebSocketManager, db_service: DatabaseService):
        """
        Initialize the background monitor.
        
        Args:
            websocket_manager: WebSocket manager for real-time updates
            db_service: Database service for data operations
        """
        self.websocket_manager = websocket_manager
        self.db_service = db_service
        self.mcp = None
        self.monitoring_active = False
        self.last_feed_check = None
        self.feed_check_interval = 900  # 15 minutes
        self.status_update_interval = 60  # 1 minute
        
        # Performance tracking
        self.performance_stats = {
            'total_analyses': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'average_analysis_time': 0.0,
            'error_count': 0
        }
        
        logger.info("Background monitor initialized")
    
    async def start_monitoring(self) -> None:
        """Start background monitoring tasks."""
        if self.monitoring_active:
            logger.warning("Monitoring already active")
            return
        
        self.monitoring_active = True
        logger.info("Starting background monitoring")
        
        # Initialize MCP
        try:
            self.mcp = ModelControlPoint(
                data_dir="data",
                use_persistent_model=True,
                relevance_threshold=0.7,
                email_threshold=0.8,
                screening_threshold=0.3
            )
            logger.info("MCP initialized successfully")
            
            # Broadcast initial model status
            await self._broadcast_model_status()
            
        except Exception as e:
            logger.error(f"Failed to initialize MCP: {e}")
            await self.websocket_manager.broadcast_error(
                "mcp_initialization",
                f"Failed to initialize MCP: {str(e)}"
            )
        
        # Start monitoring tasks
        asyncio.create_task(self._monitor_atom_feed())
        asyncio.create_task(self._monitor_system_status())
        asyncio.create_task(self._monitor_performance())
        
        logger.info("Background monitoring started")
    
    async def stop_monitoring(self) -> None:
        """Stop background monitoring."""
        self.monitoring_active = False
        logger.info("Background monitoring stopped")
    
    async def _monitor_atom_feed(self) -> None:
        """Monitor the SEC ATOM feed for new filings."""
        while self.monitoring_active:
            try:
                logger.info("Checking ATOM feed for new filings")
                
                # Update feed status
                await self.websocket_manager.broadcast_feed_status({
                    "status": "checking",
                    "last_check": datetime.now().isoformat(),
                    "entries_processed": 0
                })
                
                # Fetch new entries
                entries = await asyncio.to_thread(fetch_feed)
                
                if entries:
                    logger.info(f"Found {len(entries)} new entries in ATOM feed")
                    
                    # Process entries with MCP
                    if self.mcp:
                        processed_entries = await self._process_entries_with_mcp(entries)
                        
                        # Broadcast updates for each processed entry
                        for entry in processed_entries:
                            await self.websocket_manager.broadcast_filing_update(entry)
                            
                            # Update performance stats
                            self.performance_stats['total_analyses'] += 1
                            if entry.get('cached'):
                                self.performance_stats['cache_hits'] += 1
                            else:
                                self.performance_stats['cache_misses'] += 1
                    
                    # Update feed status
                    await self.websocket_manager.broadcast_feed_status({
                        "status": "completed",
                        "last_check": datetime.now().isoformat(),
                        "entries_processed": len(entries),
                        "next_check": (datetime.now() + timedelta(seconds=self.feed_check_interval)).isoformat()
                    })
                else:
                    logger.info("No new entries found in ATOM feed")
                    
                    # Update feed status
                    await self.websocket_manager.broadcast_feed_status({
                        "status": "no_new_entries",
                        "last_check": datetime.now().isoformat(),
                        "entries_processed": 0,
                        "next_check": (datetime.now() + timedelta(seconds=self.feed_check_interval)).isoformat()
                    })
                
                self.last_feed_check = datetime.now()
                
            except Exception as e:
                logger.error(f"Error monitoring ATOM feed: {e}")
                self.performance_stats['error_count'] += 1
                
                await self.websocket_manager.broadcast_error(
                    "atom_feed_error",
                    f"Error monitoring ATOM feed: {str(e)}"
                )
                
                await self.websocket_manager.broadcast_feed_status({
                    "status": "error",
                    "last_check": datetime.now().isoformat(),
                    "error_message": str(e)
                })
            
            # Wait for next check
            await asyncio.sleep(self.feed_check_interval)
    
    async def _process_entries_with_mcp(self, entries: list) -> list:
        """
        Process entries using the MCP system.
        
        Args:
            entries: List of feed entries to process
            
        Returns:
            List of processed entries with analysis results
        """
        try:
            # Process entries through MCP
            processed_entries = await asyncio.to_thread(
                self.mcp.process_new_filings,
                entries,
                historical_context=True,
                news_context=True
            )
            
            logger.info(f"Processed {len(processed_entries)} entries through MCP")
            return processed_entries
            
        except Exception as e:
            logger.error(f"Error processing entries with MCP: {e}")
            self.performance_stats['error_count'] += 1
            return []
    
    async def _monitor_system_status(self) -> None:
        """Monitor overall system status and health."""
        while self.monitoring_active:
            try:
                # Get system metrics
                memory_usage = psutil.virtual_memory().used / (1024**3)  # GB
                cpu_usage = psutil.cpu_percent(interval=1)
                disk_usage = psutil.disk_usage('/').used / (1024**3)  # GB
                
                # Calculate cache hit rate
                total_requests = self.performance_stats['cache_hits'] + self.performance_stats['cache_misses']
                cache_hit_rate = (self.performance_stats['cache_hits'] / total_requests * 100) if total_requests > 0 else 0
                
                # Create status update
                status = {
                    "status": "healthy" if self.performance_stats['error_count'] < 5 else "degraded",
                    "timestamp": datetime.now().isoformat(),
                    "atom_feed_status": "active" if self.last_feed_check else "inactive",
                    "last_feed_check": self.last_feed_check.isoformat() if self.last_feed_check else None,
                    "feed_entries_processed": self.performance_stats['total_analyses'],
                    "model_status": "loaded" if self.mcp and self.mcp.model_available else "not_loaded",
                    "cache_hit_rate": cache_hit_rate,
                    "memory_usage_gb": memory_usage,
                    "cpu_usage_percent": cpu_usage,
                    "disk_usage_gb": disk_usage,
                    "queue_size": 0,  # Placeholder
                    "processing_rate": 0.0,  # Placeholder
                    "error_count": self.performance_stats['error_count']
                }
                
                # Broadcast status update
                await self.websocket_manager.broadcast_system_status(status)
                
            except Exception as e:
                logger.error(f"Error monitoring system status: {e}")
            
            # Wait for next status update
            await asyncio.sleep(self.status_update_interval)
    
    async def _monitor_performance(self) -> None:
        """Monitor performance metrics and broadcast updates."""
        while self.monitoring_active:
            try:
                # Get performance stats from persistent model manager
                if self.mcp and self.mcp.persistent_model:
                    model_stats = self.mcp.persistent_model.get_performance_stats()
                    
                    # Merge with our stats
                    combined_stats = {
                        **self.performance_stats,
                        **model_stats
                    }
                    
                    # Broadcast performance update
                    await self.websocket_manager.broadcast_performance_update(combined_stats)
                
            except Exception as e:
                logger.error(f"Error monitoring performance: {e}")
            
            # Wait for next performance update (every 5 minutes)
            await asyncio.sleep(300)
    
    async def _broadcast_model_status(self) -> None:
        """Broadcast current model status."""
        try:
            if self.mcp and self.mcp.persistent_model:
                stats = self.mcp.persistent_model.get_performance_stats()
                
                model_status = {
                    "status": "loaded" if stats.get('model_loaded') else "not_loaded",
                    "model_name": stats.get('model_path', 'Unknown'),
                    "load_time": stats.get('model_load_time_seconds'),
                    "cache_hit_rate": stats.get('cache_hit_rate_percent', 0),
                    "total_analyses": stats.get('total_analyses', 0),
                    "memory_usage": psutil.virtual_memory().used / (1024**3)
                }
                
                await self.websocket_manager.broadcast_model_status(model_status)
            
        except Exception as e:
            logger.error(f"Error broadcasting model status: {e}")
    
    async def reanalyze_filing(self, filing_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Trigger re-analysis of a specific filing.
        
        Args:
            filing_data: Filing data to re-analyze
            
        Returns:
            Analysis result
        """
        try:
            if not self.mcp:
                raise ValueError("MCP not initialized")
            
            logger.info(f"Re-analyzing filing: {filing_data.get('id')}")
            
            # Process single filing through MCP
            processed_entries = await asyncio.to_thread(
                self.mcp.process_new_filings,
                [filing_data],
                historical_context=True,
                news_context=True
            )
            
            if processed_entries:
                result = processed_entries[0]
                logger.info(f"Re-analysis completed for filing: {result.get('id')}")
                return result
            else:
                raise ValueError("No result from re-analysis")
                
        except Exception as e:
            logger.error(f"Error re-analyzing filing: {e}")
            raise
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get current system status."""
        try:
            # Get system metrics
            memory_usage = psutil.virtual_memory().used / (1024**3)
            cpu_usage = psutil.cpu_percent()
            disk_usage = psutil.disk_usage('/').used / (1024**3)
            
            # Calculate cache hit rate
            total_requests = self.performance_stats['cache_hits'] + self.performance_stats['cache_misses']
            cache_hit_rate = (self.performance_stats['cache_hits'] / total_requests * 100) if total_requests > 0 else 0
            
            return {
                "status": "healthy" if self.performance_stats['error_count'] < 5 else "degraded",
                "timestamp": datetime.now().isoformat(),
                "details": {
                    "status": "healthy" if self.performance_stats['error_count'] < 5 else "degraded",
                    "timestamp": datetime.now().isoformat(),
                    "atom_feed_status": "active" if self.last_feed_check else "inactive",
                    "last_feed_check": self.last_feed_check.isoformat() if self.last_feed_check else None,
                    "feed_entries_processed": self.performance_stats['total_analyses'],
                    "model_status": "loaded" if self.mcp and self.mcp.model_available else "not_loaded",
                    "cache_hit_rate": cache_hit_rate,
                    "memory_usage_gb": memory_usage,
                    "cpu_usage_percent": cpu_usage,
                    "disk_usage_gb": disk_usage,
                    "queue_size": 0,
                    "processing_rate": 0.0,
                    "error_count": self.performance_stats['error_count']
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {
                "status": "error",
                "timestamp": datetime.now().isoformat(),
                "details": {"error": str(e)}
            }
    
    async def get_performance_stats(self) -> Dict[str, Any]:
        """Get current performance statistics."""
        try:
            # Get stats from persistent model manager if available
            if self.mcp and self.mcp.persistent_model:
                model_stats = self.mcp.persistent_model.get_performance_stats()
                return {**self.performance_stats, **model_stats}
            else:
                return self.performance_stats
                
        except Exception as e:
            logger.error(f"Error getting performance stats: {e}")
            return self.performance_stats
