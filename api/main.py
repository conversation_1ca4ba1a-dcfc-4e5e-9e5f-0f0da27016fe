#!/usr/bin/env python3
"""
FastAPI Backend for SEC Form D Analysis Dashboard

Provides REST API endpoints and WebSocket connections for the web-based
dashboard interface to the SEC Form D analysis system.
"""

import logging
import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional

from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from pydantic import BaseModel

# Local imports
from api.database import DatabaseService
from api.websocket import WebSocketManager
from api.background import BackgroundMonitor
from api.models import (
    Filing, AnalysisResult, SystemStatus, Configuration,
    FilingResponse, AnalysisResponse, StatusResponse
)
from chatbot.engine import DataLibrarianChatbot
from chatbot.models import ChatMessage, ChatResponse

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="SEC Form D Analysis API",
    description="REST API for SEC Form D filing analysis and monitoring",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize services
db_service = DatabaseService()
websocket_manager = WebSocketManager()
background_monitor = BackgroundMonitor(websocket_manager, db_service)
chatbot = DataLibrarianChatbot()

# Mount static files for frontend
app.mount("/static", StaticFiles(directory="api/static"), name="static")

@app.on_event("startup")
async def startup_event():
    """Initialize background tasks on startup."""
    logger.info("Starting SEC Form D Analysis API")
    
    # Start background monitoring
    asyncio.create_task(background_monitor.start_monitoring())
    
    logger.info("API startup complete")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown."""
    logger.info("Shutting down SEC Form D Analysis API")
    await background_monitor.stop_monitoring()

# Root endpoint
@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve the main dashboard page."""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>SEC Form D Analysis Dashboard</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    </head>
    <body class="bg-gray-100">
        <div id="root">
            <div class="min-h-screen flex items-center justify-center">
                <div class="text-center">
                    <h1 class="text-4xl font-bold text-gray-900 mb-4">SEC Form D Analysis Dashboard</h1>
                    <p class="text-xl text-gray-600 mb-8">AI-Powered Private Market Intelligence</p>
                    <div class="space-y-4">
                        <a href="/api/docs" class="inline-block bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            API Documentation
                        </a>
                        <div class="text-sm text-gray-500">
                            Frontend dashboard coming soon...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    """

# WebSocket endpoint for real-time updates
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time dashboard updates."""
    await websocket_manager.connect(websocket)
    try:
        while True:
            # Keep connection alive and handle incoming messages
            data = await websocket.receive_text()
            logger.info(f"Received WebSocket message: {data}")
            
            # Echo back for now (can be extended for client commands)
            await websocket.send_text(f"Echo: {data}")
            
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket)

# API Routes

@app.get("/api/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.get("/api/filings/recent", response_model=FilingResponse)
async def get_recent_filings(
    limit: int = 50,
    min_relevance: float = 0.0,
    industry: Optional[str] = None
):
    """Get recent filings with analysis results."""
    try:
        filings = await db_service.get_recent_filings(
            limit=limit,
            min_relevance=min_relevance,
            industry=industry
        )
        
        return FilingResponse(
            filings=filings,
            total_count=len(filings),
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"Error fetching recent filings: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/filings/{filing_id}", response_model=Filing)
async def get_filing_details(filing_id: str):
    """Get detailed information for a specific filing."""
    try:
        filing = await db_service.get_filing_by_id(filing_id)
        if not filing:
            raise HTTPException(status_code=404, detail="Filing not found")
        
        return filing
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching filing {filing_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/analysis/results", response_model=AnalysisResponse)
async def get_analysis_results(
    limit: int = 100,
    min_score: float = 0.0,
    hours_back: int = 24
):
    """Get recent analysis results and performance metrics."""
    try:
        results = await db_service.get_analysis_results(
            limit=limit,
            min_score=min_score,
            hours_back=hours_back
        )
        
        # Get performance statistics
        performance_stats = await background_monitor.get_performance_stats()
        
        return AnalysisResponse(
            results=results,
            performance_stats=performance_stats,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"Error fetching analysis results: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/analysis/reanalyze/{filing_id}")
async def reanalyze_filing(filing_id: str):
    """Trigger re-analysis of a specific filing."""
    try:
        # Get filing data
        filing = await db_service.get_filing_by_id(filing_id)
        if not filing:
            raise HTTPException(status_code=404, detail="Filing not found")
        
        # Trigger re-analysis
        result = await background_monitor.reanalyze_filing(filing)
        
        # Broadcast update to connected clients
        await websocket_manager.broadcast_filing_update(result)
        
        return {"status": "success", "filing_id": filing_id, "result": result}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error re-analyzing filing {filing_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/monitoring/status", response_model=StatusResponse)
async def get_system_status():
    """Get current system status and health metrics."""
    try:
        status = await background_monitor.get_system_status()
        return StatusResponse(**status)
    except Exception as e:
        logger.error(f"Error fetching system status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/config/settings", response_model=Configuration)
async def get_configuration():
    """Get current system configuration."""
    try:
        config = await db_service.get_configuration()
        return Configuration(**config)
    except Exception as e:
        logger.error(f"Error fetching configuration: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/api/config/settings")
async def update_configuration(config: Configuration):
    """Update system configuration."""
    try:
        await db_service.update_configuration(config.dict())
        
        # Broadcast configuration update to connected clients
        await websocket_manager.broadcast_config_update(config.dict())
        
        return {"status": "success", "message": "Configuration updated"}
    except Exception as e:
        logger.error(f"Error updating configuration: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/search/filings")
async def search_filings(
    query: str,
    limit: int = 50,
    min_relevance: float = 0.0,
    date_from: Optional[str] = None,
    date_to: Optional[str] = None,
    industry: Optional[str] = None
):
    """Search filings with advanced filters."""
    try:
        results = await db_service.search_filings(
            query=query,
            limit=limit,
            min_relevance=min_relevance,
            date_from=date_from,
            date_to=date_to,
            industry=industry
        )
        
        return {
            "results": results,
            "total_count": len(results),
            "query": query,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error searching filings: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/analytics/trends")
async def get_analytics_trends(days_back: int = 30):
    """Get analytics and trend data for the dashboard."""
    try:
        trends = await db_service.get_analytics_trends(days_back)
        return trends
    except Exception as e:
        logger.error(f"Error fetching analytics trends: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Chatbot API Endpoints

@app.post("/api/chatbot/message", response_model=ChatResponse)
async def send_chatbot_message(
    message: str,
    session_id: Optional[str] = None,
    user_id: Optional[str] = None
):
    """Send a message to the data librarian chatbot."""
    try:
        response = await chatbot.process_message(
            message=message,
            session_id=session_id,
            user_id=user_id
        )

        # Broadcast chatbot interaction to connected clients
        await websocket_manager.broadcast_filing_update({
            "type": "chatbot_interaction",
            "session_id": response.session_id,
            "message": message,
            "response_preview": response.text[:100] + "..." if len(response.text) > 100 else response.text
        })

        return response
    except Exception as e:
        logger.error(f"Error processing chatbot message: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/chatbot/history/{session_id}")
async def get_chatbot_history(session_id: str):
    """Get conversation history for a chatbot session."""
    try:
        history = await chatbot.get_session_history(session_id)
        return {"session_id": session_id, "messages": history}
    except Exception as e:
        logger.error(f"Error fetching chatbot history: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/chatbot/session/{session_id}")
async def clear_chatbot_session(session_id: str):
    """Clear a chatbot session."""
    try:
        await chatbot.clear_session(session_id)
        return {"status": "success", "message": f"Session {session_id} cleared"}
    except Exception as e:
        logger.error(f"Error clearing chatbot session: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/chatbot/stats")
async def get_chatbot_stats():
    """Get chatbot performance statistics."""
    try:
        stats = chatbot.get_stats()
        return stats
    except Exception as e:
        logger.error(f"Error fetching chatbot stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
