#!/usr/bin/env python3
"""
Database Service for SEC Form D Analysis API

Provides database operations for the web dashboard, integrating with
the existing Supabase database and local SQLite cache.
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

from db.supabase_manager import SupabaseDatabaseManager
from db.database import DatabaseManager
from models.persistent_model_manager import PersistentModelManager
from api.models import Filing, AnalysisResult, SystemStatus, Configuration

logger = logging.getLogger(__name__)

class DatabaseService:
    """
    Database service for the API, integrating with existing database systems.
    """
    
    def __init__(self):
        """Initialize database connections."""
        try:
            self.supabase_manager = SupabaseDatabaseManager()
            self.local_db = DatabaseManager()
            self.persistent_model = PersistentModelManager()
            logger.info("Database service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize database service: {e}")
            raise
    
    async def get_recent_filings(self, 
                                limit: int = 50,
                                min_relevance: float = 0.0,
                                industry: Optional[str] = None) -> List[Filing]:
        """
        Get recent filings with analysis results.
        
        Args:
            limit: Maximum number of filings to return
            min_relevance: Minimum relevance score filter
            industry: Industry filter
            
        Returns:
            List of Filing objects
        """
        try:
            # Get recent filings from Supabase
            filings_data = await asyncio.to_thread(
                self.supabase_manager.get_recent_filings,
                limit=limit * 2  # Get more to account for filtering
            )
            
            filings = []
            for filing_data in filings_data:
                # Convert to Filing model
                filing = Filing(
                    id=filing_data.get('id', ''),
                    accession_number=filing_data.get('accession_number', ''),
                    title=filing_data.get('title', ''),
                    issuer_name=filing_data.get('issuer_name', ''),
                    filing_date=filing_data.get('filing_date', ''),
                    offering_amount=filing_data.get('offering_amount'),
                    industry_group=filing_data.get('industry_group'),
                    issuer_city=filing_data.get('issuer_city'),
                    issuer_state=filing_data.get('issuer_state'),
                    offering_type=filing_data.get('offering_type'),
                    minimum_investment=filing_data.get('minimum_investment'),
                    total_amount_sold=filing_data.get('total_amount_sold'),
                    total_remaining=filing_data.get('total_remaining')
                )
                
                # Get analysis results if available
                analysis = await self._get_filing_analysis(filing.id)
                if analysis:
                    filing.relevance_score = analysis.get('relevance_score')
                    filing.summary = analysis.get('summary')
                    filing.email_draft = analysis.get('email_draft')
                    filing.analysis_timestamp = analysis.get('analysis_timestamp')
                    filing.cached = analysis.get('cached', False)
                
                # Apply filters
                if min_relevance > 0 and (filing.relevance_score or 0) < min_relevance:
                    continue
                
                if industry and filing.industry_group != industry:
                    continue
                
                filings.append(filing)
                
                # Stop when we have enough results
                if len(filings) >= limit:
                    break
            
            return filings
            
        except Exception as e:
            logger.error(f"Error fetching recent filings: {e}")
            return []
    
    async def get_filing_by_id(self, filing_id: str) -> Optional[Filing]:
        """
        Get detailed information for a specific filing.
        
        Args:
            filing_id: Filing identifier
            
        Returns:
            Filing object or None if not found
        """
        try:
            # Get filing from Supabase
            filing_data = await asyncio.to_thread(
                self.supabase_manager.get_filing_by_id,
                filing_id
            )
            
            if not filing_data:
                return None
            
            # Convert to Filing model
            filing = Filing(
                id=filing_data.get('id', ''),
                accession_number=filing_data.get('accession_number', ''),
                title=filing_data.get('title', ''),
                issuer_name=filing_data.get('issuer_name', ''),
                filing_date=filing_data.get('filing_date', ''),
                offering_amount=filing_data.get('offering_amount'),
                industry_group=filing_data.get('industry_group'),
                issuer_city=filing_data.get('issuer_city'),
                issuer_state=filing_data.get('issuer_state'),
                offering_type=filing_data.get('offering_type'),
                minimum_investment=filing_data.get('minimum_investment'),
                total_amount_sold=filing_data.get('total_amount_sold'),
                total_remaining=filing_data.get('total_remaining')
            )
            
            # Get analysis results
            analysis = await self._get_filing_analysis(filing.id)
            if analysis:
                filing.relevance_score = analysis.get('relevance_score')
                filing.summary = analysis.get('summary')
                filing.email_draft = analysis.get('email_draft')
                filing.analysis_timestamp = analysis.get('analysis_timestamp')
                filing.cached = analysis.get('cached', False)
            
            # Get additional context (news, similar filings)
            filing.news_context = await self._get_news_context(filing.issuer_name)
            filing.similar_filings = await self._get_similar_filings(filing.id)
            
            return filing
            
        except Exception as e:
            logger.error(f"Error fetching filing {filing_id}: {e}")
            return None
    
    async def get_analysis_results(self,
                                  limit: int = 100,
                                  min_score: float = 0.0,
                                  hours_back: int = 24) -> List[AnalysisResult]:
        """
        Get recent analysis results.
        
        Args:
            limit: Maximum number of results
            min_score: Minimum relevance score
            hours_back: Hours to look back
            
        Returns:
            List of AnalysisResult objects
        """
        try:
            # Calculate cutoff time
            cutoff_time = datetime.now() - timedelta(hours=hours_back)
            
            # Get analysis results from local database
            results_data = await asyncio.to_thread(
                self.local_db.get_recent_analysis_results,
                limit=limit,
                min_score=min_score,
                since=cutoff_time.isoformat()
            )
            
            results = []
            for result_data in results_data:
                result = AnalysisResult(
                    filing_id=result_data.get('filing_id', ''),
                    relevance_score=result_data.get('relevance_score', 0.0),
                    summary=result_data.get('summary', ''),
                    email_draft=result_data.get('email_draft', ''),
                    analysis_timestamp=result_data.get('analysis_timestamp', ''),
                    model_name=result_data.get('model_name'),
                    cached=True  # From database, so it's cached
                )
                results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"Error fetching analysis results: {e}")
            return []
    
    async def search_filings(self,
                            query: str,
                            limit: int = 50,
                            min_relevance: float = 0.0,
                            date_from: Optional[str] = None,
                            date_to: Optional[str] = None,
                            industry: Optional[str] = None) -> List[Filing]:
        """
        Search filings with advanced filters.
        
        Args:
            query: Search query
            limit: Maximum number of results
            min_relevance: Minimum relevance score
            date_from: Start date filter
            date_to: End date filter
            industry: Industry filter
            
        Returns:
            List of Filing objects
        """
        try:
            # Use Supabase search functionality
            search_results = await asyncio.to_thread(
                self.supabase_manager.search_filings,
                query=query,
                limit=limit,
                date_from=date_from,
                date_to=date_to,
                industry=industry
            )
            
            filings = []
            for filing_data in search_results:
                filing = Filing(**filing_data)
                
                # Get analysis results
                analysis = await self._get_filing_analysis(filing.id)
                if analysis:
                    filing.relevance_score = analysis.get('relevance_score')
                    filing.summary = analysis.get('summary')
                    filing.email_draft = analysis.get('email_draft')
                    filing.analysis_timestamp = analysis.get('analysis_timestamp')
                
                # Apply relevance filter
                if min_relevance > 0 and (filing.relevance_score or 0) < min_relevance:
                    continue
                
                filings.append(filing)
            
            return filings
            
        except Exception as e:
            logger.error(f"Error searching filings: {e}")
            return []
    
    async def get_configuration(self) -> Dict[str, Any]:
        """Get current system configuration."""
        # Return default configuration for now
        return {
            "relevance_threshold": 0.7,
            "email_threshold": 0.8,
            "screening_threshold": 0.3,
            "atom_feed_interval": 15,
            "auto_email_enabled": True,
            "model_temperature": 0.1,
            "max_tokens": 768,
            "use_persistent_model": True,
            "cache_ttl_hours": 24,
            "email_recipients": [],
            "email_subject_prefix": "SEC Form D Alert"
        }
    
    async def update_configuration(self, config: Dict[str, Any]) -> None:
        """Update system configuration."""
        # Placeholder - implement configuration persistence
        logger.info(f"Configuration update requested: {config}")
    
    async def get_analytics_trends(self, days_back: int = 30) -> Dict[str, Any]:
        """Get analytics and trend data."""
        try:
            # Get trend data from Supabase
            trends = await asyncio.to_thread(
                self.supabase_manager.get_filing_trends,
                days_back=days_back
            )
            
            return trends
            
        except Exception as e:
            logger.error(f"Error fetching analytics trends: {e}")
            return {}
    
    async def _get_filing_analysis(self, filing_id: str) -> Optional[Dict[str, Any]]:
        """Get analysis results for a filing."""
        try:
            # Check local database first
            analysis = await asyncio.to_thread(
                self.local_db.get_analysis_result_by_filing_id,
                filing_id
            )
            
            if analysis:
                analysis['cached'] = True
                return analysis
            
            return None
            
        except Exception as e:
            logger.error(f"Error fetching analysis for filing {filing_id}: {e}")
            return None
    
    async def _get_news_context(self, company_name: str) -> List[Dict[str, Any]]:
        """Get news context for a company."""
        # Placeholder - integrate with news scraper tool
        return []
    
    async def _get_similar_filings(self, filing_id: str) -> List[str]:
        """Get similar filings for a filing."""
        # Placeholder - implement similarity search
        return []

    async def get_recent_analysis_results(self, limit: int = 100,
                                        min_score: float = 0.0,
                                        since: str = None) -> List[Dict[str, Any]]:
        """Get recent analysis results from local database."""
        try:
            # This would integrate with the local database
            # For now, return empty list as placeholder
            return []
        except Exception as e:
            logger.error(f"Error fetching recent analysis results: {e}")
            return []

    async def get_analysis_result_by_filing_id(self, filing_id: str) -> Optional[Dict[str, Any]]:
        """Get analysis result for a specific filing."""
        try:
            # This would query the local analysis results
            # For now, return None as placeholder
            return None
        except Exception as e:
            logger.error(f"Error fetching analysis for filing {filing_id}: {e}")
            return None

    async def get_company_filings(self, company_name: str = None,
                                 industry: str = None,
                                 limit: int = 25) -> List[Dict[str, Any]]:
        """Get filings for company analysis."""
        try:
            # Use existing search functionality
            return await self.search_filings(
                query=company_name or "",
                limit=limit,
                industry=industry
            )
        except Exception as e:
            logger.error(f"Error fetching company filings: {e}")
            return []

    async def get_filing_trends(self, days_back: int = 30,
                               industry: str = None) -> List[Dict[str, Any]]:
        """Get filing trends for trend analysis."""
        try:
            # This would implement trend analysis queries
            # For now, return placeholder data
            return [
                {
                    "date": "2024-01-01",
                    "filing_count": 10,
                    "avg_amount": 5000000,
                    "industry": industry or "All"
                }
            ]
        except Exception as e:
            logger.error(f"Error fetching filing trends: {e}")
            return []
