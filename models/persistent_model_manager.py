#!/usr/bin/env python3
"""
Persistent Model Manager for SEC Form D Analysis

Implements a singleton pattern to keep the Mixtral model loaded in memory
between analyses, dramatically reducing processing time from 25+ minutes
to 2-3 minutes by eliminating model reload overhead.
"""

import json
import logging
import threading
import time
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
import hashlib

import mlx.core as mx
from mlx_lm import load, generate
from mlx_lm.sample_utils import make_sampler

from db.database import DatabaseManager

logger = logging.getLogger(__name__)

class PersistentModelManager:
    """
    Singleton model manager that keeps the LLM loaded in memory
    and provides analysis result caching.
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, *args, **kwargs):
        """Ensure singleton pattern."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, 
                 model_path: Optional[str] = None,
                 temperature: float = 0.1,
                 max_tokens: int = 768,
                 cache_ttl_hours: int = 24):
        """
        Initialize the persistent model manager.
        
        Args:
            model_path: Path to the model (defaults to Mistral-7B)
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            cache_ttl_hours: Hours to keep analysis results cached
        """
        # Prevent re-initialization of singleton
        if hasattr(self, '_initialized'):
            return
            
        self.model_path = model_path or "mlx-community/Mistral-7B-Instruct-v0.2-4bit"
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.cache_ttl_hours = cache_ttl_hours
        
        # Model state
        self.model = None
        self.tokenizer = None
        self.model_loaded = False
        self.load_time = None
        
        # Database for caching
        self.db_manager = DatabaseManager()
        
        # Performance tracking
        self.cache_hits = 0
        self.cache_misses = 0
        self.analysis_count = 0
        
        self._initialized = True
        logger.info("Persistent Model Manager initialized")
    
    def _load_model(self) -> None:
        """Load the model into memory (one-time operation)."""
        if self.model_loaded:
            return
            
        logger.info(f"Loading model: {self.model_path}")
        start_time = time.time()
        
        try:
            self.model, self.tokenizer = load(self.model_path)
            self.model_loaded = True
            self.load_time = time.time() - start_time
            
            logger.info(f"Model loaded successfully in {self.load_time:.2f} seconds")
            
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise
    
    def _get_analysis_cache_key(self, filing_data: Dict[str, Any]) -> str:
        """
        Generate a cache key for analysis results.
        
        Args:
            filing_data: Filing data dictionary
            
        Returns:
            Cache key string
        """
        # Create a hash of the relevant filing data
        cache_data = {
            'id': filing_data.get('id', ''),
            'title': filing_data.get('title', ''),
            'summary': filing_data.get('summary', ''),
            'issuer_name': filing_data.get('issuer_name', ''),
            'filing_date': filing_data.get('filing_date', ''),
            'offering_amount': filing_data.get('offering_amount', ''),
            'model_path': self.model_path,
            'temperature': self.temperature
        }
        
        cache_string = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_string.encode()).hexdigest()
    
    def _get_cached_analysis(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve cached analysis results.
        
        Args:
            cache_key: Cache key for the analysis
            
        Returns:
            Cached analysis results or None
        """
        try:
            # Check if we have a cached result
            cached_result = self.db_manager.get_analysis_result_by_cache_key(cache_key)
            
            if cached_result:
                # Check if cache is still valid (TTL)
                analysis_time = datetime.fromisoformat(cached_result['analysis_timestamp'])
                age_hours = (datetime.now() - analysis_time).total_seconds() / 3600
                
                if age_hours < self.cache_ttl_hours:
                    self.cache_hits += 1
                    logger.info(f"Cache HIT for key {cache_key[:8]}... (age: {age_hours:.1f}h)")
                    return {
                        'relevance_score': cached_result['relevance_score'],
                        'summary': cached_result['summary'],
                        'email_draft': cached_result['email_draft'],
                        'analysis_timestamp': cached_result['analysis_timestamp'],
                        'cached': True
                    }
                else:
                    logger.info(f"Cache EXPIRED for key {cache_key[:8]}... (age: {age_hours:.1f}h)")
            
            self.cache_misses += 1
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving cached analysis: {e}")
            self.cache_misses += 1
            return None
    
    def _cache_analysis_result(self, cache_key: str, filing_id: Optional[int], 
                              analysis_result: Dict[str, Any]) -> None:
        """
        Cache analysis results in the database.
        
        Args:
            cache_key: Cache key for the analysis
            filing_id: Database ID of the filing (if available)
            analysis_result: Analysis results to cache
        """
        try:
            self.db_manager.add_analysis_result(
                filing_id=filing_id,
                relevance_score=analysis_result.get('relevance_score', 0.0),
                summary=analysis_result.get('summary', ''),
                email_draft=analysis_result.get('email_draft', ''),
                analysis_timestamp=analysis_result.get('analysis_timestamp', datetime.now().isoformat()),
                model_name=self.model_path,
                cache_key=cache_key
            )
            logger.debug(f"Cached analysis result for key {cache_key[:8]}...")
            
        except Exception as e:
            logger.error(f"Error caching analysis result: {e}")
    
    def analyze_filing(self, filing_data: Dict[str, Any], 
                      prompt: str,
                      filing_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Analyze a filing with caching support.
        
        Args:
            filing_data: Filing data dictionary
            prompt: Analysis prompt
            filing_id: Database ID of the filing (if available)
            
        Returns:
            Analysis results dictionary
        """
        self.analysis_count += 1
        
        # Generate cache key
        cache_key = self._get_analysis_cache_key(filing_data)
        
        # Check cache first
        cached_result = self._get_cached_analysis(cache_key)
        if cached_result:
            return cached_result
        
        # Load model if not already loaded
        self._load_model()
        
        # Perform analysis
        try:
            logger.info(f"Analyzing filing: {filing_data.get('id', 'unknown')}")
            start_time = time.time()
            
            # Format prompt for Mistral
            formatted_prompt = f"<s>[INST] {prompt} [/INST]"
            
            # Create sampler
            sampler = make_sampler(temp=self.temperature)
            
            # Generate response
            response = generate(
                model=self.model,
                tokenizer=self.tokenizer,
                prompt=formatted_prompt,
                sampler=sampler,
                max_tokens=self.max_tokens
            )
            
            analysis_time = time.time() - start_time
            logger.info(f"Analysis completed in {analysis_time:.2f} seconds")
            
            # Parse the response (assuming JSON format)
            try:
                parsed_result = json.loads(response)
            except json.JSONDecodeError:
                # Fallback parsing if JSON is malformed
                parsed_result = {
                    'relevance_score': 0.5,
                    'summary': response[:500] if response else 'Failed to parse response',
                    'email_draft': ''
                }
            
            # Add metadata
            result = {
                'relevance_score': parsed_result.get('relevance_score', 0.0),
                'summary': parsed_result.get('summary', ''),
                'email_draft': parsed_result.get('email_draft', ''),
                'analysis_timestamp': datetime.now().isoformat(),
                'analysis_time_seconds': analysis_time,
                'cached': False
            }
            
            # Cache the result
            self._cache_analysis_result(cache_key, filing_id, result)
            
            return result
            
        except Exception as e:
            logger.error(f"Error analyzing filing: {e}")
            return {
                'relevance_score': 0.0,
                'summary': f'Error analyzing filing: {str(e)}',
                'email_draft': '',
                'analysis_timestamp': datetime.now().isoformat(),
                'error': str(e),
                'cached': False
            }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        total_requests = self.cache_hits + self.cache_misses
        cache_hit_rate = (self.cache_hits / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'model_loaded': self.model_loaded,
            'model_load_time_seconds': self.load_time,
            'total_analyses': self.analysis_count,
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'cache_hit_rate_percent': cache_hit_rate,
            'model_path': self.model_path
        }
    
    def clear_cache(self, older_than_hours: int = 24) -> int:
        """
        Clear old cached analysis results.
        
        Args:
            older_than_hours: Clear results older than this many hours
            
        Returns:
            Number of results cleared
        """
        try:
            cleared_count = self.db_manager.clear_old_analysis_results(older_than_hours)
            logger.info(f"Cleared {cleared_count} old analysis results")
            return cleared_count
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
            return 0
    
    def cleanup(self) -> None:
        """Clean up resources."""
        try:
            if self.model_loaded:
                mx.clear_cache()
                logger.info("Model cache cleared")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
