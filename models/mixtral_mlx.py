#!/usr/bin/env python3
"""
Mixtral MLX Integration

Provides a wrapper around the Mixtral model using MLX for efficient local inference:
1. Loading and initializing the model
2. Generating text with proper prompting
3. Handling errors and fallbacks
4. Streaming generation with timeout safety
"""

import os
import logging
import time
import signal
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Callable
import json
import threading
import multiprocessing
from multiprocessing import Process, Queue
import ctypes
import sys

# Import MLX libraries
import mlx.core as mx
import mlx_lm
from mlx_lm import load, generate
from mlx_lm.generate import stream_generate, GenerationResponse
from mlx_lm.utils import get_model_path

class TimeoutError(Exception):
    """Exception raised when generation times out."""
    pass

# Global signal handler for graceful interruption
original_sigint_handler = None

def setup_interrupt_handler():
    """Set up a handler for SIGINT to avoid segmentation faults."""
    def sigint_handler(sig, frame):
        logging.warning("Received SIGINT. Cleaning up resources...")
        # Clear MLX cache to free memory
        mx.clear_cache()
        # Call the original handler
        if original_sigint_handler:
            original_sigint_handler(sig, frame)
        else:
            sys.exit(1)

    global original_sigint_handler
    original_sigint_handler = signal.getsignal(signal.SIGINT)
    signal.signal(signal.SIGINT, sigint_handler)

class MixtralMLX:
    """
    Wrapper for Mixtral model using MLX.
    """

    def __init__(self,
                model_path: Optional[str] = None,
                temperature: float = 0.1,
                max_tokens: int = 768,  # Increased for better chain-of-thought reasoning
                models_dir: str = "models",
                generation_timeout: int = 60,  # Increased timeout for Mixtral
                use_multiprocessing: bool = False):  # Use threading by default to avoid model reloading
        """
        Initialize the Mixtral model.

        Args:
            model_path: Path to model weights (if None, uses default)
            temperature: Sampling temperature (lower = more deterministic)
            max_tokens: Maximum tokens to generate
            models_dir: Directory to store downloaded models
            generation_timeout: Timeout in seconds for generation
            use_multiprocessing: Whether to use multiprocessing for generation
        """
        # Set up interrupt handler to avoid segmentation faults
        setup_interrupt_handler()

        self.temperature = temperature
        self.max_tokens = max_tokens
        self.generation_timeout = generation_timeout
        self.models_dir = Path(models_dir)
        self.models_dir.mkdir(exist_ok=True, parents=True)
        self.use_multiprocessing = use_multiprocessing

        # For multiprocessing
        self.active_process = None

        # Initialize model
        try:
            # Use memory-safe model selection
            if not model_path:
                # MEMORY FIX: Use smaller Mistral-7B model to prevent 260GB allocation
                model_path = "mlx-community/Mistral-7B-Instruct-v0.2-4bit"
                logging.info(f"Using Mistral-7B model for memory-safe analysis (switched from Mixtral-8x7B)")

            logging.info(f"Loading model: {model_path}")

            # Load the model directly - MLX will use the local cache if available
            self.model, self.tokenizer = load(model_path)
            logging.info(f"Successfully loaded model: {model_path}")
            self.model_loaded = True
            self.model_name = model_path

            # Create cache for the model (will be recreated for each generation to prevent memory leaks)
            self.prompt_cache = None

        except Exception as e:
            logging.error(f"Error initializing model: {e}")
            raise Exception(f"Failed to load model: {e}")

    # Mock model initialization removed as we now rely on the local model

    def _format_prompt(self, prompt: str) -> str:
        """
        Format prompt for Mixtral model.

        Args:
            prompt: Raw prompt text

        Returns:
            Formatted prompt
        """
        # Add JSON formatting instruction if needed
        if "JSON" not in prompt and "json" not in prompt:
            if prompt.strip().endswith("."):
                prompt += " Please format your response as valid JSON."
            else:
                prompt += ". Please format your response as valid JSON."

        # Mixtral uses a specific format for instructions
        formatted_prompt = f"""<s>[INST] {prompt} [/INST]"""
        return formatted_prompt

    def generate(self, prompt: str, callback: Optional[Callable[[str], None]] = None) -> str:
        """
        Generate text using the model with timeout safety.

        Args:
            prompt: Input prompt
            callback: Optional callback function to receive streaming output

        Returns:
            Generated text
        """
        try:
            # Import make_sampler here to avoid circular imports
            from mlx_lm.sample_utils import make_sampler

            # Format the prompt
            formatted_prompt = self._format_prompt(prompt)

            # Create a sampler with the desired temperature
            sampler = make_sampler(temp=self.temperature)

            # Use streaming generation with timeout
            if self.use_multiprocessing:
                return self._generate_with_multiprocessing(formatted_prompt, callback)
            else:
                return self._generate_with_threading(formatted_prompt, sampler, callback)

        except TimeoutError as e:
            logging.error(f"Generation timed out: {e}")
            self._cleanup_resources()
            return json.dumps({
                "relevance_score": 0.0,
                "summary": "Generation timed out. Please try again with a simpler prompt or increase the timeout.",
                "email_draft": "Generation timed out."
            }, indent=2)
        except Exception as e:
            logging.error(f"Error generating text: {e}")
            self._cleanup_resources()
            # Return a valid JSON error response instead of using mock
            return json.dumps({
                "relevance_score": 0.0,
                "summary": f"Error generating text: {str(e)}",
                "email_draft": "An error occurred during text generation."
            }, indent=2)

    def _generate_with_multiprocessing(self, formatted_prompt: str,
                                      callback: Optional[Callable[[str], None]] = None) -> str:
        """
        Generate text using multiprocessing for better isolation.

        Args:
            formatted_prompt: Formatted prompt text
            callback: Optional callback function for streaming output

        Returns:
            Generated text
        """
        # Create a queue for the result
        result_queue = Queue()

        # Create and start the process
        self.active_process = Process(
            target=self._generation_process,
            args=(formatted_prompt, self.max_tokens, self.temperature, result_queue)
        )
        self.active_process.daemon = True
        self.active_process.start()

        # Wait for the process to complete or timeout
        self.active_process.join(timeout=self.generation_timeout)

        # Check if process completed or timed out
        if self.active_process.is_alive():
            logging.warning("Generation process timed out, terminating...")
            self.active_process.terminate()
            self.active_process.join(timeout=5)  # Give it 5 seconds to clean up

            # If it's still alive, force kill it
            if self.active_process.is_alive():
                logging.warning("Process didn't terminate gracefully, force killing...")
                try:
                    self.active_process.kill()
                except:
                    pass

            self.active_process = None
            raise TimeoutError("Model generation timed out")

        # Get the result from the queue if available
        try:
            if not result_queue.empty():
                result = result_queue.get(block=False)
                if "error" in result and result["error"]:
                    raise Exception(result["error"])
                return result.get("text", "")
            else:
                raise Exception("Process completed but no result was returned")
        except Exception as e:
            logging.error(f"Error retrieving result from process: {e}")
            raise
        finally:
            # Clean up
            self.active_process = None
            self._cleanup_resources()

    @staticmethod
    def _generation_process(formatted_prompt: str, max_tokens: int,
                           temperature: float, result_queue: Queue):
        """
        Process function for text generation.

        Args:
            formatted_prompt: Formatted prompt text
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            result_queue: Queue to store the result
        """
        result = {"text": "", "error": None}

        try:
            # Set up MLX in the new process
            import mlx.core as mx
            from mlx_lm import load
            from mlx_lm.generate import generate
            from mlx_lm.sample_utils import make_sampler

            # Load the memory-safe model in this process
            model_path = "mlx-community/Mistral-7B-Instruct-v0.2-4bit"
            model, tokenizer = load(model_path)

            # Create sampler
            sampler = make_sampler(temp=temperature)

            # Generate text
            text = generate(
                model=model,
                tokenizer=tokenizer,
                prompt=formatted_prompt,
                sampler=sampler,
                max_tokens=max_tokens
            )

            result["text"] = text

        except Exception as e:
            result["error"] = str(e)
            logging.error(f"Error in generation process: {e}")
        finally:
            # Clean up resources
            try:
                mx.clear_cache()
            except:
                pass

            # Put the result in the queue
            result_queue.put(result)

    def _generate_with_threading(self, formatted_prompt: str, sampler: Callable,
                               callback: Optional[Callable[[str], None]] = None) -> str:
        """
        Generate text with threading (fallback method).

        Args:
            formatted_prompt: Formatted prompt text
            sampler: Sampling function for token generation
            callback: Optional callback function for streaming output

        Returns:
            Generated text
        """
        result = {"text": "", "completed": False, "error": None}
        generation_thread = threading.Thread(
            target=self._streaming_generation_thread,
            args=(formatted_prompt, sampler, result, callback)
        )

        # Start generation in a separate thread
        generation_thread.daemon = True
        generation_thread.start()

        # Wait for the generation to complete or timeout
        generation_thread.join(timeout=self.generation_timeout)

        # Check if generation completed or timed out
        if generation_thread.is_alive():
            result["error"] = "Generation timed out"
            # We can't safely terminate the thread, but we can mark it as timed out
            logging.warning("Generation thread is still running after timeout")

            # Return what we have so far or a timeout message
            if result["text"]:
                return result["text"]
            else:
                raise TimeoutError("Model generation timed out")

        if result["error"] and not result["text"]:
            raise Exception(result["error"])

        return result["text"]

    def _streaming_generation_thread(self, formatted_prompt: str, sampler: Callable,
                                    result: Dict[str, Any], callback: Optional[Callable[[str], None]]):
        """
        Thread function for streaming generation.

        Args:
            formatted_prompt: Formatted prompt text
            sampler: Sampling function for token generation
            result: Dictionary to store the generation result
            callback: Optional callback function for streaming output
        """
        try:
            # Clear MLX cache before generation
            mx.clear_cache()

            # Force garbage collection before generation
            import gc
            gc.collect()

            # Create a fresh prompt cache for this generation to prevent memory accumulation
            fresh_cache = mlx_lm.models.cache.make_prompt_cache(self.model)

            # Use streaming generation
            full_text = ""

            # Create generator for streaming tokens
            generator = stream_generate(
                model=self.model,
                tokenizer=self.tokenizer,
                prompt=formatted_prompt,
                sampler=sampler,
                max_tokens=self.max_tokens,
                prompt_cache=fresh_cache
            )

            # Process tokens as they're generated
            token_count = 0
            for response in generator:
                if isinstance(response, GenerationResponse):
                    token_text = response.text
                    full_text += token_text
                    token_count += 1

                    # Call callback if provided
                    if callback:
                        callback(token_text)

                    # Aggressive memory cleanup every 50 tokens
                    if token_count % 50 == 0:
                        mx.clear_cache()
                        import gc
                        gc.collect()

            result["text"] = full_text
            result["completed"] = True

        except Exception as e:
            result["error"] = str(e)
            logging.error(f"Error in streaming generation: {e}")
        finally:
            # Always clean up resources aggressively
            try:
                # Clear the fresh cache
                del fresh_cache
                # Clear MLX cache
                mx.clear_cache()
                # Force garbage collection
                import gc
                gc.collect()
            except Exception as cleanup_error:
                logging.error(f"Error cleaning up resources: {cleanup_error}")

    # Mock generate method removed as we now rely on the local model

    def _cleanup_resources(self):
        """Clean up all resources to prevent memory leaks."""
        # Clear MLX cache
        self.clear_cache()

        # Terminate any active process
        if self.active_process and self.active_process.is_alive():
            try:
                logging.info("Terminating active generation process...")
                self.active_process.terminate()
                self.active_process.join(timeout=3)
                if self.active_process.is_alive():
                    logging.warning("Process didn't terminate, force killing...")
                    self.active_process.kill()
            except Exception as e:
                logging.error(f"Error terminating process: {e}")
            finally:
                self.active_process = None

    def clear_cache(self):
        """Clear MLX memory cache to free up resources."""
        try:
            mx.clear_cache()
        except Exception as e:
            logging.error(f"Error clearing MLX cache: {e}")

    def __del__(self):
        """Cleanup resources when object is destroyed."""
        try:
            self._cleanup_resources()
        except Exception as e:
            logging.error(f"Error during cleanup in __del__: {e}")
