#!/usr/bin/env python3
"""
Heatmap Visualization Module

Generates heatmap visualizations for SEC Form D filings:
1. Creating industry/offering amount heatmaps
2. Generating time-series visualizations
3. Saving visualizations for email inclusion
"""

import os
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import json

# Try to import visualization libraries with fallbacks
try:
    import matplotlib
    matplotlib.use('Agg')  # Non-interactive backend
    import matplotlib.pyplot as plt
    import numpy as np
    import pandas as pd
    import seaborn as sns
    VISUALIZATION_AVAILABLE = True
except ImportError:
    logging.warning("Visualization libraries not available. Using mock implementation.")
    VISUALIZATION_AVAILABLE = False

def generate_heatmap(entries: List[Dict[str, Any]], 
                    output_dir: Optional[Path] = None) -> Optional[str]:
    """
    Generate a heatmap visualization for Form D filings.
    
    Args:
        entries: List of processed entries with scores
        output_dir: Directory to save visualization (default: visualization/)
        
    Returns:
        Path to generated heatmap image or None if generation failed
    """
    if not entries:
        logging.warning("No entries provided for heatmap generation")
        return None
    
    if not VISUALIZATION_AVAILABLE:
        return _generate_mock_heatmap(entries, output_dir)
    
    try:
        # Create output directory if it doesn't exist
        if output_dir is None:
            output_dir = Path("visualization")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Create a DataFrame from entries
        df = _create_dataframe(entries)
        
        # Generate timestamp for filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = output_dir / f"heatmap_{timestamp}.png"
        
        # Create the visualization
        plt.figure(figsize=(12, 8))
        
        # 1. Create industry vs. offering amount heatmap
        plt.subplot(2, 1, 1)
        _create_industry_amount_heatmap(df)
        
        # 2. Create relevance score distribution
        plt.subplot(2, 1, 2)
        _create_relevance_distribution(df)
        
        # Add title and adjust layout
        plt.suptitle("SEC Form D Filings Analysis", fontsize=16)
        plt.tight_layout(rect=[0, 0, 1, 0.95])
        
        # Save the figure
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        logging.info(f"Heatmap saved to {output_path}")
        return str(output_path)
        
    except Exception as e:
        logging.error(f"Error generating heatmap: {e}")
        return _generate_mock_heatmap(entries, output_dir)

def _create_dataframe(entries: List[Dict[str, Any]]) -> 'pd.DataFrame':
    """
    Create a DataFrame from entries for visualization.
    
    Args:
        entries: List of processed entries
        
    Returns:
        Pandas DataFrame
    """
    # Extract relevant fields
    data = []
    for entry in entries:
        # Extract industry (normalize to prevent too many categories)
        industry = entry.get("industry", "Unknown")
        if industry == "":
            industry = "Unknown"
        
        # Extract and bin offering amount
        amount = float(entry.get("offering_amount", 0))
        if amount == 0:
            amount_bin = "Unknown"
        elif amount < 1_000_000:
            amount_bin = "<$1M"
        elif amount < 10_000_000:
            amount_bin = "$1M-$10M"
        elif amount < 50_000_000:
            amount_bin = "$10M-$50M"
        elif amount < 100_000_000:
            amount_bin = "$50M-$100M"
        else:
            amount_bin = ">$100M"
        
        # Extract filing date
        try:
            filing_date = datetime.fromisoformat(entry.get("filing_date", "").replace("Z", "+00:00"))
        except:
            filing_date = datetime.now()
        
        # Create row
        row = {
            "id": entry.get("id", ""),
            "title": entry.get("title", ""),
            "industry": industry,
            "offering_amount": amount,
            "amount_bin": amount_bin,
            "filing_date": filing_date,
            "relevance_score": entry.get("relevance_score", 0.0)
        }
        data.append(row)
    
    # Create DataFrame
    df = pd.DataFrame(data)
    return df

def _create_industry_amount_heatmap(df: 'pd.DataFrame'):
    """
    Create industry vs. offering amount heatmap.
    
    Args:
        df: DataFrame with filing data
    """
    # Create a pivot table for the heatmap
    if 'industry' in df.columns and 'amount_bin' in df.columns:
        # Count filings by industry and amount bin
        pivot = pd.crosstab(df['industry'], df['amount_bin'])
        
        # Sort industries by total count
        industry_totals = pivot.sum(axis=1)
        pivot = pivot.loc[industry_totals.sort_values(ascending=False).index]
        
        # Limit to top 10 industries for readability
        if len(pivot) > 10:
            pivot = pivot.iloc[:10]
        
        # Create heatmap
        sns.heatmap(pivot, annot=True, fmt='d', cmap='YlGnBu', cbar_kws={'label': 'Number of Filings'})
        plt.title('Form D Filings by Industry and Offering Amount')
        plt.ylabel('Industry')
        plt.xlabel('Offering Amount')
    else:
        plt.text(0.5, 0.5, "Insufficient data for industry-amount heatmap", 
                 horizontalalignment='center', verticalalignment='center')

def _create_relevance_distribution(df: 'pd.DataFrame'):
    """
    Create relevance score distribution visualization.
    
    Args:
        df: DataFrame with filing data
    """
    if 'relevance_score' in df.columns and len(df) > 0:
        # Create histogram of relevance scores
        sns.histplot(df['relevance_score'], bins=10, kde=True)
        plt.title('Distribution of Relevance Scores')
        plt.xlabel('Relevance Score')
        plt.ylabel('Number of Filings')
        
        # Add vertical line at mean
        mean_score = df['relevance_score'].mean()
        plt.axvline(mean_score, color='r', linestyle='--', 
                   label=f'Mean Score: {mean_score:.2f}')
        plt.legend()
    else:
        plt.text(0.5, 0.5, "Insufficient data for relevance score distribution", 
                 horizontalalignment='center', verticalalignment='center')

def _generate_mock_heatmap(entries: List[Dict[str, Any]], 
                          output_dir: Optional[Path] = None) -> Optional[str]:
    """
    Generate a mock heatmap for development/testing.
    
    Args:
        entries: List of processed entries
        output_dir: Directory to save visualization
        
    Returns:
        Path to mock heatmap JSON file
    """
    logging.warning("Generating mock heatmap visualization")
    
    # Create output directory if it doesn't exist
    if output_dir is None:
        output_dir = Path("visualization")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Generate timestamp for filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = output_dir / f"mock_heatmap_{timestamp}.json"
    
    # Create a mock visualization data structure
    mock_data = {
        "type": "mock_heatmap",
        "generated_at": datetime.now().isoformat(),
        "entry_count": len(entries),
        "industries": {},
        "amount_bins": {
            "<$1M": 0,
            "$1M-$10M": 0,
            "$10M-$50M": 0,
            "$50M-$100M": 0,
            ">$100M": 0
        },
        "relevance_scores": {
            "min": 0.0,
            "max": 0.0,
            "mean": 0.0,
            "median": 0.0
        }
    }
    
    # Populate with actual data from entries
    if entries:
        # Count industries
        for entry in entries:
            industry = entry.get("industry", "Unknown")
            if industry == "":
                industry = "Unknown"
            
            mock_data["industries"][industry] = mock_data["industries"].get(industry, 0) + 1
            
            # Count amount bins
            amount = float(entry.get("offering_amount", 0))
            if amount == 0:
                pass  # Skip unknown amounts
            elif amount < 1_000_000:
                mock_data["amount_bins"]["<$1M"] += 1
            elif amount < 10_000_000:
                mock_data["amount_bins"]["$1M-$10M"] += 1
            elif amount < 50_000_000:
                mock_data["amount_bins"]["$10M-$50M"] += 1
            elif amount < 100_000_000:
                mock_data["amount_bins"]["$50M-$100M"] += 1
            else:
                mock_data["amount_bins"][">$100M"] += 1
        
        # Calculate relevance score statistics
        scores = [entry.get("relevance_score", 0.0) for entry in entries]
        if scores:
            mock_data["relevance_scores"]["min"] = min(scores)
            mock_data["relevance_scores"]["max"] = max(scores)
            mock_data["relevance_scores"]["mean"] = sum(scores) / len(scores)
            mock_data["relevance_scores"]["median"] = sorted(scores)[len(scores) // 2]
    
    # Save mock data
    with open(output_path, 'w') as f:
        json.dump(mock_data, f, indent=2)
    
    logging.info(f"Mock heatmap data saved to {output_path}")
    return str(output_path)
