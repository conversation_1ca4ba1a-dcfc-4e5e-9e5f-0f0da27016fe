# ingest/zip_on_demand.py
"""
Fetch the feed, rank top-N dates, and download only those ZIPs with robust error handling.

Features:
- Prioritizes downloading ZIPs for the most relevant dates
- Supports skipping ZIP downloads for faster development iterations
- Provides clear progress indicators and early exit on success
- Creates placeholder data when needed
"""

import json
import argparse
import time
from pathlib import Path
from ingest.form_d_feed import fetch_feed
from rank.scorer import rank_entries
from ingest.form_d_zip import download_and_extract, create_placeholder_data
from datetime import datetime

def parse_date(entry):
    """Extract date from feed entry."""
    return datetime.fromisoformat(entry["updated"].replace("Z","+00:00")).date()

def main(top_n=10, skip_zip=False, max_attempts=20, early_exit=True):
    """
    Fetch and process Form D ZIPs for the most relevant dates.

    Args:
        top_n: Number of top-ranked entries to consider
        skip_zip: Whether to skip ZIP downloads and use placeholders
        max_attempts: Maximum number of URL probes to try before giving up
        early_exit: Whether to exit after first successful download

    Returns:
        List of paths to the downloaded/created JSONL files
    """
    print("\n=== Form D ZIP On-Demand Downloader ===")

    if skip_zip:
        print("[zod] ZIP download skipped (--skip-zip flag). Using placeholder data.")
        today = datetime.now().date()
        placeholder_path = create_placeholder_data(today)
        return [placeholder_path]

    # Start timer
    start_time = time.time()

    # Fetch and rank entries
    print("[zod] Fetching and ranking feed entries...")
    entries = fetch_feed()
    if not entries:
        print("[zod] No feed entries—skipping ZIP fetch.")
        return []

    ranked = rank_entries(entries)
    top_entries = ranked[:top_n]
    dates = { parse_date(e) for e in top_entries }

    if not dates:
        print("[zod] No valid dates found in top entries.")
        return []

    print(f"[zod] Will fetch ZIPs for {len(dates)} dates: {', '.join(d.isoformat() for d in dates)}")
    print(f"[zod] Max probe attempts per date: {max_attempts}")
    print(f"[zod] Early exit after first success: {early_exit}")

    # Create a placeholder file if we can't download real data
    placeholder_created = False
    successful_downloads = []

    for i, d in enumerate(dates):
        print(f"\n[zod] Processing date {i+1}/{len(dates)}: {d.isoformat()}")
        try:
            # Pass max_attempts to download_and_extract
            result_path = download_and_extract(d, url=None, max_attempts=max_attempts)
            if result_path:
                successful_downloads.append(result_path)
                print(f"[zod] Successfully processed date {d.isoformat()}")

                # Exit early if requested and we have at least one success
                if early_exit and successful_downloads:
                    print(f"[zod] Early exit after successful download (early_exit=True)")
                    break

        except Exception as ex:
            print(f"[zod] ERROR downloading for {d}: {ex}")

            # Create a placeholder file if we haven't already
            if not placeholder_created:
                placeholder_dir = Path(f"data/raw/placeholder")
                placeholder_dir.mkdir(parents=True, exist_ok=True)
                placeholder_file = placeholder_dir / f"formd_placeholder.jsonl"

                # Create a sample filing
                sample_filings = []
                for i in range(5):
                    sample_filing = {
                        "issuerName": f"Example Corp {i+1}",
                        "filingDate": d.isoformat(),
                        "offeringAmount": str(1000000 * (i+1)),
                        "industryGroup": "Technology",
                        "summary": "This is a placeholder filing for development purposes."
                    }
                    sample_filings.append(json.dumps(sample_filing))

                placeholder_file.write_text("\n".join(sample_filings))
                print(f"[zod] Created placeholder file at {placeholder_file}")
                placeholder_created = True
                successful_downloads.append(placeholder_file)

    # Report results
    elapsed_time = time.time() - start_time
    print(f"\n[zod] ZIP processing completed in {elapsed_time:.2f} seconds")
    print(f"[zod] Successful downloads: {len(successful_downloads)}")
    for path in successful_downloads:
        print(f"[zod]   - {path}")

    return successful_downloads

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Download Form D ZIPs on demand")
    parser.add_argument("--top-n", type=int, default=10, help="Number of top-ranked entries to consider")
    parser.add_argument("--skip-zip", action="store_true", help="Skip ZIP downloads and use placeholders")
    parser.add_argument("--max-attempts", type=int, default=20, help="Maximum number of URL probes to try")
    parser.add_argument("--no-early-exit", action="store_true", help="Don't exit after first successful download")

    args = parser.parse_args()

    main(
        top_n=args.top_n,
        skip_zip=args.skip_zip,
        max_attempts=args.max_attempts,
        early_exit=not args.no_early_exit
    )
