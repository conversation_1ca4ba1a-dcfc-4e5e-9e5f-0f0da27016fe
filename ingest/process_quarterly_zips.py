#!/usr/bin/env python3
"""
Process quarterly Form D ZIP files with the format YYYYqQ_d.zip.

This module extracts and processes quarterly Form D ZIP files that are stored
in the data/raw directory with the naming format YYYYqQ_d.zip (e.g., 2023q1_d.zip).
It extracts the TSV files from the ZIPs and combines them into JSONL files for
easier processing by the vector store and other components.
"""

import os
import re
import json
import csv
import zipfile
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('quarterly_processor')

# TSV files we need to process
TSV_FILES = [
    "FORMDSUBMISSION.tsv",
    "ISSUERS.tsv",
    "OFFERING.tsv"
]

def find_quarterly_zips(data_dir: str = "data/raw") -> List[Path]:
    """
    Find all quarterly ZIP files in the data directory.

    Args:
        data_dir: Directory to search for ZIP files

    Returns:
        List of Path objects for the quarterly ZIP files
    """
    # Create pattern to match YYYYqQ_d.zip format
    pattern = re.compile(r"^\d{4}q[1-4]_d\.zip$")

    # Find all matching files
    data_path = Path(data_dir)
    zip_files = [
        f for f in data_path.glob("*.zip")
        if pattern.match(f.name)
    ]

    # Sort by year and quarter
    zip_files.sort()

    logger.info(f"Found {len(zip_files)} quarterly ZIP files")
    return zip_files

def read_tsv_file(file_path: Path) -> List[Dict[str, str]]:
    """
    Read a TSV file and return a list of dictionaries.

    Args:
        file_path: Path to the TSV file

    Returns:
        List of dictionaries, one for each row
    """
    try:
        with open(file_path, 'r', encoding='utf-8-sig') as f:
            reader = csv.DictReader(f, delimiter='\t')
            return list(reader)
    except Exception as e:
        logger.error(f"Error reading TSV file {file_path}: {e}")
        return []

def extract_and_process_zip(zip_path: Path) -> Optional[Path]:
    """
    Extract and process a quarterly ZIP file.

    Args:
        zip_path: Path to the ZIP file

    Returns:
        Path to the created JSONL file or None if failed
    """
    # Extract year and quarter from filename
    match = re.match(r"(\d{4})q([1-4])_d\.zip", zip_path.name)
    if not match:
        logger.error(f"Invalid ZIP filename format: {zip_path.name}")
        return None

    year, quarter = match.groups()
    quarter_key = f"{year}q{quarter}"

    # Create output directory
    output_dir = zip_path.parent / quarter_key
    output_dir.mkdir(exist_ok=True, parents=True)

    # Extract ZIP contents
    logger.info(f"Extracting {zip_path.name}...")
    try:
        with zipfile.ZipFile(zip_path) as z:
            # Extract only the TSV files we need
            for file_info in z.infolist():
                if any(tsv_file in file_info.filename.upper() for tsv_file in TSV_FILES):
                    z.extract(file_info, output_dir)
    except Exception as e:
        logger.error(f"Error extracting {zip_path.name}: {e}")
        return None

    # Find and process TSV files
    submissions = []
    issuers = {}
    offerings = {}

    # Process FORMDSUBMISSION.tsv
    submission_files = list(output_dir.rglob("*FORMDSUBMISSION.tsv"))
    if submission_files:
        submissions = read_tsv_file(submission_files[0])
        logger.info(f"Found {len(submissions)} submissions in {submission_files[0].name}")
    else:
        logger.warning(f"No FORMDSUBMISSION.tsv found in {zip_path.name}")

    # Process ISSUERS.tsv
    issuer_files = list(output_dir.rglob("*ISSUERS.tsv"))
    if issuer_files:
        issuer_rows = read_tsv_file(issuer_files[0])
        # Group issuers by ACCESSIONNUMBER
        for row in issuer_rows:
            # The column name might be different in different files
            accession = row.get('ACCESSIONNUMBER', row.get('ACCESSION_NUMBER', row.get('ACCESSION_NUM', '')))
            if accession:
                if accession not in issuers:
                    issuers[accession] = []
                issuers[accession].append(row)
        logger.info(f"Found {len(issuer_rows)} issuer entries for {len(issuers)} filings")
    else:
        logger.warning(f"No ISSUERS.tsv found in {zip_path.name}")

    # Process OFFERING.tsv
    offering_files = list(output_dir.rglob("*OFFERING.tsv"))
    if offering_files:
        offering_rows = read_tsv_file(offering_files[0])
        # Group offerings by ACCESSIONNUMBER
        for row in offering_rows:
            # The column name might be different in different files
            accession = row.get('ACCESSIONNUMBER', row.get('ACCESSION_NUMBER', row.get('ACCESSION_NUM', '')))
            if accession:
                offerings[accession] = row
        logger.info(f"Found {len(offering_rows)} offering entries")
    else:
        logger.warning(f"No OFFERING.tsv found in {zip_path.name}")

    # Combine data into filings
    filings = []
    for submission in submissions:
        # The column name might be different in different files
        accession = submission.get('ACCESSIONNUMBER', submission.get('ACCESSION_NUMBER', submission.get('ACCESSION_NUM', '')))
        if not accession:
            continue

        # Get issuer name from issuers data if available
        issuer_name = ""
        if accession in issuers and issuers[accession]:
            # Try to get the primary issuer
            primary_issuers = [i for i in issuers[accession]
                              if i.get('IS_PRIMARYISSUER_FLAG', '').upper() == 'YES']
            if primary_issuers:
                issuer_name = primary_issuers[0].get('ENTITYNAME', '')
            else:
                # If no primary issuer, use the first one
                issuer_name = issuers[accession][0].get('ENTITYNAME', '')

        # Get filing date with fallbacks
        filing_date = submission.get('FILING_DATE', '')

        # Get industry group from offering data
        industry_group = ""
        if accession in offerings:
            industry_group = offerings[accession].get('INDUSTRYGROUPTYPE', '')

        # Get offering amount with fallbacks
        offering_amount = '0'
        if accession in offerings:
            offering_amount = offerings[accession].get('TOTALOFFERINGAMOUNT',
                                offerings[accession].get('TOTALAMOUNTSOLD', '0'))

        # Skip if we don't have basic information
        if not issuer_name or not filing_date:
            continue

        # Create a filing object
        filing = {
            "accessionNumber": accession,
            "filingDate": filing_date,
            "issuerName": issuer_name,
            "industryGroup": industry_group,
            "offeringAmount": offering_amount,
            "offeringData": offerings.get(accession, {}),
            "issuers": issuers.get(accession, [])
        }

        # Add to filings list
        filings.append(filing)

    if not filings:
        logger.warning(f"No valid filings found in {zip_path.name}")
        return None

    # Create JSONL file
    jsonl_path = output_dir / f"formd_{quarter_key}.jsonl"
    try:
        jsonl_path.write_text("\n".join(json.dumps(o) for o in filings))
        logger.info(f"Saved {len(filings)} filings to {jsonl_path}")
        return jsonl_path
    except Exception as e:
        logger.error(f"Error creating JSONL file: {e}")
        return None

def process_all_quarterly_zips(data_dir: str = "data/raw") -> List[Path]:
    """
    Process all quarterly ZIP files in the data directory.

    Args:
        data_dir: Directory containing the ZIP files

    Returns:
        List of paths to the created JSONL files
    """
    zip_files = find_quarterly_zips(data_dir)
    if not zip_files:
        logger.warning(f"No quarterly ZIP files found in {data_dir}")
        return []

    jsonl_files = []
    for zip_file in zip_files:
        jsonl_path = extract_and_process_zip(zip_file)
        if jsonl_path:
            jsonl_files.append(jsonl_path)

    logger.info(f"Processed {len(jsonl_files)} quarterly ZIP files")
    return jsonl_files

if __name__ == "__main__":
    # Process all quarterly ZIP files when run directly
    process_all_quarterly_zips()
