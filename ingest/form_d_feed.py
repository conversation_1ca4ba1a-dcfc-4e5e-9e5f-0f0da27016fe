# ingest/form_d_feed.py
"""
Pull the SEC’s real-time Form D ATOM feed (latest filings)
with validation and error handling.

Following SEC best practices:
1. Responsible polling (15-30 minute intervals)
2. Proper User-Agent headers
3. Rate limiting (max 10 requests per second)
4. Robust error handling
5. Comprehensive logging
"""

import datetime as dt
import json
import logging
import time
import requests
from pathlib import Path
from bs4 import BeautifulSoup
import xml.etree.ElementTree as ET
from typing import List, Dict, Any, Optional

# Configure proper headers per SEC guidelines
HEADERS = {
    "User-Agent": "PrivateSignals/0.2 (<EMAIL>)",
    "Accept-Encoding": "identity",
}

# Base URL for the SEC ATOM feed
FEED_URL = (
    "https://www.sec.gov/cgi-bin/browse-edgar"
    "?action=getcurrent&type=D&owner=include&count=100&output=atom"
)

# Required fields for valid entries
REQUIRED_ENTRY_KEYS = ["id", "link", "summary"]

# Configure logging
logger = logging.getLogger("sec_feed")
logger.setLevel(logging.INFO)
if not logger.handlers:
    # Create file handler
    fh = logging.FileHandler("logs/sec_feed.log")
    fh.setLevel(logging.INFO)
    # Create console handler
    ch = logging.StreamHandler()
    ch.setLevel(logging.INFO)
    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    fh.setFormatter(formatter)
    ch.setFormatter(formatter)
    # Add handlers to logger
    logger.addHandler(fh)
    logger.addHandler(ch)

def fetch_feed(retry_count: int = 3, retry_delay: int = 5) -> List[Dict[str, Any]]:
    """
    Fetch and return only valid feed entries as dicts, ensuring unique IDs.

    Args:
        retry_count: Number of retries on failure
        retry_delay: Delay between retries in seconds

    Returns:
        List of validated feed entries
    """
    logger.info(f"Fetching SEC ATOM feed from {FEED_URL}")

    # Track attempts
    attempt = 0
    while attempt < retry_count:
        attempt += 1
        try:
            # Implement rate limiting
            if attempt > 1:
                logger.info(f"Retry attempt {attempt}/{retry_count} after {retry_delay}s delay")
                time.sleep(retry_delay)

            # Fetch the feed
            logger.info("Sending request to SEC ATOM feed")
            resp = requests.get(FEED_URL, headers=HEADERS, timeout=30)
            resp.raise_for_status()

            # Parse XML with error handling
            try:
                soup = BeautifulSoup(resp.text, "xml")
                # Validate that we have a proper feed
                if not soup.find('feed'):
                    logger.error("Response does not contain a valid feed element")
                    raise ValueError("Invalid feed format")
            except Exception as xml_err:
                logger.error(f"XML parsing error: {xml_err}")
                # Log the raw response content for debugging
                response_preview = resp.text[:1000] if len(resp.text) > 1000 else resp.text
                logger.error(f"Raw response content (first 1000 chars): {response_preview}")
                # Try alternative parsing method
                logger.info("Attempting alternative XML parsing")
                try:
                    root = ET.fromstring(resp.text)
                    # Convert ElementTree to BeautifulSoup for consistent processing
                    from bs4 import BeautifulStoneSoup
                    soup = BeautifulStoneSoup(ET.tostring(root))
                except Exception as alt_err:
                    logger.error(f"Alternative parsing also failed: {alt_err}")
                    # Log content type and headers for additional debugging
                    logger.error(f"Response content type: {resp.headers.get('Content-Type', 'unknown')}")
                    logger.error(f"Response status code: {resp.status_code}")
                    logger.error(f"Response headers: {dict(resp.headers)}")
                    raise

            # Log success
            logger.info("Successfully fetched and parsed SEC ATOM feed")
            break

        except requests.exceptions.HTTPError as e:
            logger.error(f"HTTP error: {e}")
            if e.response.status_code == 429:
                logger.warning("Rate limited by SEC (429). Implementing longer backoff.")
                time.sleep(retry_delay * 5)  # Longer backoff for rate limiting
            elif attempt < retry_count:
                continue
            else:
                logger.error("Max retries reached. Returning empty list.")
                return []

        except requests.exceptions.ConnectionError as e:
            logger.error(f"Connection error: {e}")
            if attempt < retry_count:
                continue
            else:
                logger.error("Max retries reached. Returning empty list.")
                return []

        except requests.exceptions.Timeout as e:
            logger.error(f"Timeout error: {e}")
            if attempt < retry_count:
                continue
            else:
                logger.error("Max retries reached. Returning empty list.")
                return []

        except Exception as e:
            logger.error(f"Unexpected error fetching Atom feed: {e}")
            if attempt < retry_count:
                continue
            else:
                logger.error("Max retries reached. Returning empty list.")
                return []

    # Process entries with duplicate handling
    entries_dict = {}
    entry_count = 0
    duplicate_count = 0
    error_count = 0

    # Find all entry elements
    entry_elements = soup.find_all("entry")
    logger.info(f"Found {len(entry_elements)} raw entries in feed")

    for entry in entry_elements:
        entry_count += 1
        try:
            # Extract entry data
            entry_data = {
                "id": entry.id.text.strip() if entry.id else "",
                "title": entry.title.text.strip() if entry.title else "",
                "updated": entry.updated.text.strip() if entry.updated else "",
                "link": entry.link["href"].strip() if entry.link and entry.link.has_attr("href") else "",
                "summary": entry.summary.text.strip() if entry.summary else "",
                "fetched_at": dt.datetime.now().isoformat(),
            }

            # Extract accession number for better tracking
            accession_number = None
            if "accession-number=" in entry_data["id"]:
                accession_number = entry_data["id"].split("accession-number=")[1].split(")")[0]
                entry_data["accession_number"] = accession_number

            # Validate required fields
            if not all(entry_data.get(k) for k in REQUIRED_ENTRY_KEYS):
                missing = [k for k in REQUIRED_ENTRY_KEYS if not entry_data.get(k)]
                logger.warning(f"Skipping incomplete entry {entry_count}: missing {missing}")
                continue

            # Filter for Form D filings only - use inclusion rather than prefix
            title = entry_data["title"].upper()
            if not ("FORM D" in title or "D/" in title or title.startswith("D ") or title.startswith("D/A ")):
                logger.debug(f"Skipping non-Form D entry: {entry_data['title']}")
                continue

            # Handle duplicates - use ID as primary key
            entry_id = entry_data["id"]
            if entry_id in entries_dict:
                duplicate_count += 1
                # Log at INFO level for better visibility
                logger.info(f"Found duplicate ID: {entry_id} - Title: {entry_data['title']}")

                # Compare updated timestamps if available
                if entry_data.get("updated") > entries_dict[entry_id].get("updated", ""):
                    old_updated = entries_dict[entry_id].get("updated", "")
                    new_updated = entry_data.get("updated", "")
                    logger.info(f"Replacing with newer entry: {old_updated} -> {new_updated}")
                    entries_dict[entry_id] = entry_data
                else:
                    logger.info(f"Keeping existing entry (newer or same timestamp)")
                    # Track skipped updates for summary
                    if not hasattr(fetch_feed, 'skipped_updates'):
                        fetch_feed.skipped_updates = 0
                    fetch_feed.skipped_updates += 1
            else:
                entries_dict[entry_id] = entry_data

        except Exception as ex:
            error_count += 1
            logger.error(f"Error parsing entry {entry_count}: {ex}")
            continue

    # Convert dictionary back to list
    entries = list(entries_dict.values())

    # Get counts of replaced and skipped entries
    replaced_count = duplicate_count - getattr(fetch_feed, 'skipped_updates', 0)
    skipped_count = getattr(fetch_feed, 'skipped_updates', 0)

    # Log detailed summary
    logger.info(f"Processed {entry_count} entries: {len(entries)} unique, {duplicate_count} duplicates, {error_count} errors")
    if duplicate_count > 0:
        logger.info(f"Duplicate handling: {replaced_count} entries replaced with newer versions, {skipped_count} entries skipped (already had newer version)")

    # Reset the counter for next run
    if hasattr(fetch_feed, 'skipped_updates'):
        fetch_feed.skipped_updates = 0

    return entries

def save_feed(entries: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Save validated entries to data/raw/YYYYMMDD_formd_feed.jsonl.

    Args:
        entries: List of feed entries to save

    Returns:
        The same list of entries (for chaining)
    """
    if not entries:
        logger.warning("No entries to save")
        return entries

    today = dt.date.today().strftime("%Y%m%d")
    out = Path(f"data/raw/{today}_formd_feed.jsonl")
    out.parent.mkdir(parents=True, exist_ok=True)

    try:
        # Check for existing file to handle incremental updates
        if out.exists():
            logger.info(f"Updating existing file: {out}")

            # Load existing entries
            existing_entries = {}
            try:
                with open(out, 'r') as f:
                    for line in f:
                        if line.strip():
                            entry = json.loads(line)
                            if "id" in entry:
                                existing_entries[entry["id"]] = entry
                logger.info(f"Loaded {len(existing_entries)} existing entries")
            except Exception as e:
                logger.error(f"Error loading existing entries: {e}")
                # Continue with empty existing entries

            # Merge with new entries, keeping newer versions
            for entry in entries:
                entry_id = entry["id"]
                if entry_id in existing_entries:
                    # Keep newer version based on updated timestamp
                    if entry.get("updated", "") > existing_entries[entry_id].get("updated", ""):
                        existing_entries[entry_id] = entry
                else:
                    existing_entries[entry_id] = entry

            # Convert back to list
            merged_entries = list(existing_entries.values())
            logger.info(f"Merged to {len(merged_entries)} total entries")

            # Write merged entries
            out.write_text("\n".join(json.dumps(e) for e in merged_entries))
            logger.info(f"Saved {len(merged_entries)} merged entries to {out}")
            return merged_entries
        else:
            # New file - just write entries directly
            out.write_text("\n".join(json.dumps(e) for e in entries))
            logger.info(f"Saved {len(entries)} new entries to {out}")
            return entries

    except Exception as e:
        logger.error(f"Error writing JSONL: {e}")
        # Try to write to a backup location
        try:
            backup = Path(f"logs/backup_{today}_formd_feed.jsonl")
            backup.parent.mkdir(parents=True, exist_ok=True)
            backup.write_text("\n".join(json.dumps(e) for e in entries))
            logger.info(f"Saved backup to {backup}")
        except Exception as backup_err:
            # Critical error - both primary and backup writes failed
            critical_msg = f"CRITICAL ERROR: Both primary and backup writes failed. Primary error: {e}, Backup error: {backup_err}"
            logger.critical(critical_msg)
            print(f"\n!!! {critical_msg} !!!\n")

            # Try one last emergency write to a different location
            try:
                emergency_path = Path(f"emergency_{today}_formd_feed.jsonl")
                emergency_path.write_text("\n".join(json.dumps(e) for e in entries))
                logger.warning(f"Emergency save to current directory: {emergency_path}")
            except Exception:
                # If this also fails, we're out of options
                pass

            # Raise a custom exception to alert monitoring systems
            class DataPersistenceError(Exception):
                """Raised when all data persistence attempts fail"""
                pass

            raise DataPersistenceError(critical_msg)

    return entries

def poll_feed(interval_minutes: int = 15, max_polls: Optional[int] = None):
    """
    Poll the SEC ATOM feed at regular intervals.

    Args:
        interval_minutes: Minutes between polls
        max_polls: Maximum number of polls (None for indefinite)
    """
    logger.info(f"Starting SEC ATOM feed polling (interval: {interval_minutes} minutes)")
    print(f"Starting SEC ATOM feed polling (interval: {interval_minutes} minutes)")

    poll_count = 0
    try:
        while max_polls is None or poll_count < max_polls:
            poll_count += 1
            poll_msg = f"Poll #{poll_count}" + (f"/{max_polls}" if max_polls else "")
            logger.info(poll_msg)
            print(f"\n=== {poll_msg} ===")

            try:
                # Fetch and save feed
                start_time = dt.datetime.now()
                entries = fetch_feed()

                if entries:
                    save_feed(entries)
                    print(f"Processed {len(entries)} entries")
                else:
                    print("No entries found in this poll")

                # Calculate elapsed time
                elapsed = (dt.datetime.now() - start_time).total_seconds()
                print(f"Poll completed in {elapsed:.2f} seconds")

                # Process successful, wait for next interval
                if max_polls is None or poll_count < max_polls:
                    next_poll = dt.datetime.now() + dt.timedelta(minutes=interval_minutes)
                    next_poll_msg = f"Next poll scheduled for {next_poll.strftime('%Y-%m-%d %H:%M:%S')}"
                    logger.info(next_poll_msg)
                    print(f"\n{next_poll_msg}")
                    print(f"Waiting {interval_minutes} minutes... (Press Ctrl+C to stop)")

                    # Sleep in smaller increments to allow for more responsive interruption
                    sleep_increment = 10  # seconds
                    for _ in range(int(interval_minutes * 60 / sleep_increment)):
                        time.sleep(sleep_increment)

            except Exception as e:
                logger.error(f"Error in polling cycle {poll_count}: {e}")
                print(f"\nERROR in polling cycle {poll_count}: {e}")
                # Wait a shorter time before retry on error
                print("Waiting 60 seconds before retry...")
                time.sleep(60)

        logger.info("Polling complete - reached maximum poll count")
        print("\nPolling complete - reached maximum poll count")

    except KeyboardInterrupt:
        logger.info(f"Polling stopped by user after {poll_count} polls")
        print(f"\nPolling stopped by user after {poll_count} polls")

    print("\nSEC ATOM feed polling ended")

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="SEC ATOM Feed Poller")
    parser.add_argument("--poll", action="store_true", help="Run in polling mode")
    parser.add_argument("--interval", type=int, default=15, help="Polling interval in minutes")
    parser.add_argument("--max-polls", type=int, default=None, help="Maximum number of polls")

    args = parser.parse_args()

    if args.poll:
        print(f"\n=== Starting SEC ATOM Feed Poller ===")
        print(f"Polling interval: {args.interval} minutes")
        print(f"Max polls: {'Unlimited' if args.max_polls is None else args.max_polls}")
        print(f"Press Ctrl+C to stop\n")
        poll_feed(interval_minutes=args.interval, max_polls=args.max_polls)
    else:
        print("\n=== Running Single SEC ATOM Feed Fetch ===")
        print("Note: This will run once and exit. For continuous polling, use --poll flag.")
        print("Example: python -m ingest.form_d_feed --poll --interval 15\n")
        # Single fetch and save
        save_feed(fetch_feed())
        print("\nCompleted single fetch. Exiting.")
