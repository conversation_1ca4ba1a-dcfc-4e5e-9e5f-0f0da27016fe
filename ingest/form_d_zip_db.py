#!/usr/bin/env python3
"""
Enhanced Form D ZIP Downloader with Database Integration

Downloads and processes SEC Form D ZIP files with database tracking:
1. Checks the database for cached ZIP files before downloading
2. Tracks download status, errors, and metadata
3. Implements proper caching with TTL and LRU policies
4. Provides robust error handling and recovery
"""

import os
import re
import json
import time
import zipfile
import logging
import requests
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union

from db.database import DatabaseManager
from db.cache_manager import CacheManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Constants
BASE_URLS = [
    "https://www.sec.gov/files/dera/data/form-d",
    "https://www.sec.gov/files/dera/data/form-d-filings",
    "https://www.sec.gov/files/form-d",
    "https://www.sec.gov/dera/data/form-d"
]

HEADERS = {
    "User-Agent": "Private-Signals Research Tool/1.0 (<EMAIL>)",
    "Accept": "application/zip, application/octet-stream"
}

FILING_REGEX = re.compile(r"formD_\d+\.json$")

class FormDZipDownloader:
    """
    Enhanced Form D ZIP downloader with database integration.
    """

    def __init__(self, db_manager: DatabaseManager, cache_manager: CacheManager,
                data_dir: str = "data"):
        """
        Initialize the Form D ZIP downloader.

        Args:
            db_manager: Database manager instance
            cache_manager: Cache manager instance
            data_dir: Directory for data storage
        """
        self.db_manager = db_manager
        self.cache_manager = cache_manager
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)

    def probe(self, url: str) -> bool:
        """
        Check if a URL exists without downloading the full content.

        Args:
            url: URL to probe

        Returns:
            True if the URL exists and is accessible
        """
        try:
            r = requests.head(url, headers=HEADERS, timeout=10)
            return r.status_code == 200
        except requests.RequestException:
            return False

    def find_latest(self, max_attempts: int = 30) -> Tuple[datetime, str]:
        """
        Find the latest available Form D ZIP file.

        Args:
            max_attempts: Maximum number of attempts to find a ZIP

        Returns:
            Tuple of (date, url) for the latest ZIP

        Raises:
            RuntimeError: If no ZIP file is found within max_attempts
        """
        today = datetime.now().date()
        attempt_count = 0

        # Check database first for recent successful downloads
        recent_zips = self.db_manager.search_form_d_filings(
            filing_date_start=(today - timedelta(days=30)).isoformat(),
            filing_date_end=today.isoformat(),
            limit=1
        )

        if recent_zips:
            # Get the most recent ZIP file
            recent_zip = self.db_manager.get_zip_file(zip_id=recent_zips[0]["source_zip_id"])
            if recent_zip and recent_zip["status"] in ["downloaded", "extracted", "processed"]:
                logger.info(f"Found recent ZIP in database: {recent_zip['filename']}")
                return datetime.strptime(recent_zip["date_str"], "%Y%m%d").date(), recent_zip["url"]

        # Try all base URLs with different patterns
        for base in BASE_URLS:
            # 1) Try evergreen alias patterns
            for pattern in ["current_formd.zip", "current-formd.zip", "formd_current.zip", "formd-current.zip"]:
                attempt_count += 1
                if attempt_count > max_attempts:
                    logger.error(f"Reached maximum attempts ({max_attempts}). Stopping search.")
                    logger.info("No Form D ZIP found within attempt limit. Creating placeholder...")
                    self.create_placeholder_data(today)
                    raise RuntimeError(f"No Form D ZIP found within {max_attempts} attempts")

                cur = f"{base}/{pattern}"
                logger.info(f"Attempt {attempt_count}/{max_attempts}: Probing evergreen URL: {cur}")
                if self.probe(cur):
                    logger.info(f"SUCCESS! Found ZIP at {cur}")
                    return today, cur
                time.sleep(5)  # respect crawl-delay

            # 2) Try today's date with different formats
            for fmt in ["%Y%m%d", "%Y-%m-%d", "%Y_%m_%d"]:
                ymd = today.strftime(fmt)
                for pattern in [f"formd-{ymd}.zip", f"formd_{ymd}.zip"]:
                    attempt_count += 1
                    if attempt_count > max_attempts:
                        logger.error(f"Reached maximum attempts ({max_attempts}). Stopping search.")
                        logger.info("No Form D ZIP found within attempt limit. Creating placeholder...")
                        self.create_placeholder_data(today)
                        raise RuntimeError(f"No Form D ZIP found within {max_attempts} attempts")

                    url = f"{base}/{pattern}"
                    logger.info(f"Attempt {attempt_count}/{max_attempts}: Probing today's URL: {url}")
                    if self.probe(url):
                        logger.info(f"SUCCESS! Found ZIP at {url}")
                        return today, url
                    time.sleep(5)  # respect crawl-delay

            # 3) Try recent dates (last 6 months)
            for days_ago in range(1, 180):
                d = today - timedelta(days=days_ago)

                # Check if we already have this date in the database
                cached_zip = self.cache_manager.get_cached_zip(d.strftime("%Y%m%d"))
                if cached_zip and cached_zip["status"] in ["downloaded", "extracted", "processed"]:
                    logger.info(f"Found cached ZIP for {d}: {cached_zip['filename']}")
                    return d, cached_zip["url"]

                # Try different date formats
                for fmt in ["%Y%m%d", "%Y-%m-%d", "%Y_%m_%d"]:
                    ymd = d.strftime(fmt)
                    for pattern in [f"formd-{ymd}.zip", f"formd_{ymd}.zip"]:
                        attempt_count += 1
                        if attempt_count > max_attempts:
                            logger.error(f"Reached maximum attempts ({max_attempts}). Stopping search.")
                            logger.info("No Form D ZIP found within attempt limit. Creating placeholder...")
                            self.create_placeholder_data(today)
                            raise RuntimeError(f"No Form D ZIP found within {max_attempts} attempts")

                        url = f"{base}/{pattern}"
                        logger.info(f"Attempt {attempt_count}/{max_attempts}: Probing {url}")
                        if self.probe(url):
                            logger.info(f"SUCCESS! Found ZIP at {url}")
                            return d, url
                        time.sleep(5)  # respect crawl-delay

        # 4) If all else fails, create a placeholder
        logger.warning("No Form D ZIP found in last 6 months. Creating placeholder...")
        self.create_placeholder_data(today)
        raise RuntimeError("No Form D ZIP found in last 6 months")

    def create_placeholder_data(self, date_obj: datetime.date) -> Path:
        """
        Create placeholder Form D data for development and testing.

        Args:
            date_obj: Date to use for the placeholder

        Returns:
            Path to the created placeholder file
        """
        placeholder_dir = Path(f"data/raw/{date_obj:%Y%m%d}")
        placeholder_dir.mkdir(parents=True, exist_ok=True)
        placeholder_file = placeholder_dir / f"formd_{date_obj:%Y%m%d}.jsonl"

        # Create sample filings with realistic data
        sample_filings = []
        industries = ["Technology", "Healthcare", "Financial Services", "Consumer Goods", "Energy"]

        for i in range(10):
            accession_number = f"0001{i:09d}-23-{i:06d}"
            sample_filing = {
                "accessionNumber": accession_number,
                "issuerName": f"Example Corp {i+1}",
                "filingDate": date_obj.isoformat(),
                "offeringAmount": str(1000000 * (i+1)),
                "industryGroup": industries[i % len(industries)],
                "summary": f"This is a placeholder filing {i+1} for development purposes.",
                "issuerCity": "San Francisco",
                "issuerState": "CA",
                "issuerZipCode": "94105",
                "offeringType": "Equity",
                "minimumInvestmentAccepted": str(50000 * (i+1)),
                "totalAmountSold": str(800000 * (i+1)),
                "totalRemaining": str(200000 * (i+1))
            }
            sample_filings.append(json.dumps(sample_filing))

            # Add to database
            json_obj = json.loads(sample_filings[-1])

            # Create a placeholder ZIP entry
            zip_filename = f"formd-{date_obj:%Y%m%d}.zip"
            zip_url = f"placeholder://{zip_filename}"

            # Add to database
            zip_id = self.cache_manager.cache_zip_file(
                filename=zip_filename,
                url=zip_url,
                date_str=date_obj.strftime("%Y%m%d"),
                file_path=placeholder_dir / zip_filename,
                is_placeholder=True
            )

            # Add filing to database
            self.db_manager.add_form_d_filing(
                accession_number=accession_number,
                issuer_name=json_obj["issuerName"],
                filing_date=json_obj["filingDate"],
                json_data=json_obj,
                source_zip_id=zip_id,
                source_file=placeholder_file.name
            )

        placeholder_file.write_text("\n".join(sample_filings))
        logger.info(f"Created placeholder file at {placeholder_file}")
        return placeholder_file

    def download_and_extract(self, date_obj: Optional[datetime.date] = None,
                           url: Optional[str] = None,
                           max_attempts: int = 30) -> Optional[Path]:
        """
        Download and extract a Form D ZIP file.

        Args:
            date_obj: Date to download (if None, uses today)
            url: URL to download from (if None, finds the latest)
            max_attempts: Maximum number of attempts to find a ZIP

        Returns:
            Path to the created JSONL file or None if failed
        """
        # Use today if no date provided
        if date_obj is None:
            date_obj = datetime.now().date()

        # Determine URL
        if url:
            target_date, target_url = date_obj, url
        else:
            # Check if we already have this date in the database
            cached_zip = self.cache_manager.get_cached_zip(date_obj.strftime("%Y%m%d"))
            if cached_zip and cached_zip["status"] in ["downloaded", "extracted", "processed"]:
                logger.info(f"Using cached ZIP for {date_obj}: {cached_zip['filename']}")
                target_date = datetime.strptime(cached_zip["date_str"], "%Y%m%d").date()
                target_url = cached_zip["url"]

                # If it's a placeholder, just return the JSONL path
                if cached_zip["is_placeholder"]:
                    jsonl_path = self.data_dir / "raw" / cached_zip["date_str"] / f"formd_{target_date:%Y%m%d}.jsonl"
                    if jsonl_path.exists():
                        return jsonl_path
            else:
                # Try all base URLs with the date
                found = False
                for base in BASE_URLS:
                    ymd = date_obj.strftime("%Y%m%d")
                    target_url = f"{base}/formd-{ymd}.zip"
                    if self.probe(target_url):
                        target_date = date_obj
                        found = True
                        break

                if not found:
                    try:
                        # Try to find the latest available
                        target_date, target_url = self.find_latest(max_attempts=max_attempts)
                    except RuntimeError:
                        # If find_latest fails, create placeholder data
                        placeholder_path = self.create_placeholder_data(date_obj)
                        return placeholder_path

        # Prepare directories and paths
        date_str = target_date.strftime("%Y%m%d")
        raw_dir = self.data_dir / "raw" / date_str
        raw_dir.mkdir(parents=True, exist_ok=True)

        filename = f"formd-{date_str}.zip"
        zip_path = raw_dir / filename

        # Check if we already have this ZIP file in the database
        existing_zip = self.db_manager.get_zip_file(filename=filename)
        if existing_zip and existing_zip["status"] in ["downloaded", "extracted", "processed"]:
            logger.info(f"ZIP file {filename} already exists in database with status {existing_zip['status']}")

            # If the ZIP file exists on disk, we can skip the download
            if zip_path.exists():
                logger.info(f"ZIP file {zip_path} already exists on disk")

                # If it's already been extracted, just return the JSONL path
                jsonl_path = raw_dir / f"formd_{date_str}.jsonl"
                if jsonl_path.exists() and existing_zip["status"] in ["extracted", "processed"]:
                    logger.info(f"JSONL file {jsonl_path} already exists")
                    return jsonl_path

        # Download
        logger.info(f"Downloading {target_url} …")
        try:
            r = requests.get(target_url, headers=HEADERS, timeout=120)
            if r.status_code != 200:
                logger.error(f"Download failed with status code {r.status_code}")
                try:
                    # Try to find a different URL
                    target_date, target_url = self.find_latest(max_attempts=max_attempts)
                    logger.info(f"Trying fallback URL: {target_url}")
                    r = requests.get(target_url, headers=HEADERS, timeout=120)
                    r.raise_for_status()
                except (RuntimeError, requests.RequestException) as ex:
                    logger.error(f"ERROR with fallback: {ex}")
                    # Create placeholder data as a last resort
                    placeholder_path = self.create_placeholder_data(date_obj)
                    return placeholder_path

            # Save the ZIP file
            zip_path.write_bytes(r.content)

            # Add to database
            zip_id = self.cache_manager.cache_zip_file(
                filename=filename,
                url=target_url,
                date_str=date_str,
                file_path=zip_path,
                is_placeholder=False
            )

        except requests.RequestException as ex:
            logger.error(f"ERROR downloading: {ex}")
            # Create placeholder data if download fails
            placeholder_path = self.create_placeholder_data(date_obj)
            return placeholder_path

        # Extract
        try:
            with zipfile.ZipFile(zip_path) as z:
                z.extractall(raw_dir)

            # Update ZIP status
            self.cache_manager.update_zip_status(zip_id, "extracted")

        except Exception as ex:
            logger.error(f"ERROR extracting ZIP: {ex}")
            # Update ZIP status
            self.cache_manager.update_zip_status(zip_id, "error", str(ex))
            # Create placeholder data if extraction fails
            placeholder_path = self.create_placeholder_data(date_obj)
            return placeholder_path

        # Collect and validate JSON files
        filings = []
        for file in raw_dir.rglob("*.json"):
            if not FILING_REGEX.search(file.name):
                continue
            try:
                text = file.read_text(encoding="utf-8-sig")
                obj = json.loads(text)
                # Minimal schema validation
                if not obj.get("issuerName") or not obj.get("filingDate"):
                    logger.warning(f"SKIP invalid filing {file.name}")
                    continue

                # Add to database
                accession_number = obj.get("accessionNumber", f"unknown_{len(filings)}")
                self.db_manager.add_form_d_filing(
                    accession_number=accession_number,
                    issuer_name=obj["issuerName"],
                    filing_date=obj["filingDate"],
                    json_data=obj,
                    source_zip_id=zip_id,
                    source_file=str(file.relative_to(self.data_dir))
                )

                filings.append(obj)
            except Exception as ex:
                logger.error(f"ERROR reading {file.name}: {ex}")

        if not filings:
            logger.warning(f"No valid Form-D filings found for {target_date}")
            # Create placeholder data if no valid filings are found
            placeholder_path = self.create_placeholder_data(date_obj)
            return placeholder_path

        # Create JSONL file
        jsonl_path = raw_dir / f"formd_{date_str}.jsonl"
        try:
            jsonl_path.write_text("\n".join(json.dumps(o) for o in filings))
            logger.info(f"Saved {len(filings)} filings to {jsonl_path}")

            # Update ZIP status
            self.cache_manager.update_zip_status(zip_id, "processed")

            return jsonl_path
        except Exception as ex:
            logger.error(f"ERROR writing JSONL: {ex}")
            # Update ZIP status
            self.cache_manager.update_zip_status(zip_id, "error", str(ex))
            # Create placeholder data if writing fails
            placeholder_path = self.create_placeholder_data(date_obj)
            return placeholder_path

    def process_quarterly_zip(self, year: int, quarter: int) -> Optional[Path]:
        """
        Process a quarterly Form D ZIP file.

        Args:
            year: Year (e.g., 2023)
            quarter: Quarter (1-4)

        Returns:
            Path to the created JSONL file or None if failed
        """
        # Validate inputs
        if not (2010 <= year <= datetime.now().year):
            raise ValueError(f"Invalid year: {year}")
        if not (1 <= quarter <= 4):
            raise ValueError(f"Invalid quarter: {quarter}")

        # Construct URL and paths
        url = f"https://www.sec.gov/files/dera/data/form-d/{year}q{quarter}_d.zip"
        date_str = f"{year}q{quarter}"
        raw_dir = self.data_dir / "raw" / date_str
        raw_dir.mkdir(parents=True, exist_ok=True)

        filename = f"{year}q{quarter}_d.zip"
        zip_path = raw_dir / filename

        # Check if we already have this ZIP file in the database
        existing_zip = self.db_manager.get_zip_file(filename=filename)
        if existing_zip and existing_zip["status"] in ["downloaded", "extracted", "processed"]:
            logger.info(f"ZIP file {filename} already exists in database with status {existing_zip['status']}")

            # If the ZIP file exists on disk, we can skip the download
            if zip_path.exists():
                logger.info(f"ZIP file {zip_path} already exists on disk")

                # If it's already been extracted, just return the JSONL path
                jsonl_path = raw_dir / f"formd_{date_str}.jsonl"
                if jsonl_path.exists() and existing_zip["status"] in ["extracted", "processed"]:
                    logger.info(f"JSONL file {jsonl_path} already exists")
                    return jsonl_path

        # Download
        logger.info(f"Downloading quarterly ZIP: {url}")
        try:
            r = requests.get(url, headers=HEADERS, timeout=300)  # Longer timeout for quarterly ZIPs
            r.raise_for_status()

            # Save the ZIP file
            zip_path.write_bytes(r.content)

            # Add to database
            zip_id = self.cache_manager.cache_zip_file(
                filename=filename,
                url=url,
                date_str=date_str,
                file_path=zip_path,
                is_placeholder=False
            )

        except requests.RequestException as ex:
            logger.error(f"ERROR downloading quarterly ZIP: {ex}")
            return None

        # Extract
        try:
            with zipfile.ZipFile(zip_path) as z:
                z.extractall(raw_dir)

            # Update ZIP status
            self.cache_manager.update_zip_status(zip_id, "extracted")

        except Exception as ex:
            logger.error(f"ERROR extracting quarterly ZIP: {ex}")
            # Update ZIP status
            self.cache_manager.update_zip_status(zip_id, "error", str(ex))
            return None

        # Process TSV files
        try:
            # Find the main TSV file
            tsv_files = list(raw_dir.rglob("*.tsv"))
            if not tsv_files:
                logger.error(f"No TSV files found in {raw_dir}")
                return None

            # TODO: Implement TSV processing
            # This would involve parsing the TSV files and converting to JSON/JSONL

            # For now, just create a placeholder JSONL
            jsonl_path = raw_dir / f"formd_{date_str}.jsonl"
            jsonl_path.write_text("[]")  # Empty JSONL for now

            # Update ZIP status
            self.cache_manager.update_zip_status(zip_id, "processed")

            return jsonl_path
        except Exception as ex:
            logger.error(f"ERROR processing quarterly ZIP: {ex}")
            # Update ZIP status
            self.cache_manager.update_zip_status(zip_id, "error", str(ex))
            return None

    def clean_old_files(self, older_than_days: int = 30,
                       min_access_count: int = 3,
                       dry_run: bool = False) -> Dict[str, int]:
        """
        Clean up old files based on cache policies.

        Args:
            older_than_days: Only clean files older than this many days
            min_access_count: Only clean files accessed fewer than this many times
            dry_run: If True, only return the files that would be cleaned without deleting

        Returns:
            Dictionary with counts of cleaned items by type
        """
        # Clean ZIP files
        cleaned_zips = self.cache_manager.clean_old_zip_files(
            older_than_days=older_than_days,
            min_access_count=min_access_count,
            dry_run=dry_run
        )

        # TODO: Implement cleaning for other file types

        return {
            "zip_files": len(cleaned_zips)
        }

    def enforce_size_limit(self, max_size_gb: float = 10.0) -> int:
        """
        Enforce a maximum cache size by cleaning old files.

        Args:
            max_size_gb: Maximum cache size in gigabytes

        Returns:
            Number of bytes freed
        """
        max_size_bytes = int(max_size_gb * 1024 * 1024 * 1024)
        return self.cache_manager.enforce_size_limit(max_size_bytes=max_size_bytes)


def main(date_str: Optional[str] = None,
         clean: bool = False,
         clean_days: int = 30,
         enforce_limit: bool = True,
         max_size_gb: float = 10.0):
    """
    Main function for downloading and processing Form D ZIP files.

    Args:
        date_str: Date string in YYYY-MM-DD format (if None, uses today)
        clean: Whether to clean old files
        clean_days: Days threshold for cleaning
        enforce_limit: Whether to enforce size limit
        max_size_gb: Maximum cache size in gigabytes
    """
    # Initialize database and cache managers
    db_manager = DatabaseManager()
    cache_manager = CacheManager(db_manager)

    # Create downloader
    downloader = FormDZipDownloader(db_manager, cache_manager)

    # Clean old files if requested
    if clean:
        logger.info(f"Cleaning files older than {clean_days} days...")
        cleaned = downloader.clean_old_files(older_than_days=clean_days)
        logger.info(f"Cleaned {cleaned['zip_files']} ZIP files")

    # Enforce size limit if requested
    if enforce_limit:
        logger.info(f"Enforcing size limit of {max_size_gb} GB...")
        bytes_freed = downloader.enforce_size_limit(max_size_gb=max_size_gb)
        logger.info(f"Freed {bytes_freed / (1024*1024):.2f} MB")

    # Download and process
    if date_str:
        date_obj = datetime.strptime(date_str, "%Y-%m-%d").date()
        logger.info(f"Processing Form D data for {date_str}...")
    else:
        date_obj = datetime.now().date()
        logger.info("Processing latest Form D data...")

    jsonl_path = downloader.download_and_extract(date_obj=date_obj)

    if jsonl_path and jsonl_path.exists():
        logger.info(f"Successfully processed Form D data: {jsonl_path}")
    else:
        logger.error("Failed to process Form D data")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Download and process Form D ZIP files")
    parser.add_argument("--date", type=str, help="Date in YYYY-MM-DD format (default: today)")
    parser.add_argument("--clean", action="store_true", help="Clean old files")
    parser.add_argument("--clean-days", type=int, default=30, help="Days threshold for cleaning")
    parser.add_argument("--no-enforce-limit", action="store_true", help="Don't enforce size limit")
    parser.add_argument("--max-size", type=float, default=10.0, help="Maximum cache size in GB")

    args = parser.parse_args()

    main(
        date_str=args.date,
        clean=args.clean,
        clean_days=args.clean_days,
        enforce_limit=not args.no_enforce_limit,
        max_size_gb=args.max_size
    )