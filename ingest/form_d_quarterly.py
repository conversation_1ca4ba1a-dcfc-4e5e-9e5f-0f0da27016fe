# ingest/form_d_quarterly.py
"""
Scrape SEC’s quarterly Form-D index and download the latest bundle,
then validate and combine only actual filings.
"""

import requests
import zipfile
import json
from bs4 import BeautifulSoup
from pathlib import Path
import datetime as dt
import re

BASE_PAGE = "https://www.sec.gov/data-research/sec-markets-data/form-d-data-sets"
HEADERS   = {"User-Agent": "PrivateSignals/0.1 (<EMAIL>)"}
FILING_REGEX = re.compile(r"formD?[-_]?\d+\.json$", re.IGNORECASE)

def find_latest_quarter_zip():
    resp = requests.get(BASE_PAGE, headers=HEADERS, timeout=30)
    resp.raise_for_status()
    soup = BeautifulSoup(resp.text, "html.parser")
    row = soup.select_one("table tr:nth-of-type(1) a")
    label = row.text.strip()                    # e.g. "2025 Q1"
    url   = "https://www.sec.gov" + row["href"]
    return label, url

def download_and_extract_quarterly():
    label, url = find_latest_quarter_zip()
    key   = label.replace(" ", "").lower()      # e.g. "2025q1"
    dirp  = Path(f"data/raw/{key}")
    dirp.mkdir(parents=True, exist_ok=True)
    zp    = dirp / f"{key}.zip"

    print(f"[qtr] Downloading {label} archive…")
    data = requests.get(url, headers=HEADERS, timeout=120).content
    zp.write_bytes(data)

    try:
        with zipfile.ZipFile(zp) as z:
            z.extractall(dirp)
    except Exception as e:
        print(f"[qtr] ERROR extracting ZIP: {e}")
        return

    # validate and combine actual filings
    filings = []
    for file in dirp.rglob("*.json"):
        if not FILING_REGEX.search(file.name):
            continue
        try:
            obj = json.loads(file.read_text(encoding="utf-8-sig"))
            if not obj.get("issuerName") or not obj.get("filingDate"):
                continue
            filings.append(obj)
        except Exception as e:
            print(f"[qtr] ERROR parsing {file.name}: {e}")

    if not filings:
        print(f"[qtr] No valid filings in {label}")
        return

    out = dirp / f"formd_{key}.jsonl"
    out.write_text("\n".join(json.dumps(o) for o in filings))
    print(f"[qtr] ✓ Extracted {len(filings)} filings into {out}")

if __name__ == "__main__":
    download_and_extract_quarterly()
