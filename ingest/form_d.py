"""
Grab the most recent SEC Form-D bulk file.
Strategy:
 1. Try the evergreen URL `current_formd.zip` (usually the latest).
 2. If that fails, step back in 7-day chunks to avoid 429 throttling.
 3. Stop after 26 steps (~6 months).
"""

import datetime as dt, time, zipfile, json, requests
from pathlib import Path

HEADERS = {
    # add contact info per SEC guidance
    "User-Agent": "PrivateSignals/0.1 (<EMAIL>)"
}

BASE = "https://www.sec.gov/files/dera/data/form-d"

def probe(url: str) -> bool:
    """Cheap existence check without downloading the whole file."""
    r = requests.get(url, headers=HEADERS, stream=True, timeout=15)
    # 429 = rate-limited; give SEC a breather and treat as miss
    if r.status_code == 429:
        time.sleep(1)
        return False
    return r.status_code == 200

def find_latest_formd():
    # 1. Evergreen "current" file
    url = f"{BASE}/current_formd.zip"
    if probe(url):
        return dt.date.today(), url

    # 2. Step back in 7-day chunks to stay within rate limits
    for weeks in range(0, 26):               # ~ six months
        d = dt.date.today() - dt.timedelta(days=weeks * 7)
        ymd = d.strftime("%Y%m%d")
        url = f"{BASE}/formd-{ymd}.zip"
        if probe(url):
            return d, url

    raise RuntimeError("No Form D bulk file found in last 6 months")

def download_and_extract(d, url):
    ymd = d.strftime("%Y%m%d")
    raw_dir = Path(f"data/raw/{ymd}")
    raw_dir.mkdir(parents=True, exist_ok=True)
    zip_path = raw_dir / f"formd-{ymd}.zip"

    print(f"Downloading {url} …")
    zbytes = requests.get(url, headers=HEADERS, timeout=60)
    zbytes.raise_for_status()
    zip_path.write_bytes(zbytes.content)

    with zipfile.ZipFile(zip_path) as z:
        z.extractall(raw_dir)

    files = list(raw_dir.glob("**/*.json"))
    jsonl = raw_dir / f"formd_{ymd}.jsonl"
    jsonl.write_text("\n".join(f.read_text() for f in files))
    print(f"✓ Saved {len(files)} Form-D filings to {jsonl}")

if __name__ == "__main__":
    date_obj, url = find_latest_formd()
    download_and_extract(date_obj, url)
