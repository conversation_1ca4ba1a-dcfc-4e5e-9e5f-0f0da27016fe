#!/usr/bin/env python3
"""
SEC API Client

Provides standardized access to SEC APIs following best practices:
1. Proper User-Agent headers
2. Rate limiting (max 10 requests per second)
3. Response caching
4. Error handling

Supported APIs:
- Submissions API (CIK lookup)
- XBRL API (financial data)
- Company Facts API
"""

import os
import json
import time
import logging
import requests
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
import re

# Configure logging
logger = logging.getLogger("sec_api")
logger.setLevel(logging.INFO)
if not logger.handlers:
    # Create file handler
    fh = logging.FileHandler("logs/sec_api.log")
    fh.setLevel(logging.INFO)
    # Create console handler
    ch = logging.StreamHandler()
    ch.setLevel(logging.INFO)
    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    fh.setFormatter(formatter)
    ch.setFormatter(formatter)
    # Add handlers to logger
    logger.addHandler(fh)
    logger.addHandler(ch)

# Configure proper headers per SEC guidelines
HEADERS = {
    "User-Agent": "PrivateSignals/0.2 (<EMAIL>)",
    "Accept-Encoding": "identity",
    "Accept": "application/json",
}

# Base URLs for SEC APIs
SUBMISSIONS_API_URL = "https://data.sec.gov/submissions/CIK{cik}.json"
COMPANY_FACTS_API_URL = "https://data.sec.gov/api/xbrl/companyfacts/CIK{cik}.json"
COMPANY_CONCEPT_API_URL = "https://data.sec.gov/api/xbrl/companyconcept/CIK{cik}/{taxonomy}/{tag}.json"

# Cache directory
CACHE_DIR = Path("data/cache/sec_api")
CACHE_EXPIRY = {
    "submissions": 24,  # hours
    "company_facts": 24,  # hours
    "company_concept": 24,  # hours
}

# Rate limiting
RATE_LIMIT = 10  # requests per second
last_request_time = 0

def _normalize_cik(cik: Union[str, int]) -> str:
    """
    Normalize CIK to 10-digit format with leading zeros.
    
    Args:
        cik: CIK as string or integer
        
    Returns:
        Normalized 10-digit CIK string
    """
    cik_str = str(cik).strip()
    # Remove leading 'CIK' if present
    if cik_str.upper().startswith('CIK'):
        cik_str = cik_str[3:]
    # Remove any non-numeric characters
    cik_str = re.sub(r'[^0-9]', '', cik_str)
    # Pad with leading zeros to 10 digits
    return cik_str.zfill(10)

def _enforce_rate_limit():
    """
    Enforce rate limiting for SEC API requests.
    Ensures no more than 10 requests per second.
    """
    global last_request_time
    current_time = time.time()
    time_since_last_request = current_time - last_request_time
    
    # If less than 0.1 seconds since last request, sleep
    if time_since_last_request < 0.1:
        sleep_time = 0.1 - time_since_last_request
        logger.debug(f"Rate limiting: sleeping for {sleep_time:.3f} seconds")
        time.sleep(sleep_time)
    
    last_request_time = time.time()

def _get_cache_path(api_type: str, cik: str, **kwargs) -> Path:
    """
    Get the cache file path for a specific API request.
    
    Args:
        api_type: Type of API ('submissions', 'company_facts', etc.)
        cik: Company CIK
        **kwargs: Additional parameters for the cache key
        
    Returns:
        Path to cache file
    """
    # Create cache directory if it doesn't exist
    cache_dir = CACHE_DIR / api_type
    cache_dir.mkdir(parents=True, exist_ok=True)
    
    # Create cache key
    cache_key = f"CIK{cik}"
    if kwargs:
        # Add additional parameters to cache key
        param_str = "_".join(f"{k}_{v}" for k, v in sorted(kwargs.items()))
        cache_key += f"_{param_str}"
    
    return cache_dir / f"{cache_key}.json"

def _is_cache_valid(cache_path: Path, api_type: str) -> bool:
    """
    Check if a cache file is valid (exists and not expired).
    
    Args:
        cache_path: Path to cache file
        api_type: Type of API to determine expiry time
        
    Returns:
        True if cache is valid, False otherwise
    """
    if not cache_path.exists():
        return False
    
    # Check if cache is expired
    expiry_hours = CACHE_EXPIRY.get(api_type, 24)
    cache_time = datetime.fromtimestamp(cache_path.stat().st_mtime)
    expiry_time = datetime.now() - timedelta(hours=expiry_hours)
    
    return cache_time > expiry_time

def _get_from_cache(cache_path: Path) -> Optional[Dict[str, Any]]:
    """
    Get data from cache file.
    
    Args:
        cache_path: Path to cache file
        
    Returns:
        Cached data or None if cache is invalid or doesn't exist
    """
    try:
        if cache_path.exists():
            with open(cache_path, 'r') as f:
                return json.load(f)
    except Exception as e:
        logger.warning(f"Error reading cache file {cache_path}: {e}")
    
    return None

def _save_to_cache(cache_path: Path, data: Dict[str, Any]):
    """
    Save data to cache file.
    
    Args:
        cache_path: Path to cache file
        data: Data to cache
    """
    try:
        with open(cache_path, 'w') as f:
            json.dump(data, f, indent=2)
        logger.debug(f"Saved data to cache: {cache_path}")
    except Exception as e:
        logger.warning(f"Error saving to cache file {cache_path}: {e}")

def get_submissions(cik: Union[str, int], use_cache: bool = True) -> Optional[Dict[str, Any]]:
    """
    Get company submissions from SEC Submissions API.
    
    Args:
        cik: Company CIK
        use_cache: Whether to use cached data if available
        
    Returns:
        Submissions data or None on error
    """
    # Normalize CIK
    normalized_cik = _normalize_cik(cik)
    
    # Check cache
    cache_path = _get_cache_path("submissions", normalized_cik)
    if use_cache and _is_cache_valid(cache_path, "submissions"):
        logger.info(f"Using cached submissions data for CIK{normalized_cik}")
        return _get_from_cache(cache_path)
    
    # Enforce rate limit
    _enforce_rate_limit()
    
    # Make API request
    url = SUBMISSIONS_API_URL.format(cik=normalized_cik)
    logger.info(f"Fetching submissions data from {url}")
    
    try:
        response = requests.get(url, headers=HEADERS, timeout=30)
        response.raise_for_status()
        
        # Parse response
        data = response.json()
        
        # Cache response
        _save_to_cache(cache_path, data)
        
        return data
    
    except requests.exceptions.HTTPError as e:
        if e.response.status_code == 429:
            logger.warning(f"Rate limited by SEC (429). Implement longer backoff.")
            time.sleep(5)  # Longer backoff for rate limiting
        logger.error(f"HTTP error fetching submissions: {e}")
    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching submissions: {e}")
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing submissions response: {e}")
    except Exception as e:
        logger.error(f"Unexpected error fetching submissions: {e}")
    
    # Return cached data as fallback if available
    if cache_path.exists():
        logger.warning(f"Using stale cache as fallback for CIK{normalized_cik}")
        return _get_from_cache(cache_path)
    
    return None

def get_company_facts(cik: Union[str, int], use_cache: bool = True) -> Optional[Dict[str, Any]]:
    """
    Get company facts from SEC Company Facts API.
    
    Args:
        cik: Company CIK
        use_cache: Whether to use cached data if available
        
    Returns:
        Company facts data or None on error
    """
    # Normalize CIK
    normalized_cik = _normalize_cik(cik)
    
    # Check cache
    cache_path = _get_cache_path("company_facts", normalized_cik)
    if use_cache and _is_cache_valid(cache_path, "company_facts"):
        logger.info(f"Using cached company facts data for CIK{normalized_cik}")
        return _get_from_cache(cache_path)
    
    # Enforce rate limit
    _enforce_rate_limit()
    
    # Make API request
    url = COMPANY_FACTS_API_URL.format(cik=normalized_cik)
    logger.info(f"Fetching company facts data from {url}")
    
    try:
        response = requests.get(url, headers=HEADERS, timeout=30)
        response.raise_for_status()
        
        # Parse response
        data = response.json()
        
        # Cache response
        _save_to_cache(cache_path, data)
        
        return data
    
    except requests.exceptions.HTTPError as e:
        if e.response.status_code == 429:
            logger.warning(f"Rate limited by SEC (429). Implement longer backoff.")
            time.sleep(5)  # Longer backoff for rate limiting
        logger.error(f"HTTP error fetching company facts: {e}")
    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching company facts: {e}")
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing company facts response: {e}")
    except Exception as e:
        logger.error(f"Unexpected error fetching company facts: {e}")
    
    # Return cached data as fallback if available
    if cache_path.exists():
        logger.warning(f"Using stale cache as fallback for CIK{normalized_cik}")
        return _get_from_cache(cache_path)
    
    return None

def extract_cik_from_filing(filing_data: Dict[str, Any]) -> Optional[str]:
    """
    Extract CIK from a Form D filing.
    
    Args:
        filing_data: Filing data from ATOM feed or other source
        
    Returns:
        Normalized CIK or None if not found
    """
    # Try different methods to extract CIK
    
    # Method 1: From ID field
    if "id" in filing_data:
        id_field = filing_data["id"]
        # Look for pattern like "0001234567" in the ID
        cik_match = re.search(r'\/(\d{10})\/|data\/(\d+)', id_field)
        if cik_match:
            cik = cik_match.group(1) or cik_match.group(2)
            return _normalize_cik(cik)
    
    # Method 2: From title field
    if "title" in filing_data:
        title_field = filing_data["title"]
        # Look for pattern like "(0001234567)" in the title
        cik_match = re.search(r'\((\d+)\)', title_field)
        if cik_match:
            return _normalize_cik(cik_match.group(1))
    
    # Method 3: From link field
    if "link" in filing_data:
        link_field = filing_data["link"]
        # Look for pattern like "/data/1234567/" in the link
        cik_match = re.search(r'\/data\/(\d+)\/', link_field)
        if cik_match:
            return _normalize_cik(cik_match.group(1))
    
    return None

def enrich_filing_with_company_data(filing_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Enrich a Form D filing with company data from SEC APIs.
    
    Args:
        filing_data: Filing data from ATOM feed or other source
        
    Returns:
        Enriched filing data
    """
    # Create a copy of the filing data to enrich
    enriched_data = filing_data.copy()
    
    # Extract CIK
    cik = extract_cik_from_filing(filing_data)
    if not cik:
        logger.warning(f"Could not extract CIK from filing: {filing_data.get('id', 'unknown')}")
        enriched_data["sec_api_enriched"] = False
        return enriched_data
    
    # Add CIK to enriched data
    enriched_data["cik"] = cik
    
    # Get submissions data
    submissions_data = get_submissions(cik)
    if submissions_data:
        # Extract relevant information
        company_info = {
            "name": submissions_data.get("name", ""),
            "sic": submissions_data.get("sic", ""),
            "sic_description": submissions_data.get("sicDescription", ""),
            "state_of_incorporation": submissions_data.get("stateOfIncorporation", ""),
            "fiscal_year_end": submissions_data.get("fiscalYearEnd", ""),
            "filing_count": len(submissions_data.get("filings", {}).get("recent", {}).get("filingDate", [])),
        }
        
        # Add to enriched data
        enriched_data["company_info"] = company_info
        enriched_data["sec_api_enriched"] = True
    else:
        enriched_data["sec_api_enriched"] = False
    
    return enriched_data

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="SEC API Client")
    parser.add_argument("--cik", type=str, required=True, help="Company CIK")
    parser.add_argument("--no-cache", action="store_true", help="Bypass cache")
    parser.add_argument("--type", choices=["submissions", "facts"], default="submissions", 
                       help="API type to query")
    
    args = parser.parse_args()
    
    # Set up console logging
    console = logging.StreamHandler()
    console.setLevel(logging.INFO)
    formatter = logging.Formatter('%(levelname)s: %(message)s')
    console.setFormatter(formatter)
    logger.addHandler(console)
    
    # Fetch data
    if args.type == "submissions":
        data = get_submissions(args.cik, use_cache=not args.no_cache)
    elif args.type == "facts":
        data = get_company_facts(args.cik, use_cache=not args.no_cache)
    
    if data:
        print(json.dumps(data, indent=2))
    else:
        print("No data returned")
