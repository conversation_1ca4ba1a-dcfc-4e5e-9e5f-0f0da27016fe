#!/usr/bin/env python3
"""
Enhanced Prompt Manager with Financial RAG Integration

Extends the existing prompt manager with financial expertise through
RAG-enhanced prompts and professional analyst personas.
"""

import json
import logging
from typing import Dict, List, Any, Optional
from mcp.prompt_manager import PromptManager
from mcp.financial_rag_system import FinancialRAGSystem
from mcp.financial_personas import FinancialPersonas
from mcp.analysis_templates import AnalysisTemplates

class EnhancedPromptManager(PromptManager):
    """
    Enhanced prompt manager with financial RAG integration and analyst personas.
    """
    
    def __init__(self, enable_rag: bool = True):
        """Initialize the enhanced prompt manager."""
        super().__init__()
        
        self.enable_rag = enable_rag
        
        # Initialize RAG system if enabled
        if self.enable_rag:
            try:
                self.rag_system = FinancialRAGSystem()
                logging.info("Financial RAG system initialized successfully")
            except Exception as e:
                logging.warning(f"Could not initialize RAG system: {e}")
                self.rag_system = None
                self.enable_rag = False
        else:
            self.rag_system = None
        
        # Initialize financial personas and templates
        self.financial_personas = FinancialPersonas()
        self.analysis_templates = AnalysisTemplates()

        # Legacy personas for backward compatibility
        self.analyst_personas = self._load_analyst_personas()

        # Enhanced analysis templates
        self.enhanced_templates = self._load_enhanced_templates()
    
    def _load_analyst_personas(self) -> Dict[str, Dict[str, Any]]:
        """Load different financial analyst personas."""
        return {
            "growth_investor": {
                "name": "Growth Equity Investor",
                "description": "Focuses on scalable business models with high growth potential",
                "expertise": [
                    "Revenue growth analysis",
                    "Market opportunity assessment",
                    "Scalability evaluation",
                    "Unit economics analysis"
                ],
                "key_metrics": [
                    "Revenue growth rate",
                    "Total Addressable Market (TAM)",
                    "Customer acquisition metrics",
                    "Gross margin trends",
                    "Market penetration potential"
                ],
                "investment_criteria": [
                    "Large addressable market (>$1B)",
                    "Strong revenue growth (>50% annually)",
                    "Scalable business model",
                    "Experienced management team",
                    "Clear competitive advantages"
                ],
                "risk_focus": [
                    "Market saturation risk",
                    "Competitive threats",
                    "Execution risk",
                    "Capital efficiency"
                ]
            },
            "value_investor": {
                "name": "Value-Oriented Investor",
                "description": "Focuses on financial fundamentals and asset values",
                "expertise": [
                    "Financial statement analysis",
                    "Asset valuation",
                    "Cash flow analysis",
                    "Risk assessment"
                ],
                "key_metrics": [
                    "Price-to-book ratio",
                    "Debt-to-equity ratio",
                    "Free cash flow",
                    "Return on invested capital",
                    "Asset utilization"
                ],
                "investment_criteria": [
                    "Strong balance sheet",
                    "Consistent cash flow generation",
                    "Reasonable valuation",
                    "Sustainable competitive position",
                    "Experienced management"
                ],
                "risk_focus": [
                    "Financial leverage",
                    "Cash flow volatility",
                    "Asset quality",
                    "Market cyclicality"
                ]
            },
            "sector_specialist": {
                "name": "Industry Sector Specialist",
                "description": "Deep expertise in specific industry verticals",
                "expertise": [
                    "Industry dynamics",
                    "Regulatory environment",
                    "Competitive landscape",
                    "Technology trends"
                ],
                "key_metrics": [
                    "Industry-specific KPIs",
                    "Market share analysis",
                    "Regulatory compliance",
                    "Technology adoption"
                ],
                "investment_criteria": [
                    "Industry leadership position",
                    "Regulatory compliance",
                    "Technology differentiation",
                    "Market timing",
                    "Sector growth trends"
                ],
                "risk_focus": [
                    "Regulatory changes",
                    "Technology disruption",
                    "Competitive dynamics",
                    "Cyclical trends"
                ]
            },
            "venture_capitalist": {
                "name": "Venture Capital Investor",
                "description": "Focuses on early-stage, high-growth potential companies",
                "expertise": [
                    "Early-stage evaluation",
                    "Team assessment",
                    "Product-market fit",
                    "Scalability analysis"
                ],
                "key_metrics": [
                    "Total addressable market",
                    "Product-market fit indicators",
                    "Team experience",
                    "Technology differentiation",
                    "Capital efficiency"
                ],
                "investment_criteria": [
                    "Large market opportunity",
                    "Strong founding team",
                    "Unique technology or approach",
                    "Early traction indicators",
                    "Scalable business model"
                ],
                "risk_focus": [
                    "Execution risk",
                    "Market timing",
                    "Technology risk",
                    "Team risk",
                    "Funding risk"
                ]
            }
        }
    
    def _load_enhanced_templates(self) -> Dict[str, str]:
        """Load enhanced analysis templates."""
        return {
            "comprehensive_analysis": """
You are a {persona_name} with deep expertise in {persona_expertise}.

Your investment philosophy focuses on {investment_criteria}.
You typically analyze {key_metrics} and pay special attention to {risk_focus}.

# FILING TO ANALYZE
{filing_info}

# FINANCIAL ANALYSIS CONTEXT
{financial_context}

# MARKET INTELLIGENCE
{market_context}

# ANALYSIS REQUIREMENTS
Provide a comprehensive analysis following this structure:

## 1. EXECUTIVE SUMMARY
- One-paragraph investment thesis
- Key recommendation (STRONG BUY/BUY/HOLD/PASS)
- Confidence level (1-10)

## 2. INVESTMENT THESIS
- Core value proposition
- Market opportunity assessment
- Competitive positioning
- Growth potential analysis

## 3. FINANCIAL ANALYSIS
- Valuation assessment using appropriate methodologies
- Key financial metrics evaluation
- Revenue and profitability projections
- Capital requirements analysis

## 4. RISK ASSESSMENT
- Top 3 investment risks with probability and impact
- Risk mitigation strategies
- Scenario analysis (bull/base/bear cases)

## 5. MARKET ANALYSIS
- Total addressable market (TAM) sizing
- Competitive landscape evaluation
- Market timing assessment
- Industry trends and dynamics

## 6. MANAGEMENT & EXECUTION
- Team assessment and track record
- Execution capabilities
- Strategic vision alignment
- Operational readiness

## 7. RECOMMENDATION
- Investment recommendation with rationale
- Suggested investment amount and terms
- Key milestones to monitor
- Exit strategy considerations

Use specific financial metrics, industry benchmarks, and quantitative analysis to support your conclusions.
""",
            "quick_screening": """
You are a {persona_name} performing rapid screening analysis.

# FILING INFORMATION
{filing_info}

# SCREENING CRITERIA
{screening_criteria}

Provide a quick assessment:

## SCREENING RESULT
- PASS/FAIL for further analysis
- Relevance score (0.0-1.0)
- Key reasons for decision

## KEY HIGHLIGHTS
- Most attractive aspects
- Primary concerns
- Critical questions for deeper analysis

## NEXT STEPS
- Recommended follow-up actions
- Information gaps to address
- Timeline for decision

Keep analysis concise but substantive.
""",
            "risk_focused": """
You are a senior risk analyst with expertise in {risk_expertise}.

# FILING TO ANALYZE
{filing_info}

# RISK ASSESSMENT FRAMEWORK
{risk_framework}

Conduct a comprehensive risk analysis:

## RISK IDENTIFICATION
- Technology/Product risks
- Market/Competitive risks
- Financial/Operational risks
- Regulatory/Legal risks
- Management/Team risks

## RISK QUANTIFICATION
- Probability assessment (High/Medium/Low)
- Impact assessment (High/Medium/Low)
- Risk severity matrix
- Quantitative impact estimates where possible

## RISK MITIGATION
- Existing mitigation strategies
- Recommended additional measures
- Monitoring and early warning indicators
- Contingency planning requirements

## OVERALL RISK PROFILE
- Risk-adjusted return expectations
- Risk tolerance requirements
- Portfolio fit assessment
- Risk monitoring framework

Focus on actionable risk insights with specific mitigation recommendations.
"""
        }
    
    def select_analyst_persona(self, filing_data: Dict[str, Any]) -> str:
        """Select the most appropriate analyst persona based on filing characteristics."""

        # Use the comprehensive persona selection from FinancialPersonas
        return self.financial_personas.select_best_persona(filing_data)
    
    def create_enhanced_analysis_prompt(self, 
                                      filing_data: Dict[str, Any],
                                      analysis_type: str = "comprehensive_analysis",
                                      persona: str = None) -> str:
        """Create enhanced analysis prompt with RAG integration and persona."""
        
        # Select persona if not specified
        if not persona:
            persona = self.select_analyst_persona(filing_data)
        
        # Get persona from new comprehensive system
        persona_obj = self.financial_personas.get_persona(persona)
        if not persona_obj:
            # Fall back to legacy system
            persona_info = self.analyst_personas.get(persona, self.analyst_personas["growth_investor"])
        else:
            # Convert new persona object to legacy format for compatibility
            persona_info = {
                "name": persona_obj.name,
                "expertise": persona_obj.expertise,
                "investment_criteria": persona_obj.investment_criteria,
                "key_metrics": persona_obj.key_metrics,
                "risk_focus": persona_obj.risk_focus
            }
        
        # Format filing information
        filing_info = self._format_filing_info(filing_data)
        
        # Get financial context from RAG system
        financial_context = ""
        market_context = ""
        
        if self.enable_rag and self.rag_system:
            try:
                # Get RAG-enhanced context
                rag_context = self.rag_system.retrieve_relevant_context(filing_data)
                
                # Generate financial frameworks context
                financial_context = self.rag_system.knowledge_base.generate_analysis_context(filing_data)
                
                # Format market context
                market_context = self.rag_system._format_market_context(
                    rag_context.get("market_context", {})
                )
                
                # Add similar filings context
                market_context += "\n" + self.rag_system._format_similar_filings(
                    rag_context.get("similar_filings", [])
                )
                
            except Exception as e:
                logging.error(f"Error retrieving RAG context: {e}")
                financial_context = "Financial context not available due to system error."
                market_context = "Market context not available due to system error."
        else:
            financial_context = "RAG system not available - using basic analysis."
            market_context = "Market context not available."
        
        # Get template
        template = self.enhanced_templates.get(analysis_type, self.enhanced_templates["comprehensive_analysis"])
        
        # Format prompt with all context
        enhanced_prompt = template.format(
            persona_name=persona_info["name"],
            persona_expertise=", ".join(persona_info["expertise"]),
            investment_criteria=", ".join(persona_info["investment_criteria"]),
            key_metrics=", ".join(persona_info["key_metrics"]),
            risk_focus=", ".join(persona_info["risk_focus"]),
            filing_info=filing_info,
            financial_context=financial_context,
            market_context=market_context,
            screening_criteria=", ".join(persona_info["investment_criteria"]),
            risk_expertise=", ".join(persona_info["risk_focus"]),
            risk_framework=financial_context
        )
        
        return enhanced_prompt
    
    def _format_filing_info(self, filing_data: Dict[str, Any]) -> str:
        """Format filing information for prompt inclusion."""
        info_parts = []
        
        if filing_data.get("issuer_name"):
            info_parts.append(f"Company: {filing_data['issuer_name']}")
        
        if filing_data.get("industry_group"):
            info_parts.append(f"Industry: {filing_data['industry_group']}")
        
        if filing_data.get("offering_amount"):
            amount = filing_data["offering_amount"]
            info_parts.append(f"Offering Amount: ${amount:,.0f}")
        
        if filing_data.get("filing_date"):
            info_parts.append(f"Filing Date: {filing_data['filing_date']}")
        
        if filing_data.get("issuer_city") and filing_data.get("issuer_state"):
            info_parts.append(f"Location: {filing_data['issuer_city']}, {filing_data['issuer_state']}")
        
        if filing_data.get("title"):
            info_parts.append(f"Title: {filing_data['title']}")
        
        if filing_data.get("summary"):
            info_parts.append(f"Summary: {filing_data['summary']}")
        
        return "\n".join(info_parts)
    
    def parse_enhanced_output(self, llm_output: str) -> Dict[str, Any]:
        """Parse enhanced LLM output into structured format."""
        
        # Start with base parsing
        base_result = super().parse_output(llm_output)
        
        # Enhanced parsing for additional fields
        enhanced_result = base_result.copy()
        
        # Extract investment thesis
        thesis_match = self._extract_section(llm_output, "INVESTMENT THESIS")
        if thesis_match:
            enhanced_result["investment_thesis"] = thesis_match
        
        # Extract financial analysis
        financial_match = self._extract_section(llm_output, "FINANCIAL ANALYSIS")
        if financial_match:
            enhanced_result["financial_analysis"] = financial_match
        
        # Extract risk assessment
        risk_match = self._extract_section(llm_output, "RISK ASSESSMENT")
        if risk_match:
            enhanced_result["risk_assessment"] = risk_match
        
        # Extract market analysis
        market_match = self._extract_section(llm_output, "MARKET ANALYSIS")
        if market_match:
            enhanced_result["market_analysis"] = market_match
        
        # Extract recommendation
        recommendation_match = self._extract_section(llm_output, "RECOMMENDATION")
        if recommendation_match:
            enhanced_result["recommendation"] = recommendation_match
        
        return enhanced_result
    
    def _extract_section(self, text: str, section_name: str) -> Optional[str]:
        """Extract a specific section from the LLM output."""
        import re
        
        # Look for section headers
        pattern = rf"##\s*\d*\.?\s*{section_name}(.*?)(?=##|\Z)"
        match = re.search(pattern, text, re.DOTALL | re.IGNORECASE)
        
        if match:
            return match.group(1).strip()
        
        return None
