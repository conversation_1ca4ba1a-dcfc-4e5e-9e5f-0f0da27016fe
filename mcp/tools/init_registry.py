#!/usr/bin/env python3
"""
Tool Registry Initializer

Initializes the MCP tool registry:
1. Registers all available tools
2. Configures tool dependencies
3. Validates tool configurations
4. Provides a central access point for tools
"""

import os
import sys
import logging
from typing import Dict, List, Any, Optional

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from mcp.tools.registry import registry, Tool
from mcp.tools.news_scraper import NewsScraperTool
from mcp.tools.json_server import JSONMCPServerTool
from mcp.tools.github_server import GitHubMCPServerTool
from mcp.tools.postgresql_server import PostgreSQLMCPServerTool
from mcp.tools.microsoft365_server import Microsoft365MCPServerTool

# Configure logging
logger = logging.getLogger(__name__)

def init_registry() -> None:
    """Initialize the tool registry with all available tools."""
    logger.info("Initializing tool registry...")
    
    # Register tools
    registry.register(NewsScraperTool)
    registry.register(JSONMCPServerTool)
    registry.register(GitHubMCPServerTool)
    registry.register(PostgreSQLMCPServerTool)
    registry.register(Microsoft365MCPServerTool)
    
    # Log registered tools
    tools = registry.list_tools()
    logger.info(f"Registered {len(tools)} tools:")
    for tool in tools:
        logger.info(f"  - {tool['name']} ({tool['category']}): {tool['description']}")

def get_registry():
    """Get the tool registry."""
    return registry

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Initialize registry
    init_registry()
    
    # Print tool documentation
    for tool in registry.list_tools():
        print(f"\n{'=' * 80}")
        print(registry.get_tool_documentation(tool['name']))
        print(f"{'=' * 80}\n")
