#!/usr/bin/env python3
"""
Tool Registry System

Provides a central registry for MCP tools:
1. Tool registration and discovery
2. Tool execution and error handling
3. Tool documentation and help
4. Tool chaining and composition
"""

import inspect
import logging
import json
import time
from typing import Dict, List, Any, Optional, Callable, Type, Union, Set
from functools import wraps
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)

class ToolError(Exception):
    """Exception raised when a tool encounters an error."""
    pass

class Tool:
    """Base class for all MCP tools."""
    
    name: str = "base_tool"
    description: str = "Base tool class"
    version: str = "1.0.0"
    category: str = "general"
    
    def __init__(self, registry=None):
        """
        Initialize the tool.
        
        Args:
            registry: The tool registry this tool belongs to
        """
        self.registry = registry
        self._setup()
    
    def _setup(self):
        """Set up the tool. Override in subclasses."""
        pass
    
    def execute(self, **kwargs) -> Any:
        """
        Execute the tool.
        
        Args:
            **kwargs: Tool-specific arguments
            
        Returns:
            Tool execution result
        """
        raise NotImplementedError("Tool must implement execute method")
    
    def get_schema(self) -> Dict[str, Any]:
        """
        Get the tool's parameter schema.
        
        Returns:
            JSON schema for the tool's parameters
        """
        # Get the signature of the execute method
        sig = inspect.signature(self.execute)
        
        # Build schema from parameter annotations
        properties = {}
        required = []
        
        for name, param in sig.parameters.items():
            if name == 'self':
                continue
                
            # Get parameter type annotation
            param_type = param.annotation
            if param_type is inspect.Parameter.empty:
                param_type = Any
            
            # Convert Python type to JSON schema type
            json_type = "string"
            if param_type in (int, float):
                json_type = "number"
            elif param_type is bool:
                json_type = "boolean"
            elif param_type in (list, List):
                json_type = "array"
            elif param_type in (dict, Dict):
                json_type = "object"
            
            # Add property to schema
            properties[name] = {
                "type": json_type,
                "description": f"Parameter: {name}"
            }
            
            # Check if parameter is required
            if param.default is inspect.Parameter.empty:
                required.append(name)
        
        return {
            "type": "object",
            "properties": properties,
            "required": required
        }
    
    def get_documentation(self) -> str:
        """
        Get the tool's documentation.
        
        Returns:
            Markdown-formatted documentation
        """
        doc = f"# {self.name}\n\n"
        doc += f"{self.description}\n\n"
        doc += f"**Version:** {self.version}\n"
        doc += f"**Category:** {self.category}\n\n"
        
        # Add parameter documentation
        doc += "## Parameters\n\n"
        schema = self.get_schema()
        for name, prop in schema.get("properties", {}).items():
            required = name in schema.get("required", [])
            doc += f"- `{name}` ({prop.get('type')}): {prop.get('description')} "
            if required:
                doc += "(Required)\n"
            else:
                doc += "(Optional)\n"
        
        # Add example usage if available
        if hasattr(self, "example_usage") and self.example_usage:
            doc += "\n## Example\n\n```python\n"
            doc += self.example_usage
            doc += "\n```\n"
        
        return doc

class ToolRegistry:
    """Registry for MCP tools."""
    
    def __init__(self):
        """Initialize the tool registry."""
        self.tools: Dict[str, Tool] = {}
        self.categories: Dict[str, Set[str]] = {}
        self.execution_history: List[Dict[str, Any]] = []
    
    def register(self, tool_class: Type[Tool]) -> None:
        """
        Register a tool with the registry.
        
        Args:
            tool_class: Tool class to register
        """
        # Create an instance of the tool
        tool = tool_class(registry=self)
        
        # Register the tool
        self.tools[tool.name] = tool
        
        # Add to category
        if tool.category not in self.categories:
            self.categories[tool.category] = set()
        self.categories[tool.category].add(tool.name)
        
        logger.info(f"Registered tool: {tool.name} (category: {tool.category})")
    
    def get_tool(self, name: str) -> Optional[Tool]:
        """
        Get a tool by name.
        
        Args:
            name: Name of the tool
            
        Returns:
            Tool instance or None if not found
        """
        return self.tools.get(name)
    
    def list_tools(self, category: Optional[str] = None) -> List[Dict[str, str]]:
        """
        List available tools.
        
        Args:
            category: Filter by category (optional)
            
        Returns:
            List of tool information dictionaries
        """
        result = []
        
        # Filter by category if provided
        tool_names = []
        if category:
            tool_names = list(self.categories.get(category, []))
        else:
            tool_names = list(self.tools.keys())
        
        # Build result
        for name in sorted(tool_names):
            tool = self.tools[name]
            result.append({
                "name": tool.name,
                "description": tool.description,
                "version": tool.version,
                "category": tool.category
            })
        
        return result
    
    def execute_tool(self, name: str, **kwargs) -> Any:
        """
        Execute a tool by name.
        
        Args:
            name: Name of the tool
            **kwargs: Tool-specific arguments
            
        Returns:
            Tool execution result
            
        Raises:
            ToolError: If the tool is not found or execution fails
        """
        # Get the tool
        tool = self.get_tool(name)
        if not tool:
            raise ToolError(f"Tool not found: {name}")
        
        # Record execution start
        start_time = time.time()
        execution_id = len(self.execution_history) + 1
        
        execution_record = {
            "id": execution_id,
            "tool": name,
            "params": kwargs,
            "start_time": datetime.now().isoformat(),
            "status": "running"
        }
        self.execution_history.append(execution_record)
        
        try:
            # Execute the tool
            logger.info(f"Executing tool: {name}")
            result = tool.execute(**kwargs)
            
            # Update execution record
            execution_record["status"] = "success"
            execution_record["end_time"] = datetime.now().isoformat()
            execution_record["duration"] = time.time() - start_time
            
            return result
        except Exception as e:
            # Update execution record
            execution_record["status"] = "error"
            execution_record["end_time"] = datetime.now().isoformat()
            execution_record["duration"] = time.time() - start_time
            execution_record["error"] = str(e)
            
            logger.error(f"Error executing tool {name}: {e}")
            raise ToolError(f"Error executing tool {name}: {e}")
    
    def get_execution_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get the tool execution history.
        
        Args:
            limit: Maximum number of entries to return
            
        Returns:
            List of execution records
        """
        return self.execution_history[-limit:]
    
    def get_tool_schema(self, name: str) -> Optional[Dict[str, Any]]:
        """
        Get a tool's parameter schema.
        
        Args:
            name: Name of the tool
            
        Returns:
            JSON schema for the tool's parameters or None if not found
        """
        tool = self.get_tool(name)
        if not tool:
            return None
        
        return tool.get_schema()
    
    def get_tool_documentation(self, name: str) -> Optional[str]:
        """
        Get a tool's documentation.
        
        Args:
            name: Name of the tool
            
        Returns:
            Markdown-formatted documentation or None if not found
        """
        tool = self.get_tool(name)
        if not tool:
            return None
        
        return tool.get_documentation()

# Global registry instance
registry = ToolRegistry()
