#!/usr/bin/env python3
"""
Microsoft 365 MCP Server Tool

Provides Microsoft 365 integration for the MCP:
1. Sends emails with Form D analysis results
2. Manages calendar events for important filings
3. Stores analysis results in OneDrive/SharePoint
4. Integrates with Microsoft Teams for notifications
"""

import os
import json
import logging
import time
import base64
import mimetypes
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
import requests
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication

from mcp.tools.registry import Tool, ToolError

# Configure logging
logger = logging.getLogger(__name__)

class Microsoft365MCPServerTool(Tool):
    """Tool for Microsoft 365 integration with the MCP."""
    
    name = "microsoft365_mcp_server"
    description = "Provides Microsoft 365 integration for the MCP"
    version = "1.0.0"
    category = "server"
    
    def _setup(self):
        """Set up the Microsoft 365 MCP server tool."""
        # Get credentials from environment variables
        self.client_id = os.environ.get("MS365_CLIENT_ID")
        self.client_secret = os.environ.get("MS365_CLIENT_SECRET")
        self.tenant_id = os.environ.get("MS365_TENANT_ID")
        self.refresh_token = os.environ.get("MS365_REFRESH_TOKEN")
        
        # Token cache
        self.access_token = None
        self.token_expires_at = 0
        
        # Initialize session
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "Private-Signals-MCP/1.0"
        })
    
    def execute(self, 
               action: str,
               **kwargs) -> Dict[str, Any]:
        """
        Execute the Microsoft 365 MCP server tool.
        
        Args:
            action: Action to perform (send_email, create_event, etc.)
            **kwargs: Action-specific parameters
            
        Returns:
            Dictionary with action results
        """
        # Dispatch to appropriate method based on action
        if action == "send_email":
            return self._send_email(**kwargs)
        elif action == "create_event":
            return self._create_event(**kwargs)
        elif action == "upload_file":
            return self._upload_file(**kwargs)
        elif action == "send_teams_message":
            return self._send_teams_message(**kwargs)
        elif action == "get_access_token":
            return self._get_access_token_info()
        else:
            raise ToolError(f"Invalid action: {action}")
    
    def _get_access_token(self) -> str:
        """
        Get a valid access token.
        
        Returns:
            Access token string
        """
        # Check if we have a valid token
        if self.access_token and time.time() < self.token_expires_at - 300:
            return self.access_token
        
        # Check if we have credentials
        if not all([self.client_id, self.client_secret, self.tenant_id, self.refresh_token]):
            raise ToolError("Microsoft 365 credentials not available")
        
        try:
            # Get new token
            token_url = f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token"
            
            data = {
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "refresh_token": self.refresh_token,
                "grant_type": "refresh_token",
                "scope": "https://graph.microsoft.com/.default"
            }
            
            response = self.session.post(token_url, data=data)
            response.raise_for_status()
            
            token_data = response.json()
            
            # Update token cache
            self.access_token = token_data["access_token"]
            self.token_expires_at = time.time() + token_data["expires_in"]
            
            return self.access_token
        except Exception as e:
            logger.error(f"Error getting access token: {e}")
            raise ToolError(f"Error getting access token: {str(e)}")
    
    def _get_access_token_info(self) -> Dict[str, Any]:
        """
        Get information about the current access token.
        
        Returns:
            Dictionary with token information
        """
        try:
            token = self._get_access_token()
            
            return {
                "status": "success",
                "has_token": bool(token),
                "expires_in": int(self.token_expires_at - time.time()) if token else 0,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error getting token info: {e}")
            return {
                "status": "error",
                "has_token": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def _send_email(self, 
                   to: List[str],
                   subject: str,
                   body: str,
                   body_type: str = "html",
                   cc: Optional[List[str]] = None,
                   bcc: Optional[List[str]] = None,
                   attachments: Optional[List[Dict[str, Any]]] = None,
                   save_to_sent: bool = True) -> Dict[str, Any]:
        """
        Send an email using Microsoft Graph API.
        
        Args:
            to: List of recipient email addresses
            subject: Email subject
            body: Email body
            body_type: Body type (html or text)
            cc: List of CC email addresses
            bcc: List of BCC email addresses
            attachments: List of attachment dictionaries
            save_to_sent: Whether to save to sent items
            
        Returns:
            Dictionary with email status
        """
        if not to:
            raise ToolError("Recipients are required")
        
        if not subject:
            raise ToolError("Subject is required")
        
        if not body:
            raise ToolError("Body is required")
        
        try:
            # Get access token
            token = self._get_access_token()
            
            # Prepare headers
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            # Prepare recipients
            to_recipients = [{"emailAddress": {"address": email}} for email in to]
            cc_recipients = [{"emailAddress": {"address": email}} for email in cc] if cc else []
            bcc_recipients = [{"emailAddress": {"address": email}} for email in bcc] if bcc else []
            
            # Prepare message
            message = {
                "message": {
                    "subject": subject,
                    "body": {
                        "contentType": body_type,
                        "content": body
                    },
                    "toRecipients": to_recipients,
                    "ccRecipients": cc_recipients,
                    "bccRecipients": bcc_recipients
                },
                "saveToSentItems": save_to_sent
            }
            
            # Add attachments if provided
            if attachments:
                message_attachments = []
                
                for attachment in attachments:
                    # Check required fields
                    if not all([attachment.get("name"), attachment.get("content")]):
                        logger.warning(f"Skipping attachment with missing fields: {attachment}")
                        continue
                    
                    # Determine content type
                    content_type = attachment.get("content_type")
                    if not content_type:
                        content_type, _ = mimetypes.guess_type(attachment["name"])
                        if not content_type:
                            content_type = "application/octet-stream"
                    
                    # Encode content if it's not already base64
                    content = attachment["content"]
                    if not isinstance(content, str) or not self._is_base64(content):
                        content = base64.b64encode(content).decode("utf-8")
                    
                    # Add attachment
                    message_attachments.append({
                        "@odata.type": "#microsoft.graph.fileAttachment",
                        "name": attachment["name"],
                        "contentType": content_type,
                        "contentBytes": content
                    })
                
                if message_attachments:
                    message["message"]["attachments"] = message_attachments
            
            # Send message
            url = "https://graph.microsoft.com/v1.0/me/sendMail"
            response = self.session.post(url, headers=headers, json=message)
            response.raise_for_status()
            
            return {
                "status": "success",
                "to": to,
                "subject": subject,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error sending email: {e}")
            raise ToolError(f"Error sending email: {str(e)}")
    
    def _create_event(self,
                     subject: str,
                     start: str,
                     end: str,
                     body: str,
                     body_type: str = "html",
                     location: Optional[str] = None,
                     attendees: Optional[List[str]] = None,
                     is_online_meeting: bool = False,
                     reminder_minutes: int = 15) -> Dict[str, Any]:
        """
        Create a calendar event using Microsoft Graph API.
        
        Args:
            subject: Event subject
            start: Start time (ISO format)
            end: End time (ISO format)
            body: Event body
            body_type: Body type (html or text)
            location: Event location
            attendees: List of attendee email addresses
            is_online_meeting: Whether to create an online meeting
            reminder_minutes: Reminder time in minutes
            
        Returns:
            Dictionary with event status
        """
        if not subject:
            raise ToolError("Subject is required")
        
        if not start or not end:
            raise ToolError("Start and end times are required")
        
        try:
            # Get access token
            token = self._get_access_token()
            
            # Prepare headers
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            # Prepare attendees
            event_attendees = []
            if attendees:
                for email in attendees:
                    event_attendees.append({
                        "emailAddress": {
                            "address": email
                        },
                        "type": "required"
                    })
            
            # Prepare event
            event = {
                "subject": subject,
                "body": {
                    "contentType": body_type,
                    "content": body
                },
                "start": {
                    "dateTime": start,
                    "timeZone": "UTC"
                },
                "end": {
                    "dateTime": end,
                    "timeZone": "UTC"
                },
                "reminderMinutesBeforeStart": reminder_minutes,
                "isOnlineMeeting": is_online_meeting
            }
            
            # Add location if provided
            if location:
                event["location"] = {
                    "displayName": location
                }
            
            # Add attendees if provided
            if event_attendees:
                event["attendees"] = event_attendees
            
            # Create event
            url = "https://graph.microsoft.com/v1.0/me/events"
            response = self.session.post(url, headers=headers, json=event)
            response.raise_for_status()
            
            created_event = response.json()
            
            return {
                "status": "success",
                "event_id": created_event.get("id"),
                "subject": subject,
                "start": start,
                "end": end,
                "online_meeting_url": created_event.get("onlineMeeting", {}).get("joinUrl") if is_online_meeting else None,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error creating event: {e}")
            raise ToolError(f"Error creating event: {str(e)}")
    
    def _upload_file(self,
                    file_name: str,
                    content: str,
                    folder_path: str = "/Documents",
                    content_type: Optional[str] = None,
                    overwrite: bool = True) -> Dict[str, Any]:
        """
        Upload a file to OneDrive using Microsoft Graph API.
        
        Args:
            file_name: Name of the file
            content: File content (base64 encoded or raw)
            folder_path: Path to the folder in OneDrive
            content_type: Content type of the file
            overwrite: Whether to overwrite existing file
            
        Returns:
            Dictionary with upload status
        """
        if not file_name:
            raise ToolError("File name is required")
        
        if not content:
            raise ToolError("Content is required")
        
        try:
            # Get access token
            token = self._get_access_token()
            
            # Prepare headers
            headers = {
                "Authorization": f"Bearer {token}"
            }
            
            # Determine content type
            if not content_type:
                content_type, _ = mimetypes.guess_type(file_name)
                if not content_type:
                    content_type = "application/octet-stream"
            
            # Encode content if it's not already base64
            if not isinstance(content, str) or not self._is_base64(content):
                content = base64.b64encode(content).decode("utf-8")
            
            # Decode base64 content
            file_content = base64.b64decode(content)
            
            # Prepare upload URL
            folder_path = folder_path.strip("/")
            upload_url = f"https://graph.microsoft.com/v1.0/me/drive/root:/{folder_path}/{file_name}:/content"
            
            if overwrite:
                upload_url += "?@microsoft.graph.conflictBehavior=replace"
            else:
                upload_url += "?@microsoft.graph.conflictBehavior=fail"
            
            # Upload file
            headers["Content-Type"] = content_type
            response = self.session.put(upload_url, headers=headers, data=file_content)
            response.raise_for_status()
            
            file_info = response.json()
            
            return {
                "status": "success",
                "file_id": file_info.get("id"),
                "file_name": file_name,
                "web_url": file_info.get("webUrl"),
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error uploading file: {e}")
            raise ToolError(f"Error uploading file: {str(e)}")
    
    def _send_teams_message(self,
                           channel_id: str,
                           team_id: str,
                           content: str,
                           subject: Optional[str] = None,
                           importance: str = "normal") -> Dict[str, Any]:
        """
        Send a message to a Microsoft Teams channel.
        
        Args:
            channel_id: ID of the channel
            team_id: ID of the team
            content: Message content (HTML)
            subject: Message subject
            importance: Message importance (normal, high, urgent)
            
        Returns:
            Dictionary with message status
        """
        if not channel_id:
            raise ToolError("Channel ID is required")
        
        if not team_id:
            raise ToolError("Team ID is required")
        
        if not content:
            raise ToolError("Content is required")
        
        try:
            # Get access token
            token = self._get_access_token()
            
            # Prepare headers
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            # Prepare message
            message = {
                "body": {
                    "contentType": "html",
                    "content": content
                },
                "importance": importance
            }
            
            # Add subject if provided
            if subject:
                message["subject"] = subject
            
            # Send message
            url = f"https://graph.microsoft.com/v1.0/teams/{team_id}/channels/{channel_id}/messages"
            response = self.session.post(url, headers=headers, json=message)
            response.raise_for_status()
            
            message_info = response.json()
            
            return {
                "status": "success",
                "message_id": message_info.get("id"),
                "team_id": team_id,
                "channel_id": channel_id,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error sending Teams message: {e}")
            raise ToolError(f"Error sending Teams message: {str(e)}")
    
    def _is_base64(self, content: str) -> bool:
        """
        Check if a string is base64 encoded.
        
        Args:
            content: String to check
            
        Returns:
            True if base64 encoded, False otherwise
        """
        try:
            # Check if string is valid base64
            if not isinstance(content, str):
                return False
            
            # Check if length is valid for base64
            if len(content) % 4 != 0:
                return False
            
            # Try to decode
            base64.b64decode(content)
            return True
        except:
            return False

# Example usage
example_usage = """
# Send an email with Form D analysis
email_result = registry.execute_tool("microsoft365_mcp_server", 
                                    action="send_email",
                                    to=["<EMAIL>"],
                                    subject="Form D Analysis: High Relevance Filing",
                                    body="<h1>Form D Analysis</h1><p>Relevance Score: 0.92</p><p>This filing appears to be highly relevant...</p>",
                                    body_type="html")

# Create a calendar event for an important filing
event_result = registry.execute_tool("microsoft365_mcp_server",
                                    action="create_event",
                                    subject="Review High-Priority Form D Filing",
                                    start="2023-06-01T14:00:00Z",
                                    end="2023-06-01T15:00:00Z",
                                    body="<p>Review the recent Form D filing from Acme Corp.</p>",
                                    is_online_meeting=True)

# Upload analysis results to OneDrive
upload_result = registry.execute_tool("microsoft365_mcp_server",
                                     action="upload_file",
                                     file_name="form_d_analysis.json",
                                     content=json.dumps(analysis_results),
                                     folder_path="/Documents/Form D Analysis")
"""
