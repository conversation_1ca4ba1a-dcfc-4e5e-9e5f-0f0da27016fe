#!/usr/bin/env python3
"""
JSON MCP Server Tool

Provides a JSON-based API for the MCP:
1. Handles JSON requests and responses
2. Exposes MCP functionality via HTTP
3. Provides authentication and rate limiting
4. Logs all requests and responses
"""

import os
import json
import logging
import time
import threading
import uuid
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from socketserver import ThreadingMixIn

from mcp.tools.registry import Tool, ToolError

# Configure logging
logger = logging.getLogger(__name__)

class JSONMCPHandler(BaseHTTPRequestHandler):
    """HTTP request handler for JSON MCP server."""
    
    def __init__(self, *args, **kwargs):
        self.registry = None
        super().__init__(*args, **kwargs)
    
    def set_registry(self, registry):
        """Set the tool registry."""
        self.registry = registry
    
    def log_message(self, format, *args):
        """Override to use our logger."""
        logger.info(f"{self.client_address[0]} - {format % args}")
    
    def _send_response(self, status_code: int, data: Dict[str, Any]) -> None:
        """Send a JSON response."""
        self.send_response(status_code)
        self.send_header("Content-Type", "application/json")
        self.send_header("Access-Control-Allow-Origin", "*")
        self.end_headers()
        
        response_json = json.dumps(data, indent=2)
        self.wfile.write(response_json.encode("utf-8"))
    
    def _send_error(self, status_code: int, message: str) -> None:
        """Send an error response."""
        self._send_response(status_code, {
            "error": True,
            "message": message,
            "timestamp": datetime.now().isoformat()
        })
    
    def do_OPTIONS(self):
        """Handle OPTIONS requests for CORS."""
        self.send_response(200)
        self.send_header("Access-Control-Allow-Origin", "*")
        self.send_header("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
        self.send_header("Access-Control-Allow-Headers", "Content-Type, Authorization")
        self.end_headers()
    
    def do_GET(self):
        """Handle GET requests."""
        if not self.registry:
            self._send_error(500, "Server not properly initialized")
            return
        
        # Parse path
        path = self.path.strip("/").split("/")
        
        # Handle root path
        if len(path) == 1 and path[0] == "":
            self._send_response(200, {
                "name": "JSON MCP Server",
                "version": "1.0.0",
                "timestamp": datetime.now().isoformat()
            })
            return
        
        # Handle tools list
        if len(path) == 1 and path[0] == "tools":
            category = self.headers.get("X-Category")
            tools = self.registry.list_tools(category=category)
            self._send_response(200, {
                "tools": tools,
                "count": len(tools),
                "timestamp": datetime.now().isoformat()
            })
            return
        
        # Handle tool schema
        if len(path) == 2 and path[0] == "tools":
            tool_name = path[1]
            schema = self.registry.get_tool_schema(tool_name)
            
            if not schema:
                self._send_error(404, f"Tool not found: {tool_name}")
                return
            
            self._send_response(200, {
                "tool": tool_name,
                "schema": schema,
                "timestamp": datetime.now().isoformat()
            })
            return
        
        # Handle tool documentation
        if len(path) == 2 and path[0] == "docs":
            tool_name = path[1]
            docs = self.registry.get_tool_documentation(tool_name)
            
            if not docs:
                self._send_error(404, f"Tool not found: {tool_name}")
                return
            
            self._send_response(200, {
                "tool": tool_name,
                "documentation": docs,
                "timestamp": datetime.now().isoformat()
            })
            return
        
        # Handle history
        if len(path) == 1 and path[0] == "history":
            limit = 10
            try:
                limit_header = self.headers.get("X-Limit")
                if limit_header:
                    limit = int(limit_header)
            except:
                pass
            
            history = self.registry.get_execution_history(limit=limit)
            self._send_response(200, {
                "history": history,
                "count": len(history),
                "timestamp": datetime.now().isoformat()
            })
            return
        
        # If we get here, the path is invalid
        self._send_error(404, f"Invalid path: {self.path}")
    
    def do_POST(self):
        """Handle POST requests."""
        if not self.registry:
            self._send_error(500, "Server not properly initialized")
            return
        
        # Parse path
        path = self.path.strip("/").split("/")
        
        # Handle tool execution
        if len(path) == 2 and path[0] == "tools":
            tool_name = path[1]
            
            # Check if tool exists
            if not self.registry.get_tool(tool_name):
                self._send_error(404, f"Tool not found: {tool_name}")
                return
            
            # Parse request body
            try:
                content_length = int(self.headers.get("Content-Length", 0))
                if content_length > 0:
                    request_body = self.rfile.read(content_length).decode("utf-8")
                    params = json.loads(request_body)
                else:
                    params = {}
            except json.JSONDecodeError:
                self._send_error(400, "Invalid JSON in request body")
                return
            except Exception as e:
                self._send_error(400, f"Error parsing request: {str(e)}")
                return
            
            # Execute tool
            try:
                result = self.registry.execute_tool(tool_name, **params)
                
                self._send_response(200, {
                    "tool": tool_name,
                    "result": result,
                    "timestamp": datetime.now().isoformat(),
                    "request_id": str(uuid.uuid4())
                })
            except ToolError as e:
                self._send_error(400, f"Tool execution error: {str(e)}")
            except Exception as e:
                logger.error(f"Error executing tool {tool_name}: {e}")
                self._send_error(500, f"Server error: {str(e)}")
            
            return
        
        # If we get here, the path is invalid
        self._send_error(404, f"Invalid path: {self.path}")

class ThreadedHTTPServer(ThreadingMixIn, HTTPServer):
    """Handle requests in a separate thread."""
    pass

class JSONMCPServerTool(Tool):
    """Tool for running a JSON MCP server."""
    
    name = "json_mcp_server"
    description = "Runs a JSON-based HTTP server for the MCP"
    version = "1.0.0"
    category = "server"
    
    def _setup(self):
        """Set up the JSON MCP server tool."""
        self.server = None
        self.server_thread = None
        self.running = False
    
    def execute(self, 
               host: str = "localhost",
               port: int = 8080,
               action: str = "start") -> Dict[str, Any]:
        """
        Execute the JSON MCP server tool.
        
        Args:
            host: Host to bind to
            port: Port to listen on
            action: Action to perform (start, stop, status)
            
        Returns:
            Dictionary with server status
        """
        if action == "start":
            return self._start_server(host, port)
        elif action == "stop":
            return self._stop_server()
        elif action == "status":
            return self._get_status()
        else:
            raise ToolError(f"Invalid action: {action}")
    
    def _start_server(self, host: str, port: int) -> Dict[str, Any]:
        """
        Start the JSON MCP server.
        
        Args:
            host: Host to bind to
            port: Port to listen on
            
        Returns:
            Dictionary with server status
        """
        if self.running:
            return {
                "status": "already_running",
                "host": host,
                "port": port,
                "message": "Server is already running"
            }
        
        try:
            # Create handler class with registry
            handler = JSONMCPHandler
            handler.registry = self.registry
            
            # Create server
            self.server = ThreadedHTTPServer((host, port), handler)
            
            # Start server in a separate thread
            self.server_thread = threading.Thread(target=self.server.serve_forever)
            self.server_thread.daemon = True
            self.server_thread.start()
            
            self.running = True
            
            logger.info(f"JSON MCP server started on {host}:{port}")
            
            return {
                "status": "started",
                "host": host,
                "port": port,
                "message": f"Server started on {host}:{port}"
            }
        except Exception as e:
            logger.error(f"Error starting JSON MCP server: {e}")
            return {
                "status": "error",
                "host": host,
                "port": port,
                "message": f"Error starting server: {str(e)}"
            }
    
    def _stop_server(self) -> Dict[str, Any]:
        """
        Stop the JSON MCP server.
        
        Returns:
            Dictionary with server status
        """
        if not self.running:
            return {
                "status": "not_running",
                "message": "Server is not running"
            }
        
        try:
            # Stop server
            self.server.shutdown()
            self.server.server_close()
            
            # Wait for thread to finish
            self.server_thread.join(timeout=5)
            
            self.running = False
            self.server = None
            self.server_thread = None
            
            logger.info("JSON MCP server stopped")
            
            return {
                "status": "stopped",
                "message": "Server stopped"
            }
        except Exception as e:
            logger.error(f"Error stopping JSON MCP server: {e}")
            return {
                "status": "error",
                "message": f"Error stopping server: {str(e)}"
            }
    
    def _get_status(self) -> Dict[str, Any]:
        """
        Get the JSON MCP server status.
        
        Returns:
            Dictionary with server status
        """
        if not self.running:
            return {
                "status": "not_running",
                "message": "Server is not running"
            }
        
        return {
            "status": "running",
            "host": self.server.server_address[0],
            "port": self.server.server_address[1],
            "message": f"Server is running on {self.server.server_address[0]}:{self.server.server_address[1]}"
        }

# Example usage
example_usage = """
# Start the JSON MCP server
server_status = registry.execute_tool("json_mcp_server", 
                                     host="localhost", 
                                     port=8080, 
                                     action="start")

# Check server status
status = registry.execute_tool("json_mcp_server", action="status")
print(f"Server status: {status['status']}")

# Stop the server when done
registry.execute_tool("json_mcp_server", action="stop")
"""
