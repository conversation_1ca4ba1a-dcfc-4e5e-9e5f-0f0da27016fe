#!/usr/bin/env python3
"""
PostgreSQL MCP Server Tool

Provides PostgreSQL database integration for the MCP:
1. Connects to PostgreSQL databases
2. Executes SQL queries and commands
3. Manages database schema and migrations
4. Provides data access layer for Form D data
"""

import os
import json
import logging
import time
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime
import psycopg2
from psycopg2.extras import RealDictCursor
import pandas as pd

from mcp.tools.registry import Tool, ToolError

# Configure logging
logger = logging.getLogger(__name__)

class PostgreSQLMCPServerTool(Tool):
    """Tool for PostgreSQL integration with the MCP."""
    
    name = "postgresql_mcp_server"
    description = "Provides PostgreSQL database integration for the MCP"
    version = "1.0.0"
    category = "server"
    
    def _setup(self):
        """Set up the PostgreSQL MCP server tool."""
        self.connections = {}
        self.default_connection_string = os.environ.get("POSTGRES_CONNECTION_STRING")
    
    def execute(self, 
               action: str,
               connection_string: Optional[str] = None,
               connection_id: Optional[str] = None,
               query: Optional[str] = None,
               params: Optional[Dict[str, Any]] = None,
               table_name: Optional[str] = None,
               schema_name: Optional[str] = "public",
               data: Optional[List[Dict[str, Any]]] = None,
               **kwargs) -> Dict[str, Any]:
        """
        Execute the PostgreSQL MCP server tool.
        
        Args:
            action: Action to perform (connect, disconnect, query, execute, etc.)
            connection_string: PostgreSQL connection string
            connection_id: ID for the connection
            query: SQL query or command
            params: Query parameters
            table_name: Table name for operations
            schema_name: Schema name for operations
            data: Data for insert/update operations
            **kwargs: Additional action-specific parameters
            
        Returns:
            Dictionary with action results
        """
        # Use default connection string if not provided
        if not connection_string and not connection_id and self.default_connection_string:
            connection_string = self.default_connection_string
        
        # Dispatch to appropriate method based on action
        if action == "connect":
            return self._connect(connection_string, connection_id)
        elif action == "disconnect":
            return self._disconnect(connection_id)
        elif action == "list_connections":
            return self._list_connections()
        elif action == "query":
            return self._query(connection_id, query, params)
        elif action == "execute":
            return self._execute(connection_id, query, params)
        elif action == "list_tables":
            return self._list_tables(connection_id, schema_name)
        elif action == "describe_table":
            return self._describe_table(connection_id, table_name, schema_name)
        elif action == "insert_data":
            return self._insert_data(connection_id, table_name, schema_name, data)
        elif action == "update_data":
            return self._update_data(connection_id, table_name, schema_name, data, **kwargs)
        elif action == "delete_data":
            return self._delete_data(connection_id, table_name, schema_name, **kwargs)
        elif action == "export_to_csv":
            return self._export_to_csv(connection_id, query, params, **kwargs)
        elif action == "import_from_csv":
            return self._import_from_csv(connection_id, table_name, schema_name, **kwargs)
        else:
            raise ToolError(f"Invalid action: {action}")
    
    def _connect(self, connection_string: str, connection_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Connect to a PostgreSQL database.
        
        Args:
            connection_string: PostgreSQL connection string
            connection_id: ID for the connection
            
        Returns:
            Dictionary with connection status
        """
        if not connection_string:
            raise ToolError("Connection string is required")
        
        # Generate connection ID if not provided
        if not connection_id:
            connection_id = f"conn_{int(time.time())}"
        
        try:
            # Connect to database
            conn = psycopg2.connect(connection_string)
            
            # Store connection
            self.connections[connection_id] = {
                "connection": conn,
                "connection_string": self._mask_password(connection_string),
                "created_at": datetime.now().isoformat()
            }
            
            logger.info(f"Connected to PostgreSQL database: {connection_id}")
            
            return {
                "status": "connected",
                "connection_id": connection_id,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error connecting to PostgreSQL database: {e}")
            raise ToolError(f"Error connecting to database: {str(e)}")
    
    def _disconnect(self, connection_id: str) -> Dict[str, Any]:
        """
        Disconnect from a PostgreSQL database.
        
        Args:
            connection_id: ID for the connection
            
        Returns:
            Dictionary with disconnection status
        """
        if not connection_id:
            raise ToolError("Connection ID is required")
        
        if connection_id not in self.connections:
            raise ToolError(f"Connection not found: {connection_id}")
        
        try:
            # Close connection
            conn = self.connections[connection_id]["connection"]
            conn.close()
            
            # Remove connection
            del self.connections[connection_id]
            
            logger.info(f"Disconnected from PostgreSQL database: {connection_id}")
            
            return {
                "status": "disconnected",
                "connection_id": connection_id,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error disconnecting from PostgreSQL database: {e}")
            raise ToolError(f"Error disconnecting from database: {str(e)}")
    
    def _list_connections(self) -> Dict[str, Any]:
        """
        List active database connections.
        
        Returns:
            Dictionary with connection list
        """
        connections = []
        
        for conn_id, conn_info in self.connections.items():
            connections.append({
                "connection_id": conn_id,
                "connection_string": conn_info["connection_string"],
                "created_at": conn_info["created_at"]
            })
        
        return {
            "connections": connections,
            "count": len(connections),
            "timestamp": datetime.now().isoformat()
        }
    
    def _query(self, connection_id: str, query: str, 
              params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Execute a SQL query.
        
        Args:
            connection_id: ID for the connection
            query: SQL query
            params: Query parameters
            
        Returns:
            Dictionary with query results
        """
        if not connection_id:
            raise ToolError("Connection ID is required")
        
        if not query:
            raise ToolError("Query is required")
        
        if connection_id not in self.connections:
            raise ToolError(f"Connection not found: {connection_id}")
        
        try:
            # Get connection
            conn = self.connections[connection_id]["connection"]
            
            # Execute query
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                # Fetch results
                results = cursor.fetchall()
                
                # Convert to list of dictionaries
                rows = [dict(row) for row in results]
                
                return {
                    "rows": rows,
                    "count": len(rows),
                    "query": query,
                    "connection_id": connection_id,
                    "timestamp": datetime.now().isoformat()
                }
        except Exception as e:
            logger.error(f"Error executing query: {e}")
            raise ToolError(f"Error executing query: {str(e)}")
    
    def _execute(self, connection_id: str, query: str,
                params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Execute a SQL command.
        
        Args:
            connection_id: ID for the connection
            query: SQL command
            params: Query parameters
            
        Returns:
            Dictionary with execution status
        """
        if not connection_id:
            raise ToolError("Connection ID is required")
        
        if not query:
            raise ToolError("Query is required")
        
        if connection_id not in self.connections:
            raise ToolError(f"Connection not found: {connection_id}")
        
        try:
            # Get connection
            conn = self.connections[connection_id]["connection"]
            
            # Execute command
            with conn.cursor() as cursor:
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                # Get row count
                row_count = cursor.rowcount
                
                # Commit changes
                conn.commit()
                
                return {
                    "status": "success",
                    "row_count": row_count,
                    "query": query,
                    "connection_id": connection_id,
                    "timestamp": datetime.now().isoformat()
                }
        except Exception as e:
            logger.error(f"Error executing command: {e}")
            
            # Rollback changes
            conn.rollback()
            
            raise ToolError(f"Error executing command: {str(e)}")
    
    def _list_tables(self, connection_id: str, 
                    schema_name: str = "public") -> Dict[str, Any]:
        """
        List tables in a schema.
        
        Args:
            connection_id: ID for the connection
            schema_name: Schema name
            
        Returns:
            Dictionary with table list
        """
        if not connection_id:
            raise ToolError("Connection ID is required")
        
        if connection_id not in self.connections:
            raise ToolError(f"Connection not found: {connection_id}")
        
        try:
            # Get connection
            conn = self.connections[connection_id]["connection"]
            
            # Query tables
            query = """
            SELECT table_name, table_type
            FROM information_schema.tables
            WHERE table_schema = %s
            ORDER BY table_name
            """
            
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(query, (schema_name,))
                tables = [dict(row) for row in cursor.fetchall()]
                
                return {
                    "tables": tables,
                    "count": len(tables),
                    "schema": schema_name,
                    "connection_id": connection_id,
                    "timestamp": datetime.now().isoformat()
                }
        except Exception as e:
            logger.error(f"Error listing tables: {e}")
            raise ToolError(f"Error listing tables: {str(e)}")
    
    def _describe_table(self, connection_id: str, table_name: str,
                       schema_name: str = "public") -> Dict[str, Any]:
        """
        Describe a table's structure.
        
        Args:
            connection_id: ID for the connection
            table_name: Table name
            schema_name: Schema name
            
        Returns:
            Dictionary with table structure
        """
        if not connection_id:
            raise ToolError("Connection ID is required")
        
        if not table_name:
            raise ToolError("Table name is required")
        
        if connection_id not in self.connections:
            raise ToolError(f"Connection not found: {connection_id}")
        
        try:
            # Get connection
            conn = self.connections[connection_id]["connection"]
            
            # Query columns
            columns_query = """
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns
            WHERE table_schema = %s AND table_name = %s
            ORDER BY ordinal_position
            """
            
            # Query constraints
            constraints_query = """
            SELECT c.conname as constraint_name, c.contype as constraint_type,
                   pg_get_constraintdef(c.oid) as constraint_definition
            FROM pg_constraint c
            JOIN pg_namespace n ON n.oid = c.connamespace
            JOIN pg_class t ON t.oid = c.conrelid
            WHERE n.nspname = %s AND t.relname = %s
            """
            
            # Query indexes
            indexes_query = """
            SELECT indexname, indexdef
            FROM pg_indexes
            WHERE schemaname = %s AND tablename = %s
            """
            
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # Get columns
                cursor.execute(columns_query, (schema_name, table_name))
                columns = [dict(row) for row in cursor.fetchall()]
                
                # Get constraints
                cursor.execute(constraints_query, (schema_name, table_name))
                constraints = [dict(row) for row in cursor.fetchall()]
                
                # Get indexes
                cursor.execute(indexes_query, (schema_name, table_name))
                indexes = [dict(row) for row in cursor.fetchall()]
                
                return {
                    "table": table_name,
                    "schema": schema_name,
                    "columns": columns,
                    "constraints": constraints,
                    "indexes": indexes,
                    "connection_id": connection_id,
                    "timestamp": datetime.now().isoformat()
                }
        except Exception as e:
            logger.error(f"Error describing table: {e}")
            raise ToolError(f"Error describing table: {str(e)}")
    
    def _insert_data(self, connection_id: str, table_name: str,
                    schema_name: str = "public",
                    data: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        Insert data into a table.
        
        Args:
            connection_id: ID for the connection
            table_name: Table name
            schema_name: Schema name
            data: List of dictionaries with data to insert
            
        Returns:
            Dictionary with insertion status
        """
        if not connection_id:
            raise ToolError("Connection ID is required")
        
        if not table_name:
            raise ToolError("Table name is required")
        
        if not data:
            raise ToolError("Data is required")
        
        if connection_id not in self.connections:
            raise ToolError(f"Connection not found: {connection_id}")
        
        try:
            # Get connection
            conn = self.connections[connection_id]["connection"]
            
            # Get column names from first row
            columns = list(data[0].keys())
            
            # Build query
            placeholders = ", ".join([f"%({col})s" for col in columns])
            columns_str = ", ".join([f'"{col}"' for col in columns])
            
            query = f'INSERT INTO "{schema_name}"."{table_name}" ({columns_str}) VALUES ({placeholders})'
            
            # Execute query for each row
            with conn.cursor() as cursor:
                row_count = 0
                for row in data:
                    cursor.execute(query, row)
                    row_count += cursor.rowcount
                
                # Commit changes
                conn.commit()
                
                return {
                    "status": "success",
                    "row_count": row_count,
                    "table": table_name,
                    "schema": schema_name,
                    "connection_id": connection_id,
                    "timestamp": datetime.now().isoformat()
                }
        except Exception as e:
            logger.error(f"Error inserting data: {e}")
            
            # Rollback changes
            conn.rollback()
            
            raise ToolError(f"Error inserting data: {str(e)}")
    
    def _update_data(self, connection_id: str, table_name: str,
                    schema_name: str = "public",
                    data: Optional[List[Dict[str, Any]]] = None,
                    key_columns: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Update data in a table.
        
        Args:
            connection_id: ID for the connection
            table_name: Table name
            schema_name: Schema name
            data: List of dictionaries with data to update
            key_columns: List of column names to use as keys
            
        Returns:
            Dictionary with update status
        """
        if not connection_id:
            raise ToolError("Connection ID is required")
        
        if not table_name:
            raise ToolError("Table name is required")
        
        if not data:
            raise ToolError("Data is required")
        
        if not key_columns:
            raise ToolError("Key columns are required")
        
        if connection_id not in self.connections:
            raise ToolError(f"Connection not found: {connection_id}")
        
        try:
            # Get connection
            conn = self.connections[connection_id]["connection"]
            
            # Get column names from first row
            columns = list(data[0].keys())
            
            # Filter out key columns from update columns
            update_columns = [col for col in columns if col not in key_columns]
            
            if not update_columns:
                raise ToolError("No columns to update")
            
            # Build query
            set_clause = ", ".join([f'"{col}" = %({col})s' for col in update_columns])
            where_clause = " AND ".join([f'"{col}" = %({col})s' for col in key_columns])
            
            query = f'UPDATE "{schema_name}"."{table_name}" SET {set_clause} WHERE {where_clause}'
            
            # Execute query for each row
            with conn.cursor() as cursor:
                row_count = 0
                for row in data:
                    cursor.execute(query, row)
                    row_count += cursor.rowcount
                
                # Commit changes
                conn.commit()
                
                return {
                    "status": "success",
                    "row_count": row_count,
                    "table": table_name,
                    "schema": schema_name,
                    "connection_id": connection_id,
                    "timestamp": datetime.now().isoformat()
                }
        except Exception as e:
            logger.error(f"Error updating data: {e}")
            
            # Rollback changes
            conn.rollback()
            
            raise ToolError(f"Error updating data: {str(e)}")
    
    def _delete_data(self, connection_id: str, table_name: str,
                    schema_name: str = "public",
                    where_clause: Optional[str] = None,
                    params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Delete data from a table.
        
        Args:
            connection_id: ID for the connection
            table_name: Table name
            schema_name: Schema name
            where_clause: WHERE clause for deletion
            params: Query parameters
            
        Returns:
            Dictionary with deletion status
        """
        if not connection_id:
            raise ToolError("Connection ID is required")
        
        if not table_name:
            raise ToolError("Table name is required")
        
        if connection_id not in self.connections:
            raise ToolError(f"Connection not found: {connection_id}")
        
        try:
            # Get connection
            conn = self.connections[connection_id]["connection"]
            
            # Build query
            query = f'DELETE FROM "{schema_name}"."{table_name}"'
            
            if where_clause:
                query += f" WHERE {where_clause}"
            
            # Execute query
            with conn.cursor() as cursor:
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                # Get row count
                row_count = cursor.rowcount
                
                # Commit changes
                conn.commit()
                
                return {
                    "status": "success",
                    "row_count": row_count,
                    "table": table_name,
                    "schema": schema_name,
                    "connection_id": connection_id,
                    "timestamp": datetime.now().isoformat()
                }
        except Exception as e:
            logger.error(f"Error deleting data: {e}")
            
            # Rollback changes
            conn.rollback()
            
            raise ToolError(f"Error deleting data: {str(e)}")
    
    def _export_to_csv(self, connection_id: str, query: str,
                      params: Optional[Dict[str, Any]] = None,
                      file_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Export query results to a CSV file.
        
        Args:
            connection_id: ID for the connection
            query: SQL query
            params: Query parameters
            file_path: Path to save the CSV file
            
        Returns:
            Dictionary with export status
        """
        if not connection_id:
            raise ToolError("Connection ID is required")
        
        if not query:
            raise ToolError("Query is required")
        
        if not file_path:
            raise ToolError("File path is required")
        
        if connection_id not in self.connections:
            raise ToolError(f"Connection not found: {connection_id}")
        
        try:
            # Get connection
            conn = self.connections[connection_id]["connection"]
            
            # Execute query and load into pandas DataFrame
            if params:
                df = pd.read_sql_query(query, conn, params=params)
            else:
                df = pd.read_sql_query(query, conn)
            
            # Save to CSV
            df.to_csv(file_path, index=False)
            
            return {
                "status": "success",
                "row_count": len(df),
                "file_path": file_path,
                "connection_id": connection_id,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error exporting to CSV: {e}")
            raise ToolError(f"Error exporting to CSV: {str(e)}")
    
    def _import_from_csv(self, connection_id: str, table_name: str,
                        schema_name: str = "public",
                        file_path: Optional[str] = None,
                        if_exists: str = "append") -> Dict[str, Any]:
        """
        Import data from a CSV file.
        
        Args:
            connection_id: ID for the connection
            table_name: Table name
            schema_name: Schema name
            file_path: Path to the CSV file
            if_exists: Action if table exists (fail, replace, append)
            
        Returns:
            Dictionary with import status
        """
        if not connection_id:
            raise ToolError("Connection ID is required")
        
        if not table_name:
            raise ToolError("Table name is required")
        
        if not file_path:
            raise ToolError("File path is required")
        
        if connection_id not in self.connections:
            raise ToolError(f"Connection not found: {connection_id}")
        
        try:
            # Get connection
            conn = self.connections[connection_id]["connection"]
            
            # Load CSV into pandas DataFrame
            df = pd.read_csv(file_path)
            
            # Save to database
            df.to_sql(
                table_name,
                conn,
                schema=schema_name,
                if_exists=if_exists,
                index=False
            )
            
            return {
                "status": "success",
                "row_count": len(df),
                "table": table_name,
                "schema": schema_name,
                "file_path": file_path,
                "connection_id": connection_id,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error importing from CSV: {e}")
            raise ToolError(f"Error importing from CSV: {str(e)}")
    
    def _mask_password(self, connection_string: str) -> str:
        """
        Mask password in connection string.
        
        Args:
            connection_string: PostgreSQL connection string
            
        Returns:
            Connection string with masked password
        """
        # Simple regex to mask password
        return re.sub(r"password=([^;]+)", "password=*****", connection_string)

# Example usage
example_usage = """
# Connect to PostgreSQL database
conn = registry.execute_tool("postgresql_mcp_server", 
                            action="connect",
                            connection_string="postgresql://username:password@localhost:5432/dbname",
                            connection_id="form_d_db")

# Query Form D filings
results = registry.execute_tool("postgresql_mcp_server",
                               action="query",
                               connection_id="form_d_db",
                               query="SELECT * FROM form_d_filings WHERE relevance_score > 0.7 ORDER BY filing_date DESC LIMIT 10")

# Disconnect when done
registry.execute_tool("postgresql_mcp_server",
                     action="disconnect",
                     connection_id="form_d_db")
"""
