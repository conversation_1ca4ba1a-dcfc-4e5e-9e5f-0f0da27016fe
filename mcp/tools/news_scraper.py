#!/usr/bin/env python3
"""
News Scraping Tool

Scrapes news articles related to Form D filings:
1. Searches for news about companies and industries
2. Extracts relevant information from articles
3. Provides context for Form D analysis
4. Caches results to avoid repeated scraping
"""

import os
import re
import json
import time
import logging
import requests
from bs4 import BeautifulSoup
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
import hashlib

from mcp.tools.registry import Tool, ToolError

# Configure logging
logger = logging.getLogger(__name__)

# Constants
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36"
]

class NewsScraperTool(Tool):
    """Tool for scraping news related to Form D filings."""

    name = "news_scraper"
    description = "Scrapes news articles related to companies and industries in Form D filings"
    version = "1.0.0"
    category = "data_enrichment"

    def _setup(self):
        """Set up the news scraper tool."""
        self.cache_dir = os.path.join("data", "cache", "news")
        os.makedirs(self.cache_dir, exist_ok=True)

        # Initialize session
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": USER_AGENTS[0],
            "Accept": "text/html,application/xhtml+xml,application/xml",
            "Accept-Language": "en-US,en;q=0.9"
        })

    def execute(self,
               query: str,
               max_results: int = 5,
               days_back: int = 30,
               cache_ttl: int = 86400,  # 24 hours in seconds
               sources: Optional[List[str]] = None,
               **kwargs) -> Dict[str, Any]:
        """
        Execute the news scraper tool.
        Enhanced for LLM integration with intelligent query processing.

        Args:
            query: Search query (company name, industry, etc.)
            max_results: Maximum number of results to return
            days_back: How many days back to search
            cache_ttl: Cache time-to-live in seconds
            sources: List of news sources to search (default: all)
            **kwargs: Additional parameters including _filing_context for LLM enhancement

        Returns:
            Dictionary with news articles and metadata
        """
        # Enhance query with filing context if available
        filing_context = kwargs.get('_filing_context', {})
        enhanced_query = self._enhance_query_with_context(query, filing_context)

        logger.info(f"Enhanced query from '{query}' to '{enhanced_query}'")
        # Check cache first
        cache_key = self._get_cache_key(enhanced_query, max_results, days_back, sources)
        cached_result = self._get_from_cache(cache_key)

        if cached_result:
            logger.info(f"Using cached news results for query: {query}")
            return cached_result

        # Prepare sources
        if not sources:
            sources = ["google_news", "bing_news", "yahoo_finance"]

        # Collect results from all sources
        all_results = []
        errors = []

        for source in sources:
            try:
                source_method = getattr(self, f"_search_{source}", None)
                if not source_method:
                    logger.warning(f"Unknown news source: {source}")
                    continue

                logger.info(f"Searching {source} for: {enhanced_query}")
                source_results = source_method(enhanced_query, max_results, days_back)
                all_results.extend(source_results)

                # Respect rate limits
                time.sleep(2)
            except Exception as e:
                logger.error(f"Error searching {source}: {e}")
                errors.append({"source": source, "error": str(e)})

        # Sort by date (newest first) and deduplicate
        unique_results = self._deduplicate_results(all_results)
        sorted_results = sorted(
            unique_results,
            key=lambda x: datetime.fromisoformat(x.get("date", "2000-01-01")),
            reverse=True
        )

        # Limit to max_results
        final_results = sorted_results[:max_results]

        # Enrich with article content if available
        for result in final_results:
            if "url" in result and not result.get("content"):
                try:
                    content = self._extract_article_content(result["url"])
                    if content:
                        result["content"] = content
                except Exception as e:
                    logger.error(f"Error extracting content from {result['url']}: {e}")

        # Prepare final response
        response = {
            "query": enhanced_query,
            "original_query": query,
            "timestamp": datetime.now().isoformat(),
            "results": final_results,
            "total_found": len(all_results),
            "sources_searched": sources,
            "errors": errors,
            "filing_context_used": bool(filing_context)
        }

        # Cache the result
        self._save_to_cache(cache_key, response)

        return response

    def _enhance_query_with_context(self, query: str, filing_context: Dict[str, Any]) -> str:
        """
        Enhance search query with filing context for better LLM-driven results.

        Args:
            query: Original search query
            filing_context: Filing context from tool orchestrator

        Returns:
            Enhanced search query
        """
        if not filing_context:
            return query

        enhanced_terms = []

        # Add issuer name if available and not already in query
        issuer_name = filing_context.get('issuer_name', '')
        if issuer_name and issuer_name.lower() not in query.lower():
            enhanced_terms.append(issuer_name)

        # Add industry context
        industry = filing_context.get('industry', '')
        if industry and industry.lower() not in query.lower():
            # Add relevant industry keywords
            industry_keywords = {
                'technology': ['tech', 'software', 'AI', 'startup'],
                'healthcare': ['biotech', 'pharma', 'medical', 'health'],
                'financial': ['fintech', 'banking', 'finance', 'investment'],
                'energy': ['renewable', 'oil', 'gas', 'solar', 'wind'],
                'real estate': ['property', 'REIT', 'development']
            }

            for key, keywords in industry_keywords.items():
                if key.lower() in industry.lower():
                    enhanced_terms.extend(keywords[:2])  # Add top 2 keywords
                    break

        # Add funding-related terms based on amount
        amount = filing_context.get('amount', 0)
        if amount:
            if amount >= ********0:  # $100M+
                enhanced_terms.append('mega round')
            elif amount >= ********:  # $50M+
                enhanced_terms.append('large funding')
            elif amount >= ********:  # $10M+
                enhanced_terms.append('Series A OR Series B')

        # Combine original query with enhanced terms
        if enhanced_terms:
            enhanced_query = f"{query} {' '.join(enhanced_terms[:3])}"  # Limit to 3 additional terms
            return enhanced_query

        return query

    def _search_google_news(self, query: str, max_results: int, days_back: int) -> List[Dict[str, Any]]:
        """
        Search Google News for articles.

        Args:
            query: Search query
            max_results: Maximum number of results
            days_back: How many days back to search

        Returns:
            List of article dictionaries
        """
        # Construct search URL
        encoded_query = requests.utils.quote(query)
        url = f"https://news.google.com/search?q={encoded_query}&hl=en-US&gl=US&ceid=US:en"

        try:
            # Send request
            response = self.session.get(url, timeout=10)
            response.raise_for_status()

            # Parse HTML
            soup = BeautifulSoup(response.text, "html.parser")
            articles = soup.select("article")

            results = []
            for article in articles[:max_results]:
                try:
                    # Extract article data
                    title_elem = article.select_one("h3 a")
                    time_elem = article.select_one("time")
                    source_elem = article.select_one("div[data-n-tid]")

                    if not title_elem:
                        continue

                    title = title_elem.text.strip()

                    # Extract URL
                    article_url = title_elem.get("href", "")
                    if article_url.startswith("./"):
                        article_url = "https://news.google.com/" + article_url[2:]

                    # Extract date
                    date = datetime.now().isoformat()
                    if time_elem and time_elem.get("datetime"):
                        date = datetime.fromisoformat(time_elem["datetime"]).isoformat()

                    # Extract source
                    source = "Google News"
                    if source_elem:
                        source = source_elem.text.strip()

                    # Check if within date range
                    article_date = datetime.fromisoformat(date)
                    if (datetime.now() - article_date).days > days_back:
                        continue

                    results.append({
                        "title": title,
                        "url": article_url,
                        "date": date,
                        "source": source,
                        "snippet": "",
                        "origin": "google_news"
                    })
                except Exception as e:
                    logger.error(f"Error parsing Google News article: {e}")

            return results
        except Exception as e:
            logger.error(f"Error searching Google News: {e}")
            return []

    def _search_bing_news(self, query: str, max_results: int, days_back: int) -> List[Dict[str, Any]]:
        """
        Search Bing News for articles.

        Args:
            query: Search query
            max_results: Maximum number of results
            days_back: How many days back to search

        Returns:
            List of article dictionaries
        """
        # Construct search URL
        encoded_query = requests.utils.quote(query)
        url = f"https://www.bing.com/news/search?q={encoded_query}&qft=interval%3D%227%22&form=PTFTNR"

        try:
            # Send request
            response = self.session.get(url, timeout=10)
            response.raise_for_status()

            # Parse HTML
            soup = BeautifulSoup(response.text, "html.parser")
            articles = soup.select(".news-card")

            results = []
            for article in articles[:max_results]:
                try:
                    # Extract article data
                    title_elem = article.select_one(".title")
                    snippet_elem = article.select_one(".snippet")
                    source_elem = article.select_one(".source")
                    time_elem = article.select_one(".source span:nth-child(2)")

                    if not title_elem:
                        continue

                    title = title_elem.text.strip()

                    # Extract URL
                    article_url = title_elem.get("href", "")

                    # Extract snippet
                    snippet = ""
                    if snippet_elem:
                        snippet = snippet_elem.text.strip()

                    # Extract source
                    source = "Bing News"
                    if source_elem:
                        source_text = source_elem.text.strip()
                        source = source_text.split("·")[0].strip()

                    # Extract date (approximate from relative time)
                    date = datetime.now().isoformat()
                    if time_elem:
                        time_text = time_elem.text.strip()
                        date = self._parse_relative_time(time_text)

                    results.append({
                        "title": title,
                        "url": article_url,
                        "date": date,
                        "source": source,
                        "snippet": snippet,
                        "origin": "bing_news"
                    })
                except Exception as e:
                    logger.error(f"Error parsing Bing News article: {e}")

            return results
        except Exception as e:
            logger.error(f"Error searching Bing News: {e}")
            return []

    def _search_yahoo_finance(self, query: str, max_results: int, days_back: int) -> List[Dict[str, Any]]:
        """
        Search Yahoo Finance for articles.

        Args:
            query: Search query
            max_results: Maximum number of results
            days_back: How many days back to search

        Returns:
            List of article dictionaries
        """
        # Construct search URL
        encoded_query = requests.utils.quote(query)
        url = f"https://finance.yahoo.com/lookup?s={encoded_query}"

        try:
            # Send request
            response = self.session.get(url, timeout=10)
            response.raise_for_status()

            # Parse HTML
            soup = BeautifulSoup(response.text, "html.parser")

            # Try to find ticker symbol
            ticker = None
            ticker_elem = soup.select_one("a[data-test='quoteLink']")
            if ticker_elem:
                ticker = ticker_elem.text.strip()

            # If ticker found, get news for that ticker
            if ticker:
                news_url = f"https://finance.yahoo.com/quote/{ticker}/news"
                news_response = self.session.get(news_url, timeout=10)
                news_response.raise_for_status()

                news_soup = BeautifulSoup(news_response.text, "html.parser")
                articles = news_soup.select("li[data-test='content-list-item']")

                results = []
                for article in articles[:max_results]:
                    try:
                        # Extract article data
                        title_elem = article.select_one("h3")
                        link_elem = article.select_one("a")
                        source_elem = article.select_one("div[data-test='content-meta'] span:first-child")
                        time_elem = article.select_one("div[data-test='content-meta'] span:nth-child(2)")

                        if not title_elem or not link_elem:
                            continue

                        title = title_elem.text.strip()

                        # Extract URL
                        article_url = link_elem.get("href", "")
                        if article_url.startswith("/"):
                            article_url = "https://finance.yahoo.com" + article_url

                        # Extract source
                        source = "Yahoo Finance"
                        if source_elem:
                            source = source_elem.text.strip()

                        # Extract date
                        date = datetime.now().isoformat()
                        if time_elem:
                            time_text = time_elem.text.strip()
                            date = self._parse_relative_time(time_text)

                        results.append({
                            "title": title,
                            "url": article_url,
                            "date": date,
                            "source": source,
                            "snippet": "",
                            "origin": "yahoo_finance",
                            "ticker": ticker
                        })
                    except Exception as e:
                        logger.error(f"Error parsing Yahoo Finance article: {e}")

                return results

            # If no ticker found, return empty results
            return []
        except Exception as e:
            logger.error(f"Error searching Yahoo Finance: {e}")
            return []

    def _extract_article_content(self, url: str) -> Optional[str]:
        """
        Extract the main content from an article.

        Args:
            url: URL of the article

        Returns:
            Extracted article content or None if extraction fails
        """
        try:
            # Send request
            response = self.session.get(url, timeout=15)
            response.raise_for_status()

            # Parse HTML
            soup = BeautifulSoup(response.text, "html.parser")

            # Remove unwanted elements
            for elem in soup.select("script, style, nav, header, footer, aside, [class*='ad'], [id*='ad'], [class*='banner'], [id*='banner']"):
                elem.decompose()

            # Try different content extraction strategies
            content = None

            # Strategy 1: Look for article tag
            article = soup.select_one("article")
            if article:
                content = article.get_text(separator="\n", strip=True)

            # Strategy 2: Look for common content containers
            if not content or len(content) < 100:
                for selector in [".content", "#content", ".article-content", ".post-content", ".entry-content", "[itemprop='articleBody']"]:
                    container = soup.select_one(selector)
                    if container:
                        content = container.get_text(separator="\n", strip=True)
                        if len(content) >= 100:
                            break

            # Strategy 3: Look for paragraphs
            if not content or len(content) < 100:
                paragraphs = [p.get_text(strip=True) for p in soup.select("p") if len(p.get_text(strip=True)) > 40]
                if paragraphs:
                    content = "\n\n".join(paragraphs)

            # Clean up content
            if content:
                # Remove excessive whitespace
                content = re.sub(r'\s+', ' ', content).strip()

                # Truncate if too long
                if len(content) > 5000:
                    content = content[:5000] + "..."

                return content

            return None
        except Exception as e:
            logger.error(f"Error extracting content from {url}: {e}")
            return None

    def _parse_relative_time(self, time_text: str) -> str:
        """
        Parse relative time (e.g., "2 hours ago") to ISO format.

        Args:
            time_text: Relative time text

        Returns:
            ISO formatted date string
        """
        now = datetime.now()

        # Handle "X minutes/hours ago"
        minutes_match = re.search(r'(\d+)\s+minute', time_text, re.IGNORECASE)
        if minutes_match:
            minutes = int(minutes_match.group(1))
            return (now - timedelta(minutes=minutes)).isoformat()

        hours_match = re.search(r'(\d+)\s+hour', time_text, re.IGNORECASE)
        if hours_match:
            hours = int(hours_match.group(1))
            return (now - timedelta(hours=hours)).isoformat()

        # Handle "X days ago"
        days_match = re.search(r'(\d+)\s+day', time_text, re.IGNORECASE)
        if days_match:
            days = int(days_match.group(1))
            return (now - timedelta(days=days)).isoformat()

        # Handle "yesterday"
        if "yesterday" in time_text.lower():
            return (now - timedelta(days=1)).isoformat()

        # Handle specific date formats
        try:
            # Try common date formats
            for fmt in ["%b %d, %Y", "%B %d, %Y", "%Y-%m-%d", "%d %b %Y", "%d %B %Y"]:
                try:
                    date = datetime.strptime(time_text, fmt)
                    return date.isoformat()
                except ValueError:
                    continue
        except:
            pass

        # Default to current time if parsing fails
        return now.isoformat()

    def _deduplicate_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Deduplicate news results based on title similarity.

        Args:
            results: List of news results

        Returns:
            Deduplicated list of results
        """
        if not results:
            return []

        # Use title as deduplication key
        seen_titles = set()
        unique_results = []

        for result in results:
            title = result.get("title", "").lower()

            # Skip if we've seen a very similar title
            if any(self._similarity(title, seen) > 0.8 for seen in seen_titles):
                continue

            seen_titles.add(title)
            unique_results.append(result)

        return unique_results

    def _similarity(self, str1: str, str2: str) -> float:
        """
        Calculate similarity between two strings.

        Args:
            str1: First string
            str2: Second string

        Returns:
            Similarity score (0-1)
        """
        # Simple Jaccard similarity
        set1 = set(str1.split())
        set2 = set(str2.split())

        if not set1 or not set2:
            return 0.0

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0

    def _get_cache_key(self, query: str, max_results: int, days_back: int, sources: Optional[List[str]]) -> str:
        """
        Generate a cache key for the query.

        Args:
            query: Search query
            max_results: Maximum number of results
            days_back: How many days back to search
            sources: List of news sources

        Returns:
            Cache key string
        """
        # Create a string representation of the parameters
        params = f"{query}|{max_results}|{days_back}|{','.join(sorted(sources or []))}"

        # Hash the parameters
        return hashlib.md5(params.encode()).hexdigest()

    def _get_from_cache(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """
        Get results from cache.

        Args:
            cache_key: Cache key

        Returns:
            Cached results or None if not found or expired
        """
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")

        if not os.path.exists(cache_file):
            return None

        try:
            with open(cache_file, 'r') as f:
                cached_data = json.load(f)

            # Check if cache is expired
            timestamp = datetime.fromisoformat(cached_data.get("timestamp", "2000-01-01"))
            cache_age = (datetime.now() - timestamp).total_seconds()

            if cache_age > cached_data.get("cache_ttl", 86400):
                logger.info(f"Cache expired for key: {cache_key}")
                return None

            return cached_data
        except Exception as e:
            logger.error(f"Error reading from cache: {e}")
            return None

    def _save_to_cache(self, cache_key: str, data: Dict[str, Any], cache_ttl: int = 86400) -> None:
        """
        Save results to cache.

        Args:
            cache_key: Cache key
            data: Data to cache
            cache_ttl: Cache time-to-live in seconds
        """
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")

        try:
            # Add cache TTL to data
            data["cache_ttl"] = cache_ttl

            with open(cache_file, 'w') as f:
                json.dump(data, f, indent=2)

            logger.info(f"Saved to cache: {cache_key}")
        except Exception as e:
            logger.error(f"Error saving to cache: {e}")

# Example usage
example_usage = """
# Get news about a company
news = registry.execute_tool("news_scraper",
                            query="Acme Corp venture capital funding",
                            max_results=3,
                            days_back=7)

# Print the results
for article in news["results"]:
    print(f"{article['title']} - {article['source']} ({article['date']})")
    print(article.get('snippet', ''))
    print()
"""
