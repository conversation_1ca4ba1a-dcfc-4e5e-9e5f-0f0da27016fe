#!/usr/bin/env python3
"""
GitHub MCP Server Tool

Provides GitHub integration for the MCP:
1. Monitors GitHub repositories for Form D related activity
2. Retrieves and processes GitHub issues and pull requests
3. Posts analysis results as GitHub comments
4. Syncs data between GitHub and local storage
"""

import os
import json
import logging
import time
import base64
import re
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
import requests

from mcp.tools.registry import Tool, ToolError

# Configure logging
logger = logging.getLogger(__name__)

class GitHubMCPServerTool(Tool):
    """Tool for GitHub integration with the MCP."""
    
    name = "github_mcp_server"
    description = "Provides GitHub integration for the MCP"
    version = "1.0.0"
    category = "server"
    
    def _setup(self):
        """Set up the GitHub MCP server tool."""
        self.token = os.environ.get("GITHUB_TOKEN")
        self.cache_dir = os.path.join("data", "cache", "github")
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # Initialize session
        self.session = requests.Session()
        if self.token:
            self.session.headers.update({
                "Authorization": f"token {self.token}",
                "Accept": "application/vnd.github.v3+json"
            })
        
        self.session.headers.update({
            "User-Agent": "Private-Signals-MCP/1.0"
        })
    
    def execute(self, 
               action: str,
               repo: Optional[str] = None,
               issue_number: Optional[int] = None,
               pr_number: Optional[int] = None,
               content: Optional[str] = None,
               query: Optional[str] = None,
               **kwargs) -> Dict[str, Any]:
        """
        Execute the GitHub MCP server tool.
        
        Args:
            action: Action to perform (get_issues, get_prs, post_comment, search, etc.)
            repo: Repository in format "owner/repo"
            issue_number: Issue number
            pr_number: Pull request number
            content: Content for comments or issues
            query: Search query
            **kwargs: Additional action-specific parameters
            
        Returns:
            Dictionary with action results
        """
        # Check if token is available
        if not self.token and action not in ["search", "get_repo", "get_issues", "get_prs"]:
            raise ToolError("GitHub token not available. Set GITHUB_TOKEN environment variable.")
        
        # Dispatch to appropriate method based on action
        if action == "get_issues":
            return self._get_issues(repo, **kwargs)
        elif action == "get_prs":
            return self._get_prs(repo, **kwargs)
        elif action == "get_issue":
            return self._get_issue(repo, issue_number, **kwargs)
        elif action == "get_pr":
            return self._get_pr(repo, pr_number, **kwargs)
        elif action == "post_comment":
            return self._post_comment(repo, issue_number or pr_number, content, **kwargs)
        elif action == "create_issue":
            return self._create_issue(repo, content, **kwargs)
        elif action == "search":
            return self._search(query, **kwargs)
        elif action == "get_repo":
            return self._get_repo(repo, **kwargs)
        elif action == "get_file":
            return self._get_file(repo, **kwargs)
        else:
            raise ToolError(f"Invalid action: {action}")
    
    def _get_issues(self, repo: str, state: str = "open", 
                   labels: Optional[List[str]] = None,
                   since: Optional[str] = None,
                   per_page: int = 30,
                   page: int = 1) -> Dict[str, Any]:
        """
        Get issues from a repository.
        
        Args:
            repo: Repository in format "owner/repo"
            state: Issue state (open, closed, all)
            labels: List of labels to filter by
            since: ISO 8601 timestamp to filter by
            per_page: Number of issues per page
            page: Page number
            
        Returns:
            Dictionary with issues and metadata
        """
        if not repo:
            raise ToolError("Repository is required")
        
        # Build query parameters
        params = {
            "state": state,
            "per_page": per_page,
            "page": page
        }
        
        if labels:
            params["labels"] = ",".join(labels)
        
        if since:
            params["since"] = since
        
        # Make request
        url = f"https://api.github.com/repos/{repo}/issues"
        response = self.session.get(url, params=params)
        
        if response.status_code != 200:
            raise ToolError(f"Error getting issues: {response.status_code} {response.text}")
        
        issues = response.json()
        
        # Filter out pull requests (GitHub API returns PRs as issues)
        issues = [issue for issue in issues if "pull_request" not in issue]
        
        return {
            "issues": issues,
            "count": len(issues),
            "repo": repo,
            "page": page,
            "per_page": per_page,
            "timestamp": datetime.now().isoformat()
        }
    
    def _get_prs(self, repo: str, state: str = "open",
                sort: str = "created",
                direction: str = "desc",
                per_page: int = 30,
                page: int = 1) -> Dict[str, Any]:
        """
        Get pull requests from a repository.
        
        Args:
            repo: Repository in format "owner/repo"
            state: PR state (open, closed, all)
            sort: Sort field (created, updated, popularity, long-running)
            direction: Sort direction (asc, desc)
            per_page: Number of PRs per page
            page: Page number
            
        Returns:
            Dictionary with PRs and metadata
        """
        if not repo:
            raise ToolError("Repository is required")
        
        # Build query parameters
        params = {
            "state": state,
            "sort": sort,
            "direction": direction,
            "per_page": per_page,
            "page": page
        }
        
        # Make request
        url = f"https://api.github.com/repos/{repo}/pulls"
        response = self.session.get(url, params=params)
        
        if response.status_code != 200:
            raise ToolError(f"Error getting pull requests: {response.status_code} {response.text}")
        
        prs = response.json()
        
        return {
            "pull_requests": prs,
            "count": len(prs),
            "repo": repo,
            "page": page,
            "per_page": per_page,
            "timestamp": datetime.now().isoformat()
        }
    
    def _get_issue(self, repo: str, issue_number: int) -> Dict[str, Any]:
        """
        Get a specific issue from a repository.
        
        Args:
            repo: Repository in format "owner/repo"
            issue_number: Issue number
            
        Returns:
            Dictionary with issue data
        """
        if not repo:
            raise ToolError("Repository is required")
        
        if not issue_number:
            raise ToolError("Issue number is required")
        
        # Make request
        url = f"https://api.github.com/repos/{repo}/issues/{issue_number}"
        response = self.session.get(url)
        
        if response.status_code != 200:
            raise ToolError(f"Error getting issue: {response.status_code} {response.text}")
        
        issue = response.json()
        
        # Get comments
        comments_url = issue["comments_url"]
        comments_response = self.session.get(comments_url)
        
        if comments_response.status_code == 200:
            comments = comments_response.json()
        else:
            comments = []
            logger.warning(f"Error getting issue comments: {comments_response.status_code}")
        
        return {
            "issue": issue,
            "comments": comments,
            "repo": repo,
            "timestamp": datetime.now().isoformat()
        }
    
    def _get_pr(self, repo: str, pr_number: int) -> Dict[str, Any]:
        """
        Get a specific pull request from a repository.
        
        Args:
            repo: Repository in format "owner/repo"
            pr_number: Pull request number
            
        Returns:
            Dictionary with PR data
        """
        if not repo:
            raise ToolError("Repository is required")
        
        if not pr_number:
            raise ToolError("Pull request number is required")
        
        # Make request
        url = f"https://api.github.com/repos/{repo}/pulls/{pr_number}"
        response = self.session.get(url)
        
        if response.status_code != 200:
            raise ToolError(f"Error getting pull request: {response.status_code} {response.text}")
        
        pr = response.json()
        
        # Get comments
        comments_url = f"https://api.github.com/repos/{repo}/issues/{pr_number}/comments"
        comments_response = self.session.get(comments_url)
        
        if comments_response.status_code == 200:
            comments = comments_response.json()
        else:
            comments = []
            logger.warning(f"Error getting PR comments: {comments_response.status_code}")
        
        # Get review comments
        review_comments_url = pr["review_comments_url"]
        review_comments_response = self.session.get(review_comments_url)
        
        if review_comments_response.status_code == 200:
            review_comments = review_comments_response.json()
        else:
            review_comments = []
            logger.warning(f"Error getting PR review comments: {review_comments_response.status_code}")
        
        return {
            "pull_request": pr,
            "comments": comments,
            "review_comments": review_comments,
            "repo": repo,
            "timestamp": datetime.now().isoformat()
        }
    
    def _post_comment(self, repo: str, number: int, content: str) -> Dict[str, Any]:
        """
        Post a comment to an issue or pull request.
        
        Args:
            repo: Repository in format "owner/repo"
            number: Issue or PR number
            content: Comment content
            
        Returns:
            Dictionary with comment data
        """
        if not repo:
            raise ToolError("Repository is required")
        
        if not number:
            raise ToolError("Issue or PR number is required")
        
        if not content:
            raise ToolError("Comment content is required")
        
        # Make request
        url = f"https://api.github.com/repos/{repo}/issues/{number}/comments"
        data = {"body": content}
        
        response = self.session.post(url, json=data)
        
        if response.status_code != 201:
            raise ToolError(f"Error posting comment: {response.status_code} {response.text}")
        
        comment = response.json()
        
        return {
            "comment": comment,
            "repo": repo,
            "number": number,
            "timestamp": datetime.now().isoformat()
        }
    
    def _create_issue(self, repo: str, title: str, body: str, 
                     labels: Optional[List[str]] = None,
                     assignees: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Create a new issue in a repository.
        
        Args:
            repo: Repository in format "owner/repo"
            title: Issue title
            body: Issue body
            labels: List of labels
            assignees: List of assignees
            
        Returns:
            Dictionary with issue data
        """
        if not repo:
            raise ToolError("Repository is required")
        
        if not title:
            raise ToolError("Issue title is required")
        
        # Build request data
        data = {
            "title": title,
            "body": body
        }
        
        if labels:
            data["labels"] = labels
        
        if assignees:
            data["assignees"] = assignees
        
        # Make request
        url = f"https://api.github.com/repos/{repo}/issues"
        response = self.session.post(url, json=data)
        
        if response.status_code != 201:
            raise ToolError(f"Error creating issue: {response.status_code} {response.text}")
        
        issue = response.json()
        
        return {
            "issue": issue,
            "repo": repo,
            "timestamp": datetime.now().isoformat()
        }
    
    def _search(self, query: str, type: str = "repositories",
               sort: Optional[str] = None,
               order: str = "desc",
               per_page: int = 30,
               page: int = 1) -> Dict[str, Any]:
        """
        Search GitHub.
        
        Args:
            query: Search query
            type: Search type (repositories, code, issues, users)
            sort: Sort field (depends on type)
            order: Sort order (asc, desc)
            per_page: Number of results per page
            page: Page number
            
        Returns:
            Dictionary with search results
        """
        if not query:
            raise ToolError("Search query is required")
        
        # Build query parameters
        params = {
            "q": query,
            "per_page": per_page,
            "page": page,
            "order": order
        }
        
        if sort:
            params["sort"] = sort
        
        # Make request
        url = f"https://api.github.com/search/{type}"
        response = self.session.get(url, params=params)
        
        if response.status_code != 200:
            raise ToolError(f"Error searching GitHub: {response.status_code} {response.text}")
        
        results = response.json()
        
        return {
            "results": results["items"],
            "total_count": results["total_count"],
            "query": query,
            "type": type,
            "page": page,
            "per_page": per_page,
            "timestamp": datetime.now().isoformat()
        }
    
    def _get_repo(self, repo: str) -> Dict[str, Any]:
        """
        Get repository information.
        
        Args:
            repo: Repository in format "owner/repo"
            
        Returns:
            Dictionary with repository data
        """
        if not repo:
            raise ToolError("Repository is required")
        
        # Make request
        url = f"https://api.github.com/repos/{repo}"
        response = self.session.get(url)
        
        if response.status_code != 200:
            raise ToolError(f"Error getting repository: {response.status_code} {response.text}")
        
        repo_data = response.json()
        
        return {
            "repository": repo_data,
            "timestamp": datetime.now().isoformat()
        }
    
    def _get_file(self, repo: str, path: str, ref: Optional[str] = None) -> Dict[str, Any]:
        """
        Get file content from a repository.
        
        Args:
            repo: Repository in format "owner/repo"
            path: File path
            ref: Git reference (branch, tag, commit)
            
        Returns:
            Dictionary with file data
        """
        if not repo:
            raise ToolError("Repository is required")
        
        if not path:
            raise ToolError("File path is required")
        
        # Build query parameters
        params = {}
        if ref:
            params["ref"] = ref
        
        # Make request
        url = f"https://api.github.com/repos/{repo}/contents/{path}"
        response = self.session.get(url, params=params)
        
        if response.status_code != 200:
            raise ToolError(f"Error getting file: {response.status_code} {response.text}")
        
        file_data = response.json()
        
        # Decode content if available
        if "content" in file_data and file_data.get("encoding") == "base64":
            content = base64.b64decode(file_data["content"]).decode("utf-8")
            file_data["decoded_content"] = content
        
        return {
            "file": file_data,
            "repo": repo,
            "path": path,
            "ref": ref,
            "timestamp": datetime.now().isoformat()
        }

# Example usage
example_usage = """
# Search for Form D related repositories
search_results = registry.execute_tool("github_mcp_server", 
                                      action="search",
                                      query="form d sec filing",
                                      type="repositories")

# Get issues from a repository
issues = registry.execute_tool("github_mcp_server",
                              action="get_issues",
                              repo="username/repo",
                              state="open",
                              labels=["form-d", "sec"])

# Post a comment with analysis results
comment = registry.execute_tool("github_mcp_server",
                               action="post_comment",
                               repo="username/repo",
                               number=123,
                               content="## Form D Analysis\\n\\nRelevance Score: 0.85\\n\\nThis filing appears to be significant...")
"""
