#!/usr/bin/env python3
"""
MCP Tool Orchestrator

Provides intelligent tool selection and orchestration capabilities for the LLM:
1. Tool-aware prompt generation
2. LLM-driven tool selection and execution
3. Intelligent tool chaining and workflow orchestration
4. Context-aware tool recommendations
"""

import json
import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)

class ToolOrchestrator:
    """
    Orchestrates tool selection and execution based on LLM decisions.
    """
    
    def __init__(self, registry, model=None):
        """
        Initialize the tool orchestrator.
        
        Args:
            registry: Tool registry instance
            model: LLM model instance for tool selection
        """
        self.registry = registry
        self.model = model
        
        # Tool selection prompt template
        self.tool_selection_template = """
You are an expert financial analyst with access to specialized tools for SEC Form D analysis.

# Available Tools
{tool_descriptions}

# Filing Context
{filing_context}

# Analysis Stage
Current stage: {analysis_stage}
Previous results: {previous_results}

# Task
Based on the filing context and analysis stage, determine which tools (if any) should be used to enhance the analysis.

Consider:
1. Filing characteristics (industry, amount, company type)
2. Analysis stage requirements
3. Available data and context
4. Tool capabilities and relevance

# Response Format
Respond in JSON format:
{{
  "tools_to_use": [
    {{
      "tool_name": "string",
      "reason": "string",
      "parameters": {{
        "param1": "value1",
        "param2": "value2"
      }},
      "priority": int  // 1-5, where 5 is highest priority
    }}
  ],
  "tool_chain": [
    {{
      "step": int,
      "tool_name": "string",
      "depends_on": ["tool_name1", "tool_name2"],  // Tools that must complete first
      "condition": "string"  // Optional condition for execution
    }}
  ],
  "reasoning": "string"  // Explanation of tool selection strategy
}}
"""

    def get_tool_descriptions(self) -> str:
        """
        Get formatted descriptions of available tools.
        
        Returns:
            Formatted tool descriptions for prompt inclusion
        """
        if not self.registry:
            return "No tools available."
        
        tools = self.registry.list_tools()
        descriptions = []
        
        for tool in tools:
            tool_obj = self.registry.get_tool(tool['name'])
            if tool_obj:
                schema = tool_obj.get_schema()
                
                # Extract key parameters
                key_params = []
                for param, details in schema.get('properties', {}).items():
                    if param in schema.get('required', []):
                        key_params.append(f"{param} (required)")
                    else:
                        key_params.append(f"{param} (optional)")
                
                description = f"""
**{tool['name']}** ({tool['category']})
- Purpose: {tool['description']}
- Key parameters: {', '.join(key_params[:3])}  # Show top 3 params
- Use cases: {self._get_tool_use_cases(tool['name'])}
"""
                descriptions.append(description)
        
        return '\n'.join(descriptions)
    
    def _get_tool_use_cases(self, tool_name: str) -> str:
        """Get specific use cases for a tool in SEC Form D analysis."""
        use_cases = {
            "news_scraper": "Find recent news about companies, funding rounds, industry trends",
            "github_mcp_server": "Track analysis progress, create issues for follow-up, collaborate on findings",
            "postgresql_mcp_server": "Query historical data, compare with similar filings, store analysis results",
            "microsoft365_mcp_server": "Send email alerts, schedule follow-up meetings, share analysis reports",
            "json_mcp_server": "Provide API access for external integrations, expose analysis data"
        }
        return use_cases.get(tool_name, "General purpose tool for SEC Form D analysis")
    
    def select_tools(self, 
                    filing_entry: Dict[str, Any],
                    analysis_stage: str,
                    previous_results: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Use LLM to select appropriate tools for the current context.
        
        Args:
            filing_entry: Current filing being analyzed
            analysis_stage: Current analysis stage (screening, detailed_analysis, action_generation)
            previous_results: Results from previous analysis stages
            
        Returns:
            Tool selection decisions and execution plan
        """
        if not self.model:
            logger.warning("No model available for tool selection")
            return {"tools_to_use": [], "tool_chain": [], "reasoning": "No model available"}
        
        # Format filing context
        filing_context = self._format_filing_context(filing_entry)
        
        # Format previous results
        prev_results_str = json.dumps(previous_results, indent=2) if previous_results else "None"
        
        # Create tool selection prompt
        prompt = self.tool_selection_template.format(
            tool_descriptions=self.get_tool_descriptions(),
            filing_context=filing_context,
            analysis_stage=analysis_stage,
            previous_results=prev_results_str
        )
        
        try:
            # Get LLM decision
            llm_output = self.model.generate(prompt)
            
            # Parse the response
            tool_decisions = self._parse_tool_selection(llm_output)
            
            logger.info(f"LLM selected {len(tool_decisions.get('tools_to_use', []))} tools for {analysis_stage}")
            
            return tool_decisions
            
        except Exception as e:
            logger.error(f"Error in tool selection: {e}")
            return {"tools_to_use": [], "tool_chain": [], "reasoning": f"Error: {str(e)}"}
    
    def execute_tool_chain(self, 
                          tool_decisions: Dict[str, Any],
                          filing_entry: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the selected tools in the determined order.
        
        Args:
            tool_decisions: Tool selection decisions from select_tools()
            filing_entry: Filing entry being processed
            
        Returns:
            Combined results from all tool executions
        """
        if not self.registry:
            logger.warning("No registry available for tool execution")
            return {}
        
        results = {}
        executed_tools = set()
        
        # Sort tools by priority
        tools_to_use = sorted(
            tool_decisions.get('tools_to_use', []),
            key=lambda x: x.get('priority', 0),
            reverse=True
        )
        
        # Execute tools in priority order, respecting dependencies
        for tool_spec in tools_to_use:
            tool_name = tool_spec.get('tool_name')
            
            if not tool_name or tool_name in executed_tools:
                continue
            
            # Check dependencies
            dependencies = self._get_tool_dependencies(tool_name, tool_decisions.get('tool_chain', []))
            if not all(dep in executed_tools for dep in dependencies):
                logger.info(f"Skipping {tool_name} - dependencies not met: {dependencies}")
                continue
            
            try:
                # Execute the tool
                logger.info(f"Executing tool: {tool_name}")
                
                # Enhance parameters with context
                enhanced_params = self._enhance_tool_parameters(
                    tool_spec.get('parameters', {}),
                    filing_entry,
                    results
                )
                
                # Execute tool
                tool_result = self.registry.execute_tool(tool_name, **enhanced_params)
                
                # Store result
                results[tool_name] = {
                    "result": tool_result,
                    "reason": tool_spec.get('reason', ''),
                    "execution_time": datetime.now().isoformat()
                }
                
                executed_tools.add(tool_name)
                
                logger.info(f"Successfully executed {tool_name}")
                
            except Exception as e:
                logger.error(f"Error executing tool {tool_name}: {e}")
                results[tool_name] = {
                    "error": str(e),
                    "reason": tool_spec.get('reason', ''),
                    "execution_time": datetime.now().isoformat()
                }
        
        return results
    
    def _format_filing_context(self, filing_entry: Dict[str, Any]) -> str:
        """Format filing entry for prompt inclusion."""
        return f"""
Title: {filing_entry.get('title', 'Unknown')}
Issuer: {filing_entry.get('issuer_name', 'Unknown')}
Industry: {filing_entry.get('industry_group', 'Unknown')}
Offering Amount: ${filing_entry.get('offering_amount', 0):,.2f}
Filing Date: {filing_entry.get('filing_date', 'Unknown')}
Description: {filing_entry.get('description', 'No description available')[:200]}...
"""
    
    def _parse_tool_selection(self, llm_output: str) -> Dict[str, Any]:
        """Parse LLM output for tool selection decisions."""
        try:
            # Try to extract JSON from the output
            json_match = re.search(r'```json\s*(.*?)\s*```', llm_output, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                json_match = re.search(r'({.*})', llm_output, re.DOTALL)
                if json_match:
                    json_str = json_match.group(1)
                else:
                    json_str = llm_output
            
            parsed = json.loads(json_str)
            
            # Validate structure
            if not isinstance(parsed.get('tools_to_use'), list):
                parsed['tools_to_use'] = []
            
            if not isinstance(parsed.get('tool_chain'), list):
                parsed['tool_chain'] = []
            
            return parsed
            
        except Exception as e:
            logger.error(f"Error parsing tool selection: {e}")
            return {
                "tools_to_use": [],
                "tool_chain": [],
                "reasoning": f"Failed to parse LLM output: {str(e)}"
            }
    
    def _get_tool_dependencies(self, tool_name: str, tool_chain: List[Dict[str, Any]]) -> List[str]:
        """Get dependencies for a specific tool from the tool chain."""
        for step in tool_chain:
            if step.get('tool_name') == tool_name:
                return step.get('depends_on', [])
        return []
    
    def _enhance_tool_parameters(self, 
                                base_params: Dict[str, Any],
                                filing_entry: Dict[str, Any],
                                previous_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhance tool parameters with context from filing and previous results.
        
        Args:
            base_params: Base parameters from LLM decision
            filing_entry: Current filing entry
            previous_results: Results from previously executed tools
            
        Returns:
            Enhanced parameters
        """
        enhanced = base_params.copy()
        
        # Add common context parameters
        if 'query' in enhanced and not enhanced['query']:
            # Auto-generate query for news scraper
            company_name = filing_entry.get('issuer_name', '')
            industry = filing_entry.get('industry_group', '')
            enhanced['query'] = f"{company_name} {industry} funding investment"
        
        # Add filing-specific parameters
        enhanced['_filing_context'] = {
            'issuer_name': filing_entry.get('issuer_name'),
            'industry': filing_entry.get('industry_group'),
            'amount': filing_entry.get('offering_amount'),
            'filing_date': filing_entry.get('filing_date')
        }
        
        return enhanced
