#!/usr/bin/env python3
"""
Analysis Templates for Financial Analysis

Comprehensive collection of analysis templates for different types of
financial analysis, from quick screening to detailed investment memos.
"""

from typing import Dict, List, Any, Optional

class AnalysisTemplates:
    """
    Collection of analysis templates for different analysis types and depths.
    """
    
    def __init__(self):
        """Initialize the analysis templates collection."""
        self.templates = self._load_all_templates()
    
    def _load_all_templates(self) -> Dict[str, str]:
        """Load all available analysis templates."""
        return {
            "executive_summary": self._get_executive_summary_template(),
            "investment_memo": self._get_investment_memo_template(),
            "risk_assessment": self._get_risk_assessment_template(),
            "market_analysis": self._get_market_analysis_template(),
            "financial_analysis": self._get_financial_analysis_template(),
            "competitive_analysis": self._get_competitive_analysis_template(),
            "management_assessment": self._get_management_assessment_template(),
            "quick_screen": self._get_quick_screen_template(),
            "sector_deep_dive": self._get_sector_deep_dive_template(),
            "valuation_analysis": self._get_valuation_analysis_template()
        }
    
    def _get_executive_summary_template(self) -> str:
        """Executive summary template for high-level overview."""
        return """
# EXECUTIVE SUMMARY

## Investment Recommendation
**Recommendation:** [STRONG BUY / BUY / HOLD / PASS]
**Confidence Level:** [1-10 scale]
**Target Investment:** $[Amount]

## Investment Thesis (2-3 sentences)
[Concise statement of why this is an attractive investment opportunity, highlighting the key value drivers and competitive advantages]

## Key Investment Highlights
- **Market Opportunity:** [Size and growth of addressable market]
- **Competitive Position:** [Unique advantages and differentiation]
- **Financial Profile:** [Key financial metrics and projections]
- **Management Team:** [Experience and track record]
- **Risk/Return Profile:** [Expected returns vs. key risks]

## Financial Summary
- **Offering Amount:** $[Amount]
- **Valuation:** $[Pre/Post-money valuation]
- **Revenue (Current/Projected):** $[Current] / $[Projected]
- **Key Metrics:** [Industry-specific KPIs]

## Primary Risks
1. **[Risk Category]:** [Brief description and mitigation]
2. **[Risk Category]:** [Brief description and mitigation]
3. **[Risk Category]:** [Brief description and mitigation]

## Next Steps
- [Immediate actions required]
- [Information gaps to address]
- [Timeline for decision]
"""
    
    def _get_investment_memo_template(self) -> str:
        """Comprehensive investment memo template."""
        return """
# INVESTMENT MEMORANDUM

## EXECUTIVE SUMMARY
[High-level overview with recommendation and key points]

## COMPANY OVERVIEW
### Business Description
[What the company does, products/services, business model]

### Key Statistics
- **Founded:** [Year]
- **Headquarters:** [Location]
- **Employees:** [Number]
- **Current Revenue:** $[Amount]
- **Funding History:** [Previous rounds and investors]

## MARKET ANALYSIS
### Market Size and Dynamics
- **Total Addressable Market (TAM):** $[Amount]
- **Serviceable Addressable Market (SAM):** $[Amount]
- **Serviceable Obtainable Market (SOM):** $[Amount]
- **Market Growth Rate:** [Percentage annually]

### Market Trends
[Key trends driving market growth and adoption]

### Competitive Landscape
[Major competitors, market share, competitive positioning]

## BUSINESS MODEL ANALYSIS
### Revenue Streams
[How the company makes money, pricing model, unit economics]

### Key Metrics
[Industry-specific KPIs and performance indicators]

### Scalability Assessment
[Ability to grow efficiently, operational leverage]

## FINANCIAL ANALYSIS
### Historical Performance
[Revenue, profitability, cash flow trends]

### Financial Projections
[3-5 year projections with assumptions]

### Valuation Analysis
[Multiple valuation methodologies and range]

### Capital Requirements
[Funding needs, use of proceeds, runway analysis]

## MANAGEMENT TEAM
### Leadership Assessment
[CEO, key executives, board composition]

### Track Record
[Previous experience, successes, relevant expertise]

### Incentive Alignment
[Equity ownership, compensation structure]

## RISK ANALYSIS
### Technology/Product Risks
[Development, scalability, obsolescence risks]

### Market/Competitive Risks
[Market adoption, competitive threats, timing]

### Financial/Operational Risks
[Cash flow, funding, execution risks]

### Regulatory/Legal Risks
[Compliance, IP, litigation risks]

## INVESTMENT TERMS
### Proposed Investment
- **Investment Amount:** $[Amount]
- **Valuation:** $[Pre/Post-money]
- **Ownership Percentage:** [Percentage]
- **Security Type:** [Equity, convertible, etc.]

### Key Terms
[Liquidation preference, anti-dilution, board seats, etc.]

## RECOMMENDATION
### Investment Decision
[Final recommendation with rationale]

### Expected Returns
[IRR, multiple, timeline expectations]

### Key Success Factors
[What needs to go right for success]

### Monitoring Plan
[Key metrics and milestones to track]
"""
    
    def _get_risk_assessment_template(self) -> str:
        """Detailed risk assessment template."""
        return """
# COMPREHENSIVE RISK ASSESSMENT

## RISK IDENTIFICATION MATRIX

### Technology/Product Risks
| Risk Factor | Probability | Impact | Severity | Mitigation |
|-------------|-------------|---------|----------|------------|
| [Risk 1] | [H/M/L] | [H/M/L] | [Critical/High/Medium/Low] | [Strategy] |
| [Risk 2] | [H/M/L] | [H/M/L] | [Critical/High/Medium/Low] | [Strategy] |

### Market/Competitive Risks
| Risk Factor | Probability | Impact | Severity | Mitigation |
|-------------|-------------|---------|----------|------------|
| [Risk 1] | [H/M/L] | [H/M/L] | [Critical/High/Medium/Low] | [Strategy] |
| [Risk 2] | [H/M/L] | [H/M/L] | [Critical/High/Medium/Low] | [Strategy] |

### Financial/Operational Risks
| Risk Factor | Probability | Impact | Severity | Mitigation |
|-------------|-------------|---------|----------|------------|
| [Risk 1] | [H/M/L] | [H/M/L] | [Critical/High/Medium/Low] | [Strategy] |
| [Risk 2] | [H/M/L] | [H/M/L] | [Critical/High/Medium/Low] | [Strategy] |

### Regulatory/Legal Risks
| Risk Factor | Probability | Impact | Severity | Mitigation |
|-------------|-------------|---------|----------|------------|
| [Risk 1] | [H/M/L] | [H/M/L] | [Critical/High/Medium/Low] | [Strategy] |
| [Risk 2] | [H/M/L] | [H/M/L] | [Critical/High/Medium/Low] | [Strategy] |

## SCENARIO ANALYSIS

### Bull Case (30% probability)
- **Key Assumptions:** [What goes very well]
- **Financial Impact:** [Revenue/valuation upside]
- **Return Expectations:** [IRR/Multiple]

### Base Case (50% probability)
- **Key Assumptions:** [Most likely scenario]
- **Financial Impact:** [Expected performance]
- **Return Expectations:** [IRR/Multiple]

### Bear Case (20% probability)
- **Key Assumptions:** [What goes wrong]
- **Financial Impact:** [Downside scenario]
- **Return Expectations:** [IRR/Multiple or loss]

## RISK MITIGATION STRATEGIES

### Immediate Actions
1. [Action 1 with timeline]
2. [Action 2 with timeline]
3. [Action 3 with timeline]

### Ongoing Monitoring
- **Key Risk Indicators:** [Metrics to track]
- **Review Frequency:** [Monthly/Quarterly]
- **Escalation Triggers:** [When to take action]

### Contingency Planning
- **Exit Strategies:** [How to minimize losses]
- **Hedging Options:** [Risk reduction mechanisms]
- **Insurance Coverage:** [Risk transfer options]

## OVERALL RISK ASSESSMENT
- **Risk-Adjusted Return:** [Expected return considering risks]
- **Risk Rating:** [1-10 scale]
- **Recommendation:** [Proceed/Modify/Decline with rationale]
"""
    
    def _get_market_analysis_template(self) -> str:
        """Market analysis template."""
        return """
# MARKET ANALYSIS

## MARKET SIZING
### Total Addressable Market (TAM)
- **Size:** $[Amount]
- **Growth Rate:** [Percentage annually]
- **Key Drivers:** [Factors driving growth]
- **Methodology:** [How calculated]

### Serviceable Addressable Market (SAM)
- **Size:** $[Amount]
- **Penetration Strategy:** [How to address]
- **Geographic Focus:** [Target markets]
- **Customer Segments:** [Target customers]

### Serviceable Obtainable Market (SOM)
- **Size:** $[Amount]
- **Market Share Target:** [Realistic capture]
- **Timeline:** [Years to achieve]
- **Competitive Positioning:** [How to win]

## MARKET DYNAMICS
### Growth Drivers
1. [Driver 1 with impact assessment]
2. [Driver 2 with impact assessment]
3. [Driver 3 with impact assessment]

### Market Trends
- **Technology Trends:** [Enabling technologies]
- **Regulatory Trends:** [Policy changes]
- **Consumer Trends:** [Behavior shifts]
- **Economic Trends:** [Macro factors]

### Market Maturity
- **Adoption Curve Stage:** [Early/Growth/Mature]
- **Customer Sophistication:** [Level of market education]
- **Infrastructure Readiness:** [Supporting ecosystem]

## COMPETITIVE LANDSCAPE
### Direct Competitors
| Competitor | Market Share | Strengths | Weaknesses | Threat Level |
|------------|--------------|-----------|------------|--------------|
| [Company 1] | [Percentage] | [Key strengths] | [Vulnerabilities] | [H/M/L] |
| [Company 2] | [Percentage] | [Key strengths] | [Vulnerabilities] | [H/M/L] |

### Indirect Competitors
[Alternative solutions and substitutes]

### Competitive Positioning
- **Differentiation:** [Unique value proposition]
- **Competitive Moats:** [Sustainable advantages]
- **Barriers to Entry:** [Protection from new entrants]

## CUSTOMER ANALYSIS
### Target Customer Segments
1. **[Segment 1]**
   - Size: [Number of potential customers]
   - Characteristics: [Demographics, needs]
   - Buying Process: [Decision-making process]
   - Price Sensitivity: [Willingness to pay]

2. **[Segment 2]**
   - Size: [Number of potential customers]
   - Characteristics: [Demographics, needs]
   - Buying Process: [Decision-making process]
   - Price Sensitivity: [Willingness to pay]

### Customer Acquisition
- **Channels:** [How to reach customers]
- **Cost of Acquisition:** [CAC by channel]
- **Conversion Rates:** [Funnel metrics]
- **Retention Rates:** [Customer stickiness]

## MARKET OPPORTUNITY ASSESSMENT
### Attractiveness Score
- **Market Size:** [Score 1-10]
- **Growth Rate:** [Score 1-10]
- **Competitive Intensity:** [Score 1-10]
- **Barriers to Entry:** [Score 1-10]
- **Overall Score:** [Weighted average]

### Strategic Recommendations
1. [Recommendation 1 with rationale]
2. [Recommendation 2 with rationale]
3. [Recommendation 3 with rationale]
"""
    
    def _get_financial_analysis_template(self) -> str:
        """Financial analysis template."""
        return """
# FINANCIAL ANALYSIS

## HISTORICAL PERFORMANCE
### Revenue Analysis
- **Current Revenue:** $[Amount]
- **3-Year CAGR:** [Percentage]
- **Revenue Mix:** [Breakdown by product/service]
- **Seasonality:** [Quarterly patterns]

### Profitability Analysis
- **Gross Margin:** [Percentage and trend]
- **Operating Margin:** [Percentage and trend]
- **EBITDA Margin:** [Percentage and trend]
- **Net Margin:** [Percentage and trend]

### Cash Flow Analysis
- **Operating Cash Flow:** $[Amount and trend]
- **Free Cash Flow:** $[Amount and trend]
- **Cash Conversion Cycle:** [Days]
- **Working Capital:** [Requirements and efficiency]

## FINANCIAL PROJECTIONS
### Revenue Forecast (5-Year)
| Year | Revenue | Growth Rate | Key Assumptions |
|------|---------|-------------|-----------------|
| Year 1 | $[Amount] | [Percentage] | [Assumptions] |
| Year 2 | $[Amount] | [Percentage] | [Assumptions] |
| Year 3 | $[Amount] | [Percentage] | [Assumptions] |
| Year 4 | $[Amount] | [Percentage] | [Assumptions] |
| Year 5 | $[Amount] | [Percentage] | [Assumptions] |

### Profitability Forecast
| Year | Gross Margin | EBITDA Margin | Net Margin |
|------|--------------|---------------|------------|
| Year 1 | [Percentage] | [Percentage] | [Percentage] |
| Year 2 | [Percentage] | [Percentage] | [Percentage] |
| Year 3 | [Percentage] | [Percentage] | [Percentage] |
| Year 4 | [Percentage] | [Percentage] | [Percentage] |
| Year 5 | [Percentage] | [Percentage] | [Percentage] |

## UNIT ECONOMICS
### Key Metrics
- **Customer Acquisition Cost (CAC):** $[Amount]
- **Lifetime Value (LTV):** $[Amount]
- **LTV/CAC Ratio:** [Ratio]
- **Payback Period:** [Months]
- **Gross Margin per Unit:** [Percentage or amount]

### Scalability Analysis
- **Fixed vs. Variable Costs:** [Breakdown]
- **Operating Leverage:** [Sensitivity analysis]
- **Marginal Cost of Growth:** [Cost to scale]

## CAPITAL REQUIREMENTS
### Funding Needs
- **Current Cash Position:** $[Amount]
- **Monthly Burn Rate:** $[Amount]
- **Runway:** [Months]
- **Additional Capital Needed:** $[Amount]

### Use of Proceeds
1. **[Category 1]:** $[Amount] ([Percentage])
2. **[Category 2]:** $[Amount] ([Percentage])
3. **[Category 3]:** $[Amount] ([Percentage])

## VALUATION ANALYSIS
### Methodology 1: [Method Name]
- **Approach:** [Description]
- **Key Assumptions:** [Critical inputs]
- **Valuation Range:** $[Low] - $[High]

### Methodology 2: [Method Name]
- **Approach:** [Description]
- **Key Assumptions:** [Critical inputs]
- **Valuation Range:** $[Low] - $[High]

### Methodology 3: [Method Name]
- **Approach:** [Description]
- **Key Assumptions:** [Critical inputs]
- **Valuation Range:** $[Low] - $[High]

### Valuation Summary
- **Low Case:** $[Amount]
- **Base Case:** $[Amount]
- **High Case:** $[Amount]
- **Recommended Valuation:** $[Amount]

## FINANCIAL RISKS
### Key Sensitivities
1. **Revenue Growth:** [Impact of ±10% change]
2. **Gross Margin:** [Impact of ±2% change]
3. **Customer Acquisition:** [Impact of ±20% CAC change]

### Stress Testing
- **Recession Scenario:** [Impact on financials]
- **Competitive Pressure:** [Margin compression impact]
- **Funding Delay:** [Cash management implications]
"""
    
    def _get_quick_screen_template(self) -> str:
        """Quick screening template for initial assessment."""
        return """
# QUICK INVESTMENT SCREEN

## BASIC INFORMATION
- **Company:** [Name]
- **Industry:** [Sector]
- **Stage:** [Early/Growth/Late]
- **Offering Amount:** $[Amount]
- **Location:** [City, State]

## INITIAL ASSESSMENT (Pass/Fail)

### Market Opportunity ✓/✗
- **Market Size:** [Large/Medium/Small]
- **Growth Rate:** [High/Medium/Low]
- **Timing:** [Early/Optimal/Late]
- **Assessment:** [Pass/Fail with brief reason]

### Business Model ✓/✗
- **Revenue Model:** [Scalable/Linear/Unclear]
- **Unit Economics:** [Attractive/Acceptable/Poor]
- **Differentiation:** [Strong/Moderate/Weak]
- **Assessment:** [Pass/Fail with brief reason]

### Team & Execution ✓/✗
- **Management Experience:** [Strong/Adequate/Weak]
- **Track Record:** [Proven/Mixed/Unproven]
- **Domain Expertise:** [Deep/Adequate/Limited]
- **Assessment:** [Pass/Fail with brief reason]

### Financial Profile ✓/✗
- **Revenue Traction:** [Strong/Moderate/Weak]
- **Growth Rate:** [High/Moderate/Low]
- **Capital Efficiency:** [High/Moderate/Low]
- **Assessment:** [Pass/Fail with brief reason]

### Competitive Position ✓/✗
- **Market Position:** [Leader/Challenger/Follower]
- **Competitive Moats:** [Strong/Moderate/Weak]
- **Barriers to Entry:** [High/Medium/Low]
- **Assessment:** [Pass/Fail with brief reason]

## SCREENING RESULT
**Overall Assessment:** [PASS/FAIL]
**Confidence Level:** [High/Medium/Low]
**Relevance Score:** [0.0-1.0]

## KEY HIGHLIGHTS
### Strengths
1. [Primary strength]
2. [Secondary strength]
3. [Additional strength]

### Concerns
1. [Primary concern]
2. [Secondary concern]
3. [Additional concern]

## RECOMMENDATION
**Next Steps:** [Proceed to full analysis/Request more information/Pass]
**Priority Level:** [High/Medium/Low]
**Timeline:** [Immediate/1-2 weeks/1 month]

## INFORMATION GAPS
1. [Critical information needed]
2. [Important information needed]
3. [Nice-to-have information]
"""
    
    def get_template(self, template_name: str) -> Optional[str]:
        """Get a specific analysis template."""
        return self.templates.get(template_name)
    
    def get_all_templates(self) -> Dict[str, str]:
        """Get all available templates."""
        return self.templates
    
    def format_template(self, template_name: str, **kwargs) -> str:
        """Format a template with provided values."""
        template = self.get_template(template_name)
        if not template:
            return f"Template '{template_name}' not found."
        
        try:
            return template.format(**kwargs)
        except KeyError as e:
            return f"Missing required parameter for template: {e}"
    
    def get_template_list(self) -> List[str]:
        """Get list of available template names."""
        return list(self.templates.keys())

    def _get_competitive_analysis_template(self) -> str:
        """Competitive analysis template."""
        return """
# COMPETITIVE ANALYSIS

## COMPETITIVE LANDSCAPE OVERVIEW
- **Market Structure:** [Fragmented/Consolidated/Emerging]
- **Competitive Intensity:** [High/Medium/Low]
- **Barriers to Entry:** [High/Medium/Low]

## DIRECT COMPETITORS
| Competitor | Market Share | Strengths | Weaknesses | Threat Level |
|------------|--------------|-----------|------------|--------------|
| [Company 1] | [%] | [Key strengths] | [Vulnerabilities] | [H/M/L] |
| [Company 2] | [%] | [Key strengths] | [Vulnerabilities] | [H/M/L] |

## COMPETITIVE POSITIONING
- **Differentiation:** [Unique value proposition]
- **Competitive Advantages:** [Sustainable moats]
- **Market Position:** [Leader/Challenger/Follower/Niche]

## COMPETITIVE RESPONSE ANALYSIS
- **Likely Responses:** [How competitors might react]
- **Defensive Strategies:** [How to protect position]
- **Offensive Opportunities:** [How to gain share]
"""

    def _get_management_assessment_template(self) -> str:
        """Management assessment template."""
        return """
# MANAGEMENT TEAM ASSESSMENT

## LEADERSHIP TEAM
### CEO Assessment
- **Name:** [Name]
- **Background:** [Previous experience]
- **Track Record:** [Successes and failures]
- **Leadership Style:** [Assessment]

### Key Executives
| Role | Name | Experience | Assessment |
|------|------|------------|------------|
| [CTO] | [Name] | [Background] | [Evaluation] |
| [CFO] | [Name] | [Background] | [Evaluation] |

## TEAM EVALUATION
- **Domain Expertise:** [Relevant industry experience]
- **Execution Capability:** [Track record of delivery]
- **Strategic Vision:** [Clarity and alignment]
- **Cultural Fit:** [Team dynamics and values]

## GOVERNANCE
- **Board Composition:** [Independent directors, expertise]
- **Compensation Structure:** [Alignment with performance]
- **Decision-Making Process:** [Efficiency and transparency]
"""

    def _get_sector_deep_dive_template(self) -> str:
        """Sector deep dive template."""
        return """
# SECTOR DEEP DIVE ANALYSIS

## INDUSTRY OVERVIEW
- **Industry Definition:** [Scope and boundaries]
- **Market Size:** [Current and projected]
- **Growth Drivers:** [Key factors driving expansion]
- **Industry Life Cycle:** [Emerging/Growth/Mature/Declining]

## INDUSTRY DYNAMICS
- **Value Chain Analysis:** [Key participants and relationships]
- **Profit Pool Distribution:** [Where value is captured]
- **Technology Trends:** [Disruptive forces]
- **Regulatory Environment:** [Current and expected changes]

## SECTOR-SPECIFIC METRICS
- **Key Performance Indicators:** [Industry-standard metrics]
- **Benchmarking Data:** [Peer comparison metrics]
- **Valuation Multiples:** [Typical trading ranges]

## INVESTMENT IMPLICATIONS
- **Sector Attractiveness:** [Overall investment appeal]
- **Key Success Factors:** [What drives outperformance]
- **Risk Factors:** [Sector-specific risks]
"""

    def _get_valuation_analysis_template(self) -> str:
        """Valuation analysis template."""
        return """
# COMPREHENSIVE VALUATION ANALYSIS

## VALUATION METHODOLOGY SELECTION
- **Primary Method:** [DCF/Multiples/Asset-based]
- **Secondary Methods:** [Supporting approaches]
- **Rationale:** [Why these methods are appropriate]

## DISCOUNTED CASH FLOW ANALYSIS
### Key Assumptions
- **Revenue Growth:** [5-year CAGR]
- **Margin Progression:** [Gross/Operating/Net]
- **Capital Requirements:** [CapEx and Working Capital]
- **Terminal Growth Rate:** [Long-term assumption]
- **Discount Rate (WACC):** [Cost of capital]

### Valuation Results
- **Enterprise Value:** $[Amount]
- **Equity Value:** $[Amount]
- **Per Share Value:** $[Amount]

## COMPARABLE COMPANY ANALYSIS
### Trading Multiples
| Metric | Company | Multiple | Median | Our Company |
|--------|---------|----------|---------|-------------|
| EV/Revenue | [Comps] | [Range] | [Median] | [Implied Value] |
| EV/EBITDA | [Comps] | [Range] | [Median] | [Implied Value] |

## SENSITIVITY ANALYSIS
- **Revenue Growth ±2%:** [Impact on valuation]
- **EBITDA Margin ±1%:** [Impact on valuation]
- **WACC ±0.5%:** [Impact on valuation]

## VALUATION SUMMARY
- **Low Case:** $[Amount]
- **Base Case:** $[Amount]
- **High Case:** $[Amount]
- **Recommended Value:** $[Amount]
"""
