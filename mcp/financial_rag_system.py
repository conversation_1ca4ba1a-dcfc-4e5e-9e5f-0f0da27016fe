#!/usr/bin/env python3
"""
Financial RAG (Retrieval-Augmented Generation) System

Integrates the financial knowledge base with the existing vector store and MCP architecture
to provide context-aware financial analysis with professional-grade expertise.
"""

import json
import logging
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

# Import existing components
from vector_store.embed import embedder, client
from db.supabase_manager import SupabaseDatabaseManager
from mcp.financial_knowledge_base import FinancialKnowledgeBase

class FinancialRAGSystem:
    """
    Enhanced RAG system for financial analysis with professional expertise.
    """
    
    def __init__(self, data_dir: str = "data/financial_kb"):
        """Initialize the Financial RAG system."""
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize components
        self.knowledge_base = FinancialKnowledgeBase(data_dir)
        self.db = SupabaseDatabaseManager()
        
        # Initialize vector store components
        self.embedder = embedder
        self.vector_client = client
        
        # Get or create financial knowledge collection
        try:
            self.financial_collection = self.vector_client.get_or_create_collection("financial_knowledge")
            self._initialize_financial_embeddings()
        except Exception as e:
            logging.warning(f"Could not initialize financial vector collection: {e}")
            self.financial_collection = None
        
        logging.info("Financial RAG System initialized successfully")
    
    def _initialize_financial_embeddings(self):
        """Initialize embeddings for financial knowledge base content."""
        try:
            # Check if embeddings already exist
            count = self.financial_collection.count()
            if count > 0:
                logging.info(f"Financial knowledge embeddings already exist: {count} items")
                return
            
            # Create embeddings for all knowledge base content
            self._embed_knowledge_base_content()
            
        except Exception as e:
            logging.error(f"Error initializing financial embeddings: {e}")
    
    def _embed_knowledge_base_content(self):
        """Embed all financial knowledge base content for semantic search."""
        documents = []
        metadatas = []
        ids = []
        
        # Embed valuation frameworks
        for framework_name, framework_data in self.knowledge_base.valuation_frameworks.items():
            doc_text = f"{framework_data['name']}: {framework_data['description']}"
            if 'best_for' in framework_data:
                doc_text += f" Best for: {', '.join(framework_data['best_for'])}"
            
            documents.append(doc_text)
            metadatas.append({
                "type": "valuation_framework",
                "name": framework_name,
                "category": "valuation"
            })
            ids.append(f"valuation_{framework_name}")
        
        # Embed risk frameworks
        for risk_name, risk_data in self.knowledge_base.risk_frameworks.items():
            doc_text = f"{risk_data['description']} Risk factors: {', '.join(risk_data['risk_factors'])}"
            
            documents.append(doc_text)
            metadatas.append({
                "type": "risk_framework",
                "name": risk_name,
                "category": "risk"
            })
            ids.append(f"risk_{risk_name}")
        
        # Embed industry templates
        for industry_name, industry_data in self.knowledge_base.industry_templates.items():
            doc_text = f"{industry_name} industry analysis. Key metrics: {', '.join(industry_data['key_metrics'])}"
            if 'success_factors' in industry_data:
                doc_text += f" Success factors: {', '.join(industry_data['success_factors'])}"
            
            documents.append(doc_text)
            metadatas.append({
                "type": "industry_template",
                "name": industry_name,
                "category": "industry"
            })
            ids.append(f"industry_{industry_name}")
        
        # Create embeddings and store
        if documents:
            embeddings = self.embedder.encode(documents, convert_to_numpy=True)
            
            self.financial_collection.upsert(
                ids=ids,
                embeddings=embeddings.tolist(),
                metadatas=metadatas,
                documents=documents
            )
            
            logging.info(f"Embedded {len(documents)} financial knowledge items")
    
    def retrieve_relevant_context(self, filing_data: Dict[str, Any], query_text: str = None) -> Dict[str, Any]:
        """Retrieve relevant financial context for analysis."""
        
        # Generate query text if not provided
        if not query_text:
            query_text = self._generate_query_text(filing_data)
        
        context = {
            "frameworks": self.knowledge_base.retrieve_relevant_frameworks(filing_data),
            "similar_filings": self._get_similar_filings(filing_data),
            "market_context": self._get_market_context(filing_data),
            "semantic_knowledge": self._get_semantic_knowledge(query_text)
        }
        
        return context
    
    def _generate_query_text(self, filing_data: Dict[str, Any]) -> str:
        """Generate query text for semantic search."""
        components = []
        
        if filing_data.get("issuer_name"):
            components.append(filing_data["issuer_name"])
        
        if filing_data.get("industry_group"):
            components.append(filing_data["industry_group"])
        
        if filing_data.get("offering_amount"):
            amount = filing_data["offering_amount"]
            if amount > 100000000:  # > $100M
                components.append("large offering")
            elif amount > 10000000:  # > $10M
                components.append("medium offering")
            else:
                components.append("small offering")
        
        if filing_data.get("title"):
            components.append(filing_data["title"])
        
        return " ".join(components)
    
    def _get_similar_filings(self, filing_data: Dict[str, Any], limit: int = 5) -> List[Dict[str, Any]]:
        """Get similar filings from the database."""
        try:
            # Search by industry and similar offering amount
            industry = filing_data.get("industry_group", "")
            amount = filing_data.get("offering_amount", 0)
            
            # Define amount range (±50%)
            min_amount = amount * 0.5 if amount > 0 else None
            max_amount = amount * 1.5 if amount > 0 else None
            
            similar_filings = self.db.search_filings(
                industry_group=industry if industry else None,
                min_amount=min_amount,
                max_amount=max_amount,
                limit=limit
            )
            
            return similar_filings
            
        except Exception as e:
            logging.error(f"Error retrieving similar filings: {e}")
            return []
    
    def _get_market_context(self, filing_data: Dict[str, Any]) -> Dict[str, Any]:
        """Get market context and statistics."""
        try:
            industry = filing_data.get("industry_group", "")
            
            # Get industry statistics
            industry_stats = self._get_industry_statistics(industry)
            
            # Get recent market trends
            recent_trends = self._get_recent_trends()
            
            return {
                "industry_statistics": industry_stats,
                "recent_trends": recent_trends,
                "market_conditions": self._assess_market_conditions()
            }
            
        except Exception as e:
            logging.error(f"Error retrieving market context: {e}")
            return {}
    
    def _get_industry_statistics(self, industry: str) -> Dict[str, Any]:
        """Get statistics for the specific industry."""
        try:
            # Get industry-specific statistics from database
            cursor = self.db.get_cursor()
            
            cursor.execute("""
                SELECT 
                    COUNT(*) as filing_count,
                    AVG(offering_amount) as avg_offering,
                    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY offering_amount) as median_offering,
                    SUM(offering_amount) as total_amount
                FROM form_d_filings 
                WHERE industry_group ILIKE %s 
                AND offering_amount IS NOT NULL
                AND filing_date >= CURRENT_DATE - INTERVAL '12 months'
            """, (f"%{industry}%",))
            
            result = cursor.fetchone()
            
            if result:
                return {
                    "filing_count": result["filing_count"],
                    "average_offering": float(result["avg_offering"]) if result["avg_offering"] else 0,
                    "median_offering": float(result["median_offering"]) if result["median_offering"] else 0,
                    "total_amount": float(result["total_amount"]) if result["total_amount"] else 0,
                    "period": "Last 12 months"
                }
            
        except Exception as e:
            logging.error(f"Error getting industry statistics: {e}")
        
        return {}
    
    def _get_recent_trends(self) -> Dict[str, Any]:
        """Get recent market trends from filings data."""
        try:
            cursor = self.db.get_cursor()
            
            # Get monthly filing trends
            cursor.execute("""
                SELECT 
                    DATE_TRUNC('month', filing_date::date) as month,
                    COUNT(*) as filing_count,
                    AVG(offering_amount) as avg_amount
                FROM form_d_filings 
                WHERE filing_date >= CURRENT_DATE - INTERVAL '6 months'
                AND offering_amount IS NOT NULL
                GROUP BY DATE_TRUNC('month', filing_date::date)
                ORDER BY month DESC
            """)
            
            monthly_trends = cursor.fetchall()
            
            return {
                "monthly_trends": [dict(row) for row in monthly_trends],
                "trend_analysis": self._analyze_trends(monthly_trends)
            }
            
        except Exception as e:
            logging.error(f"Error getting recent trends: {e}")
            return {}
    
    def _analyze_trends(self, monthly_data: List[Dict]) -> str:
        """Analyze trends from monthly data."""
        if len(monthly_data) < 2:
            return "Insufficient data for trend analysis"
        
        # Calculate trend direction
        recent_count = monthly_data[0]["filing_count"]
        previous_count = monthly_data[1]["filing_count"]
        
        if recent_count > previous_count * 1.1:
            return "Increasing filing activity"
        elif recent_count < previous_count * 0.9:
            return "Decreasing filing activity"
        else:
            return "Stable filing activity"
    
    def _assess_market_conditions(self) -> str:
        """Assess overall market conditions based on recent data."""
        try:
            cursor = self.db.get_cursor()
            
            # Get recent activity compared to historical average
            cursor.execute("""
                WITH recent_activity AS (
                    SELECT COUNT(*) as recent_count
                    FROM form_d_filings 
                    WHERE filing_date >= CURRENT_DATE - INTERVAL '30 days'
                ),
                historical_avg AS (
                    SELECT AVG(monthly_count) as avg_count
                    FROM (
                        SELECT COUNT(*) as monthly_count
                        FROM form_d_filings 
                        WHERE filing_date >= CURRENT_DATE - INTERVAL '12 months'
                        AND filing_date < CURRENT_DATE - INTERVAL '30 days'
                        GROUP BY DATE_TRUNC('month', filing_date::date)
                    ) monthly_counts
                )
                SELECT 
                    recent_activity.recent_count,
                    historical_avg.avg_count
                FROM recent_activity, historical_avg
            """)
            
            result = cursor.fetchone()
            
            if result:
                recent = result["recent_count"]
                historical = result["avg_count"]
                
                if recent > historical * 1.2:
                    return "Active market conditions"
                elif recent < historical * 0.8:
                    return "Quiet market conditions"
                else:
                    return "Normal market conditions"
            
        except Exception as e:
            logging.error(f"Error assessing market conditions: {e}")
        
        return "Market conditions unknown"
    
    def _get_semantic_knowledge(self, query_text: str, limit: int = 3) -> List[Dict[str, Any]]:
        """Get semantically relevant knowledge from the financial knowledge base."""
        if not self.financial_collection or not query_text:
            return []
        
        try:
            # Create query embedding
            query_embedding = self.embedder.encode([query_text], convert_to_numpy=True)[0]
            
            # Search for relevant knowledge
            results = self.financial_collection.query(
                query_embeddings=[query_embedding],
                n_results=limit
            )
            
            relevant_knowledge = []
            if results["ids"] and results["ids"][0]:
                for i, doc_id in enumerate(results["ids"][0]):
                    relevant_knowledge.append({
                        "id": doc_id,
                        "content": results["documents"][0][i],
                        "metadata": results["metadatas"][0][i],
                        "relevance_score": 1 - results["distances"][0][i]  # Convert distance to similarity
                    })
            
            return relevant_knowledge
            
        except Exception as e:
            logging.error(f"Error retrieving semantic knowledge: {e}")
            return []
    
    def enhance_analysis_prompt(self, filing_data: Dict[str, Any], base_prompt: str) -> str:
        """Enhance the analysis prompt with retrieved financial context."""
        
        # Retrieve relevant context
        context = self.retrieve_relevant_context(filing_data)
        
        # Generate financial frameworks context
        frameworks_context = self.knowledge_base.generate_analysis_context(filing_data)
        
        # Build enhanced prompt
        enhanced_prompt = f"""
{base_prompt}

{frameworks_context}

# MARKET CONTEXT
{self._format_market_context(context.get("market_context", {}))}

# SIMILAR FILINGS ANALYSIS
{self._format_similar_filings(context.get("similar_filings", []))}

# RELEVANT FINANCIAL KNOWLEDGE
{self._format_semantic_knowledge(context.get("semantic_knowledge", []))}

Use this comprehensive financial context to provide a professional-grade analysis with specific financial metrics, valuation rationale, and risk assessment.
"""
        
        return enhanced_prompt
    
    def _format_market_context(self, market_context: Dict[str, Any]) -> str:
        """Format market context for prompt inclusion."""
        if not market_context:
            return "Market context not available."
        
        context_text = ""
        
        if "industry_statistics" in market_context:
            stats = market_context["industry_statistics"]
            context_text += f"Industry Statistics (Last 12 months):\n"
            context_text += f"- Total filings: {stats.get('filing_count', 'N/A')}\n"
            context_text += f"- Average offering: ${stats.get('average_offering', 0):,.0f}\n"
            context_text += f"- Median offering: ${stats.get('median_offering', 0):,.0f}\n\n"
        
        if "market_conditions" in market_context:
            context_text += f"Market Conditions: {market_context['market_conditions']}\n\n"
        
        return context_text
    
    def _format_similar_filings(self, similar_filings: List[Dict[str, Any]]) -> str:
        """Format similar filings for prompt inclusion."""
        if not similar_filings:
            return "No similar filings found."
        
        context_text = "Comparable Recent Filings:\n"
        for filing in similar_filings[:3]:  # Top 3 most similar
            context_text += f"- {filing.get('issuer_name', 'Unknown')}: "
            context_text += f"${filing.get('offering_amount', 0):,.0f} "
            context_text += f"({filing.get('filing_date', 'Unknown date')})\n"
        
        return context_text
    
    def _format_semantic_knowledge(self, semantic_knowledge: List[Dict[str, Any]]) -> str:
        """Format semantic knowledge for prompt inclusion."""
        if not semantic_knowledge:
            return "No additional relevant knowledge found."
        
        context_text = "Relevant Financial Knowledge:\n"
        for knowledge in semantic_knowledge:
            context_text += f"- {knowledge['content']}\n"
        
        return context_text
