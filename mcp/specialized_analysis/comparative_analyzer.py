#!/usr/bin/env python3
"""
Comparative Analysis Module

Provides tools for comparing a filing against similar historical filings
and extracting insights from the comparison.
"""

import logging
import statistics
from typing import Dict, List, Any, Optional


class ComparativeAnalyzer:
    """
    Analyzer for comparative analysis of Form D filings.
    """

    def __init__(self):
        """Initialize the comparative analyzer."""
        self.comparison_template = """
# Comparative Analysis
Analyze this filing in comparison to similar historical filings:

1. Offering Size Comparison
   - Percentile ranking within industry: {amount_percentile:.1f}%
   - Deviation from industry average: {amount_deviation:+.1f}%
   - Trend analysis: {size_trend}

2. Valuation Metrics
   - Implied valuation comparison: {valuation_comparison}
   - Price per share relative to peers: {price_comparison}
   - Valuation multiple analysis: {multiple_analysis}

3. Investor Patterns
   - Repeat investors: {repeat_investors}
   - New investor analysis: {new_investors}
   - Investor syndicate composition: {syndicate_analysis}

4. Timing Considerations
   - Time since last financing: {time_since_last}
   - Financing cycle comparison: {cycle_comparison}
   - Market timing factors: {market_timing}

5. Competitive Positioning
   - Direct competitor comparison: {competitor_comparison}
   - Market share implications: {market_share}
   - Competitive advantage assessment: {competitive_advantage}
"""

    def analyze_comparatively(self, entry: Dict[str, Any], 
                             similar_filings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Perform comparative analysis against similar filings.

        Args:
            entry: Current filing entry to analyze
            similar_filings: List of similar historical filings

        Returns:
            Comparative analysis results
        """
        if not similar_filings:
            return {
                "comparative_analysis": "Insufficient data for comparative analysis.",
                "comparison_metrics": {}
            }
        
        # Extract metrics for comparison
        metrics = self._extract_comparison_metrics(entry, similar_filings)
        
        # Generate comparative analysis
        analysis = self._generate_comparative_analysis(entry, metrics)
        
        return {
            "comparative_analysis": analysis,
            "comparison_metrics": metrics
        }

    def _extract_comparison_metrics(self, entry: Dict[str, Any], 
                                  similar_filings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Extract metrics for comparative analysis.

        Args:
            entry: Current filing entry
            similar_filings: Similar historical filings

        Returns:
            Dictionary of comparison metrics
        """
        # Get current offering amount
        current_amount = float(entry.get('offering_amount', 0))
        if current_amount <= 0:
            return {"error": "Invalid offering amount"}
        
        # Extract offering amounts from similar filings
        similar_amounts = []
        for filing in similar_filings:
            metadata = filing.get('metadata', {})
            amount = float(metadata.get('offering_amount', 0))
            if amount > 0:
                similar_amounts.append(amount)
        
        if not similar_amounts:
            return {"error": "No valid comparison data"}
        
        # Calculate basic statistics
        avg_amount = statistics.mean(similar_amounts)
        median_amount = statistics.median(similar_amounts)
        
        # Calculate percentile
        similar_amounts.sort()
        rank = sum(1 for a in similar_amounts if a < current_amount)
        percentile = (rank / len(similar_amounts)) * 100
        
        # Calculate deviation from average
        deviation_pct = ((current_amount - avg_amount) / avg_amount) * 100 if avg_amount > 0 else 0
        
        # Determine size trend
        if percentile > 75:
            size_trend = "Significantly larger than typical for this industry"
        elif percentile > 50:
            size_trend = "Above average size for this industry"
        elif percentile > 25:
            size_trend = "Below average size for this industry"
        else:
            size_trend = "Significantly smaller than typical for this industry"
        
        # Extract dates for timing analysis
        filing_dates = []
        for filing in similar_filings:
            metadata = filing.get('metadata', {})
            date_str = metadata.get('filing_date', '')
            if date_str:
                filing_dates.append(date_str)
        
        # Determine if this is a follow-on financing
        issuer_name = entry.get('issuer_name', '').lower()
        previous_filings = [f for f in similar_filings 
                           if f.get('metadata', {}).get('issuer_name', '').lower() == issuer_name]
        
        is_follow_on = len(previous_filings) > 0
        
        # Return metrics
        return {
            "current_amount": current_amount,
            "avg_amount": avg_amount,
            "median_amount": median_amount,
            "amount_percentile": percentile,
            "amount_deviation": deviation_pct,
            "size_trend": size_trend,
            "similar_count": len(similar_amounts),
            "is_follow_on": is_follow_on,
            "previous_filings_count": len(previous_filings)
        }

    def _generate_comparative_analysis(self, entry: Dict[str, Any], 
                                     metrics: Dict[str, Any]) -> str:
        """
        Generate comparative analysis text.

        Args:
            entry: Current filing entry
            metrics: Comparison metrics

        Returns:
            Formatted comparative analysis
        """
        if "error" in metrics:
            return f"Comparative analysis not available: {metrics['error']}"
        
        # Determine valuation comparison
        if metrics.get("amount_percentile", 0) > 75:
            valuation_comparison = "Likely represents premium valuation relative to peers"
        elif metrics.get("amount_percentile", 0) > 50:
            valuation_comparison = "Suggests above-average valuation relative to peers"
        elif metrics.get("amount_percentile", 0) > 25:
            valuation_comparison = "Indicates below-average valuation relative to peers"
        else:
            valuation_comparison = "May represent discounted valuation relative to peers"
        
        # Determine price comparison (placeholder)
        price_comparison = "Insufficient data for direct price comparison"
        
        # Determine multiple analysis (placeholder)
        multiple_analysis = "Detailed multiple analysis requires additional financial data"
        
        # Investor analysis (placeholders)
        if metrics.get("is_follow_on", False):
            repeat_investors = "Previous investors likely participating in this round"
            new_investors = "May include new investors joining existing syndicate"
        else:
            repeat_investors = "No previous investors identified"
            new_investors = "All investors appear to be new to this company"
        
        syndicate_analysis = "Detailed syndicate analysis requires investor disclosure"
        
        # Timing analysis (placeholders)
        if metrics.get("previous_filings_count", 0) > 0:
            time_since_last = "Follow-on financing after previous rounds"
            cycle_comparison = "Consistent with typical financing cycle progression"
        else:
            time_since_last = "Initial financing round identified"
            cycle_comparison = "Starting point of financing cycle"
        
        # Market timing (placeholder)
        market_timing = "Current market conditions appear favorable for this offering type"
        
        # Competitive analysis (placeholders)
        competitor_comparison = f"Comparable to {metrics.get('similar_count', 0)} similar companies in dataset"
        market_share = "Market share implications require revenue data"
        competitive_advantage = "Competitive positioning assessment requires business model details"
        
        # Format the template
        analysis = self.comparison_template.format(
            amount_percentile=metrics.get("amount_percentile", 0),
            amount_deviation=metrics.get("amount_deviation", 0),
            size_trend=metrics.get("size_trend", "Unknown"),
            valuation_comparison=valuation_comparison,
            price_comparison=price_comparison,
            multiple_analysis=multiple_analysis,
            repeat_investors=repeat_investors,
            new_investors=new_investors,
            syndicate_analysis=syndicate_analysis,
            time_since_last=time_since_last,
            cycle_comparison=cycle_comparison,
            market_timing=market_timing,
            competitor_comparison=competitor_comparison,
            market_share=market_share,
            competitive_advantage=competitive_advantage
        )
        
        return analysis
