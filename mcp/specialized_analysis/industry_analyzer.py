#!/usr/bin/env python3
"""
Industry-Specific Analysis Module

Provides specialized analysis templates and criteria for different industries.
"""

import logging
from typing import Dict, List, Any, Optional


class IndustryAnalyzer:
    """
    Analyzer for industry-specific Form D filings.
    """

    def __init__(self):
        """Initialize the industry analyzer with templates."""
        # Define industry-specific templates
        self.industry_templates = {
            "technology": self._tech_template,
            "healthcare": self._healthcare_template,
            "financial": self._financial_template,
            "energy": self._energy_template,
            "real_estate": self._real_estate_template,
            "default": self._default_template
        }
        
        # Industry detection keywords
        self.industry_keywords = {
            "technology": [
                "software", "tech", "saas", "ai", "artificial intelligence", 
                "machine learning", "data", "cloud", "internet", "app", 
                "application", "platform", "digital", "computer", "cyber", 
                "security", "blockchain", "crypto", "web3", "fintech"
            ],
            "healthcare": [
                "health", "medical", "biotech", "pharmaceutical", "pharma", 
                "life science", "diagnostic", "therapeutic", "clinical", 
                "hospital", "patient", "drug", "device", "care", "wellness"
            ],
            "financial": [
                "finance", "financial", "bank", "investment", "capital", 
                "asset", "wealth", "fund", "lending", "loan", "credit", 
                "insurance", "payment", "trading", "broker", "fintech"
            ],
            "energy": [
                "energy", "oil", "gas", "renewable", "solar", "wind", 
                "battery", "power", "utility", "electric", "fuel", 
                "carbon", "climate", "clean tech", "sustainable"
            ],
            "real_estate": [
                "real estate", "property", "reit", "housing", "commercial", 
                "residential", "development", "construction", "building", 
                "land", "mortgage", "lease", "rent", "apartment"
            ]
        }

    def detect_industry(self, entry: Dict[str, Any]) -> str:
        """
        Detect the industry of a filing based on keywords.

        Args:
            entry: Filing entry to analyze

        Returns:
            Detected industry category
        """
        # Extract text fields for keyword matching
        text_fields = [
            entry.get('title', ''),
            entry.get('summary', ''),
            entry.get('industry', ''),
            entry.get('issuer_name', '')
        ]
        
        # Add SEC API data if available
        if entry.get('sec_api_enriched', False) and 'company_info' in entry:
            company_info = entry.get('company_info', {})
            text_fields.extend([
                company_info.get('name', ''),
                company_info.get('sic_description', '')
            ])
        
        # Combine all text fields
        combined_text = ' '.join(text_fields).lower()
        
        # Count keyword matches for each industry
        industry_scores = {}
        for industry, keywords in self.industry_keywords.items():
            score = sum(1 for keyword in keywords if keyword.lower() in combined_text)
            industry_scores[industry] = score
        
        # Get industry with highest score
        if industry_scores:
            max_score = max(industry_scores.values())
            if max_score > 0:
                # Get all industries with max score
                top_industries = [ind for ind, score in industry_scores.items() if score == max_score]
                return top_industries[0]  # Return first if multiple match
        
        # Default if no clear match
        return "default"

    def get_industry_template(self, entry: Dict[str, Any]) -> str:
        """
        Get the appropriate industry-specific template for a filing.

        Args:
            entry: Filing entry to analyze

        Returns:
            Industry-specific template string
        """
        industry = self.detect_industry(entry)
        template_func = self.industry_templates.get(industry, self.industry_templates["default"])
        return template_func()

    def _tech_template(self) -> str:
        """Template for technology industry."""
        return """
# Technology Industry Analysis
Analyze this technology company filing with focus on:

1. Technology Stack and Innovation
   - Core technology and differentiation
   - Patent portfolio and IP strategy
   - Technical team background and expertise

2. Market Positioning
   - Target market size and growth rate
   - Competitive landscape and barriers to entry
   - Go-to-market strategy and customer acquisition

3. Business Model
   - Revenue model (SaaS, licensing, marketplace, etc.)
   - Unit economics and scalability
   - Customer retention and expansion metrics

4. Growth Trajectory
   - Current stage (pre-seed, seed, Series A+)
   - Burn rate and runway implications
   - Potential exit strategies and acquirers

5. Technology-Specific Risks
   - Technical debt and scalability challenges
   - Regulatory and compliance considerations
   - Cybersecurity and data privacy concerns
"""

    def _healthcare_template(self) -> str:
        """Template for healthcare industry."""
        return """
# Healthcare Industry Analysis
Analyze this healthcare company filing with focus on:

1. Clinical and Scientific Foundation
   - Core technology or therapeutic approach
   - Clinical trial status and milestones
   - Scientific team and advisory board

2. Regulatory Pathway
   - FDA approval process and timeline
   - Regulatory strategy and milestones
   - Compliance requirements and challenges

3. Market Opportunity
   - Target patient population and unmet needs
   - Reimbursement landscape and pricing strategy
   - Provider adoption barriers and incentives

4. Competitive Landscape
   - Standard of care and competing approaches
   - Differentiation and clinical advantages
   - Patent protection and exclusivity period

5. Healthcare-Specific Risks
   - Clinical trial risks and patient outcomes
   - Regulatory approval uncertainties
   - Reimbursement and market access challenges
"""

    def _financial_template(self) -> str:
        """Template for financial industry."""
        return """
# Financial Services Industry Analysis
Analyze this financial services company filing with focus on:

1. Business Model and Revenue Streams
   - Fee structure and revenue generation
   - Assets under management or loan portfolio
   - Customer acquisition cost and lifetime value

2. Regulatory Compliance
   - Applicable regulations (SEC, FINRA, OCC, etc.)
   - Licensing requirements and status
   - Compliance infrastructure and controls

3. Market Positioning
   - Target customer segments and demographics
   - Competitive advantage and differentiation
   - Distribution channels and partnerships

4. Risk Management
   - Credit risk or investment risk approach
   - Operational risk controls
   - Technology and cybersecurity measures

5. Financial Services-Specific Risks
   - Regulatory changes and compliance costs
   - Interest rate and market cycle sensitivity
   - Disintermediation and fintech disruption
"""

    def _energy_template(self) -> str:
        """Template for energy industry."""
        return """
# Energy Industry Analysis
Analyze this energy company filing with focus on:

1. Energy Technology and Infrastructure
   - Core technology or energy production method
   - Infrastructure requirements and assets
   - Efficiency metrics and performance data

2. Market Dynamics
   - Energy price exposure and sensitivity
   - Supply chain and distribution channels
   - Regulatory environment and incentives

3. Sustainability Metrics
   - Carbon footprint and emissions profile
   - Renewable attributes and certifications
   - ESG considerations and reporting

4. Economic Fundamentals
   - CAPEX requirements and payback period
   - Operating costs and maintenance expenses
   - Project finance structure and terms

5. Energy-Specific Risks
   - Commodity price volatility
   - Regulatory and policy changes
   - Technology obsolescence and competition
"""

    def _real_estate_template(self) -> str:
        """Template for real estate industry."""
        return """
# Real Estate Industry Analysis
Analyze this real estate company filing with focus on:

1. Property Portfolio
   - Asset types and geographic distribution
   - Occupancy rates and tenant quality
   - Property valuation and cap rates

2. Investment Strategy
   - Value-add, development, or income focus
   - Hold period and exit strategy
   - Target returns and historical performance

3. Capital Structure
   - Debt-to-equity ratio and leverage
   - Financing terms and maturity schedule
   - Preferred equity or mezzanine components

4. Market Fundamentals
   - Supply and demand dynamics in target markets
   - Rental growth trends and projections
   - Demographic and economic drivers

5. Real Estate-Specific Risks
   - Interest rate sensitivity
   - Market cycle positioning
   - Property-specific operational challenges
"""

    def _default_template(self) -> str:
        """Default template for other industries."""
        return """
# Industry Analysis
Analyze this company filing with focus on:

1. Business Model and Value Proposition
   - Core products or services
   - Revenue model and pricing strategy
   - Customer segments and value chain position

2. Market Opportunity
   - Market size and growth trajectory
   - Competitive landscape and market share
   - Industry trends and disruption factors

3. Team and Execution
   - Leadership experience and track record
   - Key hires and organizational structure
   - Strategic partnerships and advisors

4. Financial Profile
   - Current stage and funding history
   - Capital efficiency and unit economics
   - Path to profitability and cash flow

5. Key Risks and Challenges
   - Execution risks and operational challenges
   - Market and competitive threats
   - Regulatory and compliance considerations
"""
