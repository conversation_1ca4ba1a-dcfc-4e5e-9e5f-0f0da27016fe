#!/usr/bin/env python3
"""
Offering-Type Analysis Module

Provides specialized analysis templates and criteria for different types
of offerings (e.g., equity, debt, convertible notes).
"""

import logging
import re
from typing import Dict, List, Any, Optional


class OfferingAnalyzer:
    """
    Analyzer for different types of Form D offerings.
    """

    def __init__(self):
        """Initialize the offering analyzer with templates."""
        # Define offering-specific templates
        self.offering_templates = {
            "equity": self._equity_template,
            "debt": self._debt_template,
            "convertible": self._convertible_template,
            "spac": self._spac_template,
            "fund": self._fund_template,
            "default": self._default_template
        }
        
        # Offering type detection keywords
        self.offering_keywords = {
            "equity": [
                "equity", "stock", "shares", "series", "preferred", 
                "common", "ownership", "stockholder", "shareholder"
            ],
            "debt": [
                "debt", "note", "loan", "credit", "bond", "debenture", 
                "interest", "principal", "maturity", "term loan"
            ],
            "convertible": [
                "convertible", "conversion", "safe", "simple agreement", 
                "future equity", "convert", "discount", "cap", "valuation cap"
            ],
            "spac": [
                "spac", "special purpose", "acquisition company", "blank check", 
                "shell company", "merger", "business combination", "target"
            ],
            "fund": [
                "fund", "limited partner", "lp", "general partner", "gp", 
                "capital commitment", "carried interest", "management fee", 
                "private equity", "venture capital", "hedge fund"
            ]
        }

    def detect_offering_type(self, entry: Dict[str, Any]) -> str:
        """
        Detect the offering type based on keywords.

        Args:
            entry: Filing entry to analyze

        Returns:
            Detected offering type
        """
        # Extract text fields for keyword matching
        text_fields = [
            entry.get('title', ''),
            entry.get('summary', ''),
            entry.get('offering_type', ''),
            entry.get('issuer_name', '')
        ]
        
        # Combine all text fields
        combined_text = ' '.join(text_fields).lower()
        
        # Count keyword matches for each offering type
        offering_scores = {}
        for offering_type, keywords in self.offering_keywords.items():
            score = sum(1 for keyword in keywords if keyword.lower() in combined_text)
            offering_scores[offering_type] = score
        
        # Get offering type with highest score
        if offering_scores:
            max_score = max(offering_scores.values())
            if max_score > 0:
                # Get all offering types with max score
                top_offerings = [ot for ot, score in offering_scores.items() if score == max_score]
                return top_offerings[0]  # Return first if multiple match
        
        # Default if no clear match
        return "default"

    def get_offering_template(self, entry: Dict[str, Any]) -> str:
        """
        Get the appropriate offering-specific template for a filing.

        Args:
            entry: Filing entry to analyze

        Returns:
            Offering-specific template string
        """
        offering_type = self.detect_offering_type(entry)
        template_func = self.offering_templates.get(offering_type, self.offering_templates["default"])
        return template_func()

    def _equity_template(self) -> str:
        """Template for equity offerings."""
        return """
# Equity Offering Analysis
Analyze this equity offering with focus on:

1. Equity Structure
   - Share class (preferred, common)
   - Series designation (A, B, C, etc.)
   - Voting rights and preferences

2. Valuation Metrics
   - Pre-money and post-money valuation
   - Price per share and dilution impact
   - Comparable company valuations

3. Investor Rights
   - Liquidation preferences and multiples
   - Anti-dilution provisions
   - Board seats and control provisions

4. Funding Context
   - Stage of financing (seed, early, growth)
   - Previous funding rounds and investors
   - Use of proceeds and growth targets

5. Equity-Specific Considerations
   - Employee equity pool and option plans
   - Founder vesting and lock-up provisions
   - Future financing needs and dilution
"""

    def _debt_template(self) -> str:
        """Template for debt offerings."""
        return """
# Debt Offering Analysis
Analyze this debt offering with focus on:

1. Debt Structure
   - Type of debt instrument (notes, bonds, loans)
   - Secured or unsecured status
   - Seniority and subordination

2. Economic Terms
   - Interest rate and payment schedule
   - Maturity date and term
   - Prepayment options and penalties

3. Covenants and Restrictions
   - Financial covenants and ratios
   - Operational restrictions
   - Default provisions and remedies

4. Funding Context
   - Debt-to-equity ratio and leverage impact
   - Use of proceeds and cash flow implications
   - Existing debt obligations and refinancing

5. Debt-Specific Considerations
   - Interest coverage and debt service capacity
   - Collateral and asset security
   - Refinancing risk and maturity wall
"""

    def _convertible_template(self) -> str:
        """Template for convertible offerings."""
        return """
# Convertible Offering Analysis
Analyze this convertible offering with focus on:

1. Convertible Structure
   - Instrument type (convertible note, SAFE, etc.)
   - Conversion triggers and mechanics
   - Maturity date (if applicable)

2. Economic Terms
   - Valuation cap and discount rate
   - Interest rate (if convertible note)
   - Conversion price calculation

3. Investor Protections
   - Most favored nation provisions
   - Pro-rata rights
   - Information rights

4. Funding Context
   - Bridge to equity round or standalone
   - Previous financing history
   - Use of proceeds and runway extension

5. Convertible-Specific Considerations
   - Potential dilution upon conversion
   - Conversion scenarios and outcomes
   - Tax and accounting implications
"""

    def _spac_template(self) -> str:
        """Template for SPAC offerings."""
        return """
# SPAC Offering Analysis
Analyze this SPAC offering with focus on:

1. SPAC Structure
   - Unit composition (shares and warrants)
   - Trust account size and terms
   - Sponsor promote and economics

2. Management Team
   - Sponsor background and track record
   - Industry focus and expertise
   - Prior SPAC or M&A experience

3. Target Acquisition Strategy
   - Industry focus and criteria
   - Target company size range
   - Geographic focus and limitations

4. Timeline and Process
   - IPO size and pricing
   - Search period and extension provisions
   - Shareholder approval requirements

5. SPAC-Specific Considerations
   - Redemption rights and thresholds
   - PIPE financing strategy
   - Warrant overhang and dilution
"""

    def _fund_template(self) -> str:
        """Template for fund offerings."""
        return """
# Investment Fund Analysis
Analyze this investment fund offering with focus on:

1. Fund Structure
   - Fund type (PE, VC, hedge, real estate, etc.)
   - Legal structure (LP, LLC, etc.)
   - Fund size and minimum investments

2. Economic Terms
   - Management fee structure
   - Carried interest and waterfall
   - Hurdle rate and clawback provisions

3. Investment Strategy
   - Sector and stage focus
   - Geographic concentration
   - Portfolio construction approach

4. Fund Management
   - GP track record and experience
   - Team composition and key professionals
   - Prior fund performance if available

5. Fund-Specific Considerations
   - Investment period and fund life
   - LP composition and anchor investors
   - Co-investment rights and opportunities
"""

    def _default_template(self) -> str:
        """Default template for other offering types."""
        return """
# Offering Analysis
Analyze this offering with focus on:

1. Offering Structure
   - Security type and characteristics
   - Size and pricing of the offering
   - Terms and conditions

2. Economic Terms
   - Pricing and valuation metrics
   - Return potential and investor economics
   - Liquidity provisions and exit mechanisms

3. Investor Considerations
   - Risk profile and downside protection
   - Rights and governance provisions
   - Alignment with issuer incentives

4. Market Context
   - Comparable offerings and terms
   - Market timing and conditions
   - Investor appetite and demand

5. Key Considerations
   - Use of proceeds and strategic impact
   - Post-financing capital structure
   - Future financing needs and strategy
"""
