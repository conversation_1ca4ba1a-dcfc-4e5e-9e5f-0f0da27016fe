#!/usr/bin/env python3
"""
Enhanced Model Control Point (MCP) with Financial RAG Integration

Extends the existing MCP with professional-grade financial analysis capabilities
through RAG-enhanced prompts and financial expertise.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.core import ModelControlPoint
from mcp.enhanced_prompt_manager import EnhancedPromptManager
from mcp.financial_rag_system import FinancialRAGSystem

class EnhancedModelControlPoint(ModelControlPoint):
    """
    Enhanced MCP with financial RAG integration and professional analysis capabilities.
    """
    
    def __init__(self, 
                 enable_financial_rag: bool = True,
                 financial_analysis_mode: str = "comprehensive",
                 **kwargs):
        """
        Initialize the Enhanced MCP.
        
        Args:
            enable_financial_rag: Whether to enable financial RAG system
            financial_analysis_mode: Analysis mode ('comprehensive', 'screening', 'risk_focused')
            **kwargs: Arguments passed to base ModelControlPoint
        """
        
        # Initialize base MCP
        super().__init__(**kwargs)
        
        self.enable_financial_rag = enable_financial_rag
        self.financial_analysis_mode = financial_analysis_mode
        
        # Initialize enhanced components
        self.enhanced_prompt_manager = None
        self.financial_rag_system = None
        
        # Initialize enhanced components after model is ready
        if self.model_available:
            self._initialize_enhanced_components()
    
    def _initialize_enhanced_components(self):
        """Initialize enhanced financial analysis components."""
        try:
            # Initialize enhanced prompt manager
            self.enhanced_prompt_manager = EnhancedPromptManager(
                enable_rag=self.enable_financial_rag
            )
            
            # Initialize RAG system if enabled
            if self.enable_financial_rag:
                self.financial_rag_system = FinancialRAGSystem()
            
            logging.info("Enhanced MCP components initialized successfully")
            
        except Exception as e:
            logging.error(f"Error initializing enhanced MCP components: {e}")
            # Fall back to base functionality
            self.enable_financial_rag = False
    
    def process_new_filings_enhanced(self, 
                                   feed_entries: List[Dict[str, Any]], 
                                   historical_context: bool = True,
                                   news_context: bool = True) -> List[Dict[str, Any]]:
        """
        Process new filings with enhanced financial analysis.
        
        Args:
            feed_entries: List of feed entries to process
            historical_context: Whether to include historical context
            news_context: Whether to include news context
            
        Returns:
            List of processed entries with enhanced analysis
        """
        
        if not self.enhanced_prompt_manager:
            logging.warning("Enhanced prompt manager not available, falling back to base processing")
            return super().process_new_filings(feed_entries, historical_context, news_context)
        
        logging.info(f"Processing {len(feed_entries)} entries with enhanced financial analysis")
        
        # Normalize entries using base functionality
        normalized_entries = self.data_processor.normalize_feed_entries(feed_entries)
        
        # Enrich with historical context if requested
        if historical_context:
            enriched_entries = self.data_processor.enrich_with_historical_context(
                normalized_entries, 
                use_sec_api=True
            )
        else:
            enriched_entries = normalized_entries
        
        processed_entries = []
        
        for entry in enriched_entries:
            try:
                # Process entry with enhanced analysis
                processed_entry = self._process_single_entry_enhanced(
                    entry, 
                    include_news=news_context
                )
                processed_entries.append(processed_entry)
                
            except Exception as e:
                logging.error(f"Error processing entry {entry.get('id', 'unknown')}: {e}")
                # Fall back to base processing for this entry
                try:
                    base_processed = self._process_single_entry_base(entry, include_news=news_context)
                    processed_entries.append(base_processed)
                except Exception as e2:
                    logging.error(f"Base processing also failed for entry {entry.get('id', 'unknown')}: {e2}")
                    continue
        
        logging.info(f"Successfully processed {len(processed_entries)} entries with enhanced analysis")
        return processed_entries
    
    def _process_single_entry_enhanced(self, 
                                     entry: Dict[str, Any], 
                                     include_news: bool = True) -> Dict[str, Any]:
        """Process a single entry with enhanced financial analysis."""
        
        # Create enhanced analysis prompt
        enhanced_prompt = self.enhanced_prompt_manager.create_enhanced_analysis_prompt(
            filing_data=entry,
            analysis_type=self.financial_analysis_mode
        )
        
        # Add news context if requested
        if include_news:
            news_context = self._get_news_context(entry)
            if news_context:
                enhanced_prompt += f"\n\n# NEWS CONTEXT\n{news_context}"
        
        # Generate analysis using the model
        try:
            if self.use_multi_stage:
                # Use multi-stage analysis with enhanced prompts
                analysis_result = self._run_enhanced_multi_stage_analysis(entry, enhanced_prompt)
            else:
                # Single-stage enhanced analysis
                analysis_result = self._run_enhanced_single_stage_analysis(entry, enhanced_prompt)
            
            # Parse enhanced output
            parsed_result = self.enhanced_prompt_manager.parse_enhanced_output(analysis_result)
            
            # Add enhanced analysis to entry
            entry.update({
                "enhanced_analysis": parsed_result,
                "analysis_type": self.financial_analysis_mode,
                "rag_enabled": self.enable_financial_rag,
                "processing_timestamp": self._get_timestamp()
            })
            
            return entry
            
        except Exception as e:
            logging.error(f"Error in enhanced analysis for entry {entry.get('id', 'unknown')}: {e}")
            # Fall back to base processing
            return self._process_single_entry_base(entry, include_news)
    
    def _process_single_entry_base(self, entry: Dict[str, Any], include_news: bool = True) -> Dict[str, Any]:
        """Fall back to base processing for a single entry."""
        
        # Use base prompt manager
        base_prompt = self.prompt_manager.create_analysis_prompt(
            entry,
            historical_context=entry.get("database_context", {}),
            news_context=self._get_news_context(entry) if include_news else {}
        )
        
        # Generate analysis
        analysis_result = self.model.generate(base_prompt)
        
        # Parse with base parser
        parsed_result = self.prompt_manager.parse_output(analysis_result)
        
        # Add to entry
        entry.update({
            "analysis": parsed_result,
            "analysis_type": "base",
            "rag_enabled": False,
            "processing_timestamp": self._get_timestamp()
        })
        
        return entry
    
    def _run_enhanced_multi_stage_analysis(self, entry: Dict[str, Any], base_prompt: str) -> str:
        """Run multi-stage analysis with enhanced prompts."""
        
        # Stage 1: Enhanced Screening
        screening_prompt = self.enhanced_prompt_manager.create_enhanced_analysis_prompt(
            filing_data=entry,
            analysis_type="quick_screening"
        )
        
        screening_result = self.model.generate(screening_prompt)
        screening_parsed = self.enhanced_prompt_manager.parse_enhanced_output(screening_result)
        
        # Check if should proceed based on screening
        relevance_score = screening_parsed.get("relevance_score", 0.0)
        if relevance_score < self.screening_threshold:
            return screening_result  # Return early if doesn't pass screening
        
        # Stage 2: Detailed Analysis
        detailed_prompt = self.enhanced_prompt_manager.create_enhanced_analysis_prompt(
            filing_data=entry,
            analysis_type="comprehensive_analysis"
        )
        
        # Add screening context to detailed analysis
        detailed_prompt += f"\n\n# SCREENING RESULTS\n{screening_result}"
        
        detailed_result = self.model.generate(detailed_prompt)
        detailed_parsed = self.enhanced_prompt_manager.parse_enhanced_output(detailed_result)
        
        # Stage 3: Risk-Focused Analysis (if high relevance)
        final_relevance = detailed_parsed.get("relevance_score", relevance_score)
        if final_relevance > self.relevance_threshold:
            risk_prompt = self.enhanced_prompt_manager.create_enhanced_analysis_prompt(
                filing_data=entry,
                analysis_type="risk_focused"
            )
            
            # Add previous analysis context
            risk_prompt += f"\n\n# PREVIOUS ANALYSIS\nScreening: {screening_result}\nDetailed: {detailed_result}"
            
            risk_result = self.model.generate(risk_prompt)
            
            # Combine all results
            combined_result = f"""
# SCREENING ANALYSIS
{screening_result}

# DETAILED ANALYSIS  
{detailed_result}

# RISK ANALYSIS
{risk_result}
"""
            return combined_result
        
        # Return combined screening and detailed analysis
        return f"""
# SCREENING ANALYSIS
{screening_result}

# DETAILED ANALYSIS
{detailed_result}
"""
    
    def _run_enhanced_single_stage_analysis(self, entry: Dict[str, Any], prompt: str) -> str:
        """Run single-stage enhanced analysis."""
        
        # Generate analysis with enhanced prompt
        result = self.model.generate(prompt)
        return result
    
    def _get_news_context(self, entry: Dict[str, Any]) -> str:
        """Get news context for the entry."""
        try:
            # Use existing news scraper if available
            if hasattr(self, 'tool_orchestrator') and self.tool_orchestrator:
                # Use tool orchestrator to get news
                news_query = f"{entry.get('issuer_name', '')} {entry.get('industry_group', '')}"
                news_results = self.tool_orchestrator.execute_tool("news_scraper", {"query": news_query})
                
                if news_results and isinstance(news_results, list):
                    news_context = "Recent News:\n"
                    for news in news_results[:3]:  # Top 3 news items
                        news_context += f"- {news.get('title', 'Unknown')}\n"
                        news_context += f"  {news.get('snippet', 'No snippet')}\n"
                    return news_context
            
            return "No news context available."
            
        except Exception as e:
            logging.error(f"Error getting news context: {e}")
            return "News context unavailable due to error."
    
    def _get_timestamp(self) -> str:
        """Get current timestamp."""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def get_enhanced_analysis_summary(self, processed_entries: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Get summary of enhanced analysis results."""
        
        total_entries = len(processed_entries)
        enhanced_entries = sum(1 for entry in processed_entries if entry.get("rag_enabled", False))
        
        # Calculate relevance score distribution
        relevance_scores = [
            entry.get("enhanced_analysis", {}).get("relevance_score", 0.0)
            for entry in processed_entries
            if "enhanced_analysis" in entry
        ]
        
        high_relevance = sum(1 for score in relevance_scores if score > 0.7)
        medium_relevance = sum(1 for score in relevance_scores if 0.4 <= score <= 0.7)
        low_relevance = sum(1 for score in relevance_scores if score < 0.4)
        
        summary = {
            "total_entries_processed": total_entries,
            "enhanced_analysis_count": enhanced_entries,
            "rag_enabled_percentage": (enhanced_entries / total_entries * 100) if total_entries > 0 else 0,
            "relevance_distribution": {
                "high_relevance": high_relevance,
                "medium_relevance": medium_relevance,
                "low_relevance": low_relevance
            },
            "analysis_mode": self.financial_analysis_mode,
            "average_relevance_score": sum(relevance_scores) / len(relevance_scores) if relevance_scores else 0.0
        }
        
        return summary
