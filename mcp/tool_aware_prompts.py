#!/usr/bin/env python3
"""
Tool-Aware Prompt Templates

Enhanced prompt templates that make the LLM aware of available tools and enable
intelligent tool selection and workflow orchestration for SEC Form D analysis.
"""

import json
import logging
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

class ToolAwarePromptManager:
    """
    Manages tool-aware prompts that enable LLM to make intelligent tool decisions.
    """
    
    def __init__(self, tool_orchestrator=None):
        """
        Initialize the tool-aware prompt manager.
        
        Args:
            tool_orchestrator: Tool orchestrator for getting tool descriptions
        """
        self.tool_orchestrator = tool_orchestrator
    
    def create_screening_prompt_with_tools(self, entry: Dict[str, Any]) -> str:
        """
        Create a screening prompt that includes tool awareness.
        
        Args:
            entry: Filing entry to analyze
            
        Returns:
            Tool-aware screening prompt
        """
        tool_descriptions = self._get_tool_descriptions() if self.tool_orchestrator else "No tools available."
        
        filing_info = self._format_filing_info(entry)
        
        prompt = f"""
You are an expert financial analyst specializing in private market investments and SEC Form D filings.
You have access to specialized tools that can enhance your analysis.

# Available Tools
{tool_descriptions}

# Filing Information
{filing_info}

# Screening Task
Perform an initial screening of this Form D filing to determine if it warrants detailed analysis.

Consider:
1. Offering amount (larger offerings are generally more significant)
2. Industry sector (focus on tech, healthcare, energy, financial services)
3. Notable investors or executives (if mentioned)
4. Unusual or standout features

# Tool Usage Decision
Based on the filing characteristics, determine if any tools would enhance your screening analysis:
- Use news_scraper if the company or industry might have recent relevant news
- Use postgresql_mcp_server if you need to compare with historical data
- Use github_mcp_server if this relates to an ongoing analysis project

# Response Format
Respond in JSON format with the following structure:
{{
  "initial_score": float,  // 0.0-1.0 where 1.0 is highest potential significance
  "reasoning": "string",   // Brief explanation of your score (1-2 sentences)
  "key_points": [          // List of 2-3 key points that influenced your decision
    "string",
    "string"
  ],
  "recommended_tools": [   // Tools that would enhance this analysis
    {{
      "tool_name": "string",
      "reason": "string",
      "parameters": {{
        "param1": "value1"
      }},
      "priority": int  // 1-5, where 5 is highest priority
    }}
  ]
}}
"""
        return prompt
    
    def create_detailed_analysis_prompt_with_tools(self, 
                                                  entry: Dict[str, Any],
                                                  screening_results: Optional[Dict[str, Any]] = None,
                                                  tool_results: Optional[Dict[str, Any]] = None) -> str:
        """
        Create a detailed analysis prompt that includes tool awareness and previous results.
        
        Args:
            entry: Filing entry to analyze
            screening_results: Results from screening stage
            tool_results: Results from tool executions
            
        Returns:
            Tool-aware detailed analysis prompt
        """
        tool_descriptions = self._get_tool_descriptions() if self.tool_orchestrator else "No tools available."
        
        filing_info = self._format_filing_info(entry)
        screening_context = self._format_screening_context(screening_results)
        tool_context = self._format_tool_context(tool_results)
        
        prompt = f"""
You are an expert financial analyst specializing in private market investments and SEC Form D filings.
You have access to specialized tools and previous analysis results.

# Available Tools
{tool_descriptions}

# Filing Information
{filing_info}

# Screening Results
{screening_context}

# Tool Results from Previous Stages
{tool_context}

# Detailed Analysis Task
Perform a comprehensive analysis of this Form D filing that passed initial screening.

Analyze:
1. Company background and business model
2. Investment terms and structure
3. Market opportunity and competitive landscape
4. Risk factors and mitigation strategies
5. Growth potential and exit opportunities

# Tool Usage Decision
Based on your analysis needs and available data, determine if additional tools would enhance your evaluation:
- Use news_scraper for recent company/industry developments
- Use postgresql_mcp_server for historical comparisons and data analysis
- Use github_mcp_server to track analysis progress or create follow-up tasks

# Response Format
Respond in JSON format with the following structure:
{{
  "relevance_score": float,  // 0.0-1.0 where 1.0 is highest relevance
  "summary": "string",       // 2-3 sentence summary
  "analysis": {{
    "company_analysis": "string",
    "investment_analysis": "string",
    "market_analysis": "string",
    "risk_factors": ["string", "string"],
    "opportunities": ["string", "string"]
  }},
  "reasoning_chain": [       // Step-by-step reasoning process
    "string",
    "string",
    "string"
  ],
  "recommended_tools": [     // Additional tools for deeper analysis
    {{
      "tool_name": "string",
      "reason": "string",
      "parameters": {{}},
      "priority": int
    }}
  ]
}}
"""
        return prompt
    
    def create_action_generation_prompt_with_tools(self,
                                                  entry: Dict[str, Any],
                                                  detailed_results: Optional[Dict[str, Any]] = None,
                                                  tool_results: Optional[Dict[str, Any]] = None) -> str:
        """
        Create an action generation prompt that includes tool awareness and orchestration.
        
        Args:
            entry: Filing entry to analyze
            detailed_results: Results from detailed analysis stage
            tool_results: Results from tool executions
            
        Returns:
            Tool-aware action generation prompt
        """
        tool_descriptions = self._get_tool_descriptions() if self.tool_orchestrator else "No tools available."
        
        filing_info = self._format_filing_info(entry)
        analysis_context = self._format_analysis_context(detailed_results)
        tool_context = self._format_tool_context(tool_results)
        
        prompt = f"""
You are an expert financial analyst specializing in private market investments and SEC Form D filings.
You have access to specialized tools for taking action on your analysis.

# Available Tools
{tool_descriptions}

# Filing Information
{filing_info}

# Detailed Analysis Results
{analysis_context}

# Tool Results from Previous Stages
{tool_context}

# Action Generation Task
Based on your comprehensive analysis, generate actionable insights and execute appropriate actions.

Consider:
1. Key actionable insights for stakeholders
2. Specific recommendations for investors
3. Follow-up actions needed
4. Communication and collaboration requirements

# Tool Usage for Actions
Determine which tools to use for executing actions:
- Use microsoft365_mcp_server to send email alerts to stakeholders
- Use github_mcp_server to create issues for follow-up analysis or tracking
- Use postgresql_mcp_server to store analysis results for future reference
- Use json_mcp_server to expose analysis via API for external systems

# Response Format
Respond in JSON format with the following structure:
{{
  "actionable_insights": [
    "string",
    "string"
  ],
  "recommendations": [
    "string",
    "string"
  ],
  "email_draft": "string",  // Concise email content for stakeholders
  "follow_up_questions": [
    "string",
    "string"
  ],
  "action_tools": [         // Tools to execute specific actions
    {{
      "tool_name": "string",
      "action": "string",    // Specific action to take
      "reason": "string",
      "parameters": {{}},
      "priority": int,
      "execute_immediately": bool  // Whether to execute now or later
    }}
  ]
}}
"""
        return prompt
    
    def _get_tool_descriptions(self) -> str:
        """Get formatted tool descriptions."""
        if not self.tool_orchestrator:
            return "No tools available."
        
        return self.tool_orchestrator.get_tool_descriptions()
    
    def _format_filing_info(self, entry: Dict[str, Any]) -> str:
        """Format filing information for prompt inclusion."""
        return f"""
Title: {entry.get('title', 'Unknown')}
Issuer: {entry.get('issuer_name', 'Unknown')}
Industry: {entry.get('industry_group', 'Unknown')}
Offering Amount: ${entry.get('offering_amount', 0):,.2f}
Filing Date: {entry.get('filing_date', 'Unknown')}
Description: {entry.get('description', 'No description available')[:300]}...
"""
    
    def _format_screening_context(self, screening_results: Optional[Dict[str, Any]]) -> str:
        """Format screening results for prompt inclusion."""
        if not screening_results:
            return "No screening results available."
        
        return f"""
Initial Score: {screening_results.get('initial_score', 0.0):.2f}
Reasoning: {screening_results.get('reasoning', 'No reasoning provided')}
Key Points: {', '.join(screening_results.get('key_points', []))}
"""
    
    def _format_analysis_context(self, detailed_results: Optional[Dict[str, Any]]) -> str:
        """Format detailed analysis results for prompt inclusion."""
        if not detailed_results:
            return "No detailed analysis results available."
        
        analysis = detailed_results.get('analysis', {})
        return f"""
Relevance Score: {detailed_results.get('relevance_score', 0.0):.2f}
Summary: {detailed_results.get('summary', 'No summary available')}
Company Analysis: {analysis.get('company_analysis', 'Not available')[:200]}...
Investment Analysis: {analysis.get('investment_analysis', 'Not available')[:200]}...
Risk Factors: {', '.join(analysis.get('risk_factors', [])[:3])}
Opportunities: {', '.join(analysis.get('opportunities', [])[:3])}
"""
    
    def _format_tool_context(self, tool_results: Optional[Dict[str, Any]]) -> str:
        """Format tool execution results for prompt inclusion."""
        if not tool_results:
            return "No tool results available."
        
        context = "Tool Execution Results:\n"
        for tool_name, result in tool_results.items():
            if "result" in result:
                context += f"- {tool_name}: Success\n"
            elif "error" in result:
                context += f"- {tool_name}: Error - {result['error']}\n"
            else:
                context += f"- {tool_name}: Unknown status\n"
        
        return context
