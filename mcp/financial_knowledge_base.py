#!/usr/bin/env python3
"""
Financial Knowledge Base Module

Provides comprehensive financial analysis frameworks, valuation methods,
risk assessment templates, and industry-specific analysis guides for
enhancing SEC Form D analysis with professional-grade financial expertise.
"""

import json
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
import numpy as np
from sentence_transformers import SentenceTransformer

class FinancialKnowledgeBase:
    """
    Comprehensive financial knowledge base for professional-grade analysis.
    """
    
    def __init__(self, data_dir: str = "data/financial_kb"):
        """Initialize the financial knowledge base."""
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize embedder for semantic search
        try:
            self.embedder = SentenceTransformer("all-mpnet-base-v2")  # Better for financial text
        except Exception as e:
            logging.warning(f"Could not load sentence transformer: {e}")
            self.embedder = None
        
        # Load knowledge base components
        self.valuation_frameworks = self._load_valuation_frameworks()
        self.risk_frameworks = self._load_risk_frameworks()
        self.industry_templates = self._load_industry_templates()
        self.financial_metrics = self._load_financial_metrics()
        self.market_analysis_guides = self._load_market_analysis_guides()
        
        logging.info("Financial Knowledge Base initialized successfully")
    
    def _load_valuation_frameworks(self) -> Dict[str, Any]:
        """Load comprehensive valuation methodologies."""
        return {
            "dcf_analysis": {
                "name": "Discounted Cash Flow Analysis",
                "description": "Net present value of projected future cash flows",
                "best_for": ["Mature companies", "Predictable cash flows", "Established businesses"],
                "not_suitable_for": ["Early-stage startups", "Highly volatile businesses", "Asset-light models"],
                "key_inputs": [
                    "Revenue projections (5-10 years)",
                    "Operating margin assumptions",
                    "Capital expenditure requirements",
                    "Working capital changes",
                    "Terminal growth rate",
                    "Weighted Average Cost of Capital (WACC)"
                ],
                "methodology": {
                    "step_1": "Project free cash flows for explicit forecast period",
                    "step_2": "Calculate terminal value using perpetual growth or exit multiple",
                    "step_3": "Discount all cash flows to present value using WACC",
                    "step_4": "Sum present values and subtract net debt for equity value"
                },
                "sensitivity_analysis": [
                    "Revenue growth rate (+/- 2%)",
                    "Operating margin (+/- 1%)",
                    "WACC (+/- 0.5%)",
                    "Terminal growth rate (+/- 0.5%)"
                ],
                "typical_ranges": {
                    "wacc_early_stage": "15-25%",
                    "wacc_growth_stage": "12-18%",
                    "wacc_mature": "8-12%",
                    "terminal_growth": "2-4%"
                }
            },
            "comparable_multiples": {
                "name": "Market Multiple Analysis",
                "description": "Valuation based on trading/transaction multiples of comparable companies",
                "best_for": ["Companies with public comparables", "Standard business models", "Established markets"],
                "not_suitable_for": ["Unique business models", "No comparable companies", "Distressed situations"],
                "key_multiples": {
                    "revenue_multiples": ["EV/Revenue", "EV/Forward Revenue", "P/S"],
                    "profitability_multiples": ["EV/EBITDA", "EV/EBIT", "P/E"],
                    "growth_multiples": ["PEG Ratio", "EV/Revenue/Growth"],
                    "asset_multiples": ["P/B", "EV/Tangible Assets"]
                },
                "adjustment_factors": {
                    "size_premium": "10-40% discount for smaller companies",
                    "liquidity_discount": "20-40% discount for private companies",
                    "growth_premium": "Premium for higher growth rates",
                    "profitability_premium": "Premium for higher margins",
                    "market_position": "Premium for market leadership"
                },
                "selection_criteria": [
                    "Same or similar industry",
                    "Comparable business model",
                    "Similar size (within 2-3x revenue)",
                    "Similar growth profile",
                    "Similar profitability"
                ]
            },
            "venture_capital_method": {
                "name": "Venture Capital Valuation Method",
                "description": "Valuation method specifically for early-stage, high-growth companies",
                "best_for": ["Early-stage startups", "High-growth potential", "VC-backed companies"],
                "methodology": {
                    "step_1": "Estimate exit value in 3-7 years",
                    "step_2": "Determine required return rate (IRR target)",
                    "step_3": "Calculate present value of exit",
                    "step_4": "Determine ownership percentage needed"
                },
                "typical_returns": {
                    "seed_stage": "50-100% IRR target",
                    "series_a": "40-60% IRR target",
                    "series_b": "30-50% IRR target",
                    "growth_stage": "20-35% IRR target"
                },
                "exit_multiples": {
                    "saas_companies": "8-15x revenue",
                    "biotech": "Risk-adjusted NPV",
                    "fintech": "6-12x revenue",
                    "marketplace": "10-20x revenue"
                }
            },
            "asset_based_valuation": {
                "name": "Asset-Based Valuation",
                "description": "Valuation based on underlying asset values",
                "best_for": ["Asset-heavy businesses", "Real estate companies", "Distressed situations"],
                "methods": {
                    "book_value": "Accounting value of assets minus liabilities",
                    "liquidation_value": "Expected proceeds from asset sale",
                    "replacement_cost": "Cost to replace all assets",
                    "market_value": "Current market value of assets"
                },
                "adjustments": [
                    "Depreciation vs. market value differences",
                    "Intangible asset values",
                    "Off-balance sheet items",
                    "Contingent liabilities"
                ]
            }
        }
    
    def _load_risk_frameworks(self) -> Dict[str, Any]:
        """Load comprehensive risk assessment frameworks."""
        return {
            "technology_risk": {
                "description": "Risks related to technology development and implementation",
                "risk_factors": [
                    "Technical feasibility and scalability",
                    "Intellectual property protection",
                    "Technology obsolescence",
                    "Development timeline risks",
                    "Technical team capabilities"
                ],
                "assessment_criteria": {
                    "prototype_status": ["Concept", "Prototype", "MVP", "Beta", "Production"],
                    "ip_protection": ["No IP", "Provisional patents", "Filed patents", "Granted patents"],
                    "technical_moat": ["None", "Weak", "Moderate", "Strong", "Dominant"],
                    "scalability": ["Unknown", "Limited", "Moderate", "High", "Unlimited"]
                },
                "mitigation_strategies": [
                    "Proof of concept development",
                    "IP strategy and patent filing",
                    "Technical advisory board",
                    "Key person insurance",
                    "Technology partnerships"
                ]
            },
            "market_risk": {
                "description": "Risks related to market acceptance and competition",
                "risk_factors": [
                    "Market size and growth potential",
                    "Competitive landscape intensity",
                    "Market timing and adoption",
                    "Customer acquisition challenges",
                    "Regulatory environment changes"
                ],
                "assessment_framework": {
                    "market_size": ["<$100M", "$100M-$1B", "$1B-$10B", "$10B+"],
                    "market_growth": ["Declining", "Flat", "Growing", "High Growth"],
                    "competition": ["None", "Weak", "Moderate", "Intense", "Dominant Players"],
                    "market_position": ["Unknown", "Follower", "Challenger", "Leader", "Dominant"]
                },
                "validation_methods": [
                    "Customer interviews and surveys",
                    "Pilot programs and beta testing",
                    "Market research and analysis",
                    "Competitive benchmarking",
                    "Regulatory compliance assessment"
                ]
            },
            "financial_risk": {
                "description": "Risks related to financial performance and funding",
                "risk_factors": [
                    "Cash flow predictability",
                    "Funding requirements and availability",
                    "Revenue concentration",
                    "Cost structure scalability",
                    "Financial controls and reporting"
                ],
                "key_metrics": [
                    "Cash burn rate and runway",
                    "Customer concentration (top 10%)",
                    "Gross margin sustainability",
                    "Working capital requirements",
                    "Debt service coverage"
                ],
                "mitigation_approaches": [
                    "Diversified revenue streams",
                    "Flexible cost structure",
                    "Strong financial controls",
                    "Multiple funding sources",
                    "Conservative cash management"
                ]
            },
            "operational_risk": {
                "description": "Risks related to business operations and execution",
                "risk_factors": [
                    "Management team experience",
                    "Operational scalability",
                    "Supply chain dependencies",
                    "Key person dependencies",
                    "Regulatory compliance"
                ],
                "assessment_areas": [
                    "Management track record",
                    "Operational processes and systems",
                    "Vendor and supplier relationships",
                    "Quality control and assurance",
                    "Business continuity planning"
                ]
            }
        }
    
    def _load_industry_templates(self) -> Dict[str, Any]:
        """Load industry-specific analysis templates."""
        return {
            "technology": {
                "key_metrics": [
                    "Monthly/Annual Recurring Revenue (MRR/ARR)",
                    "Customer Acquisition Cost (CAC)",
                    "Lifetime Value (LTV)",
                    "LTV/CAC ratio",
                    "Churn rate",
                    "Net Revenue Retention",
                    "Gross margin",
                    "Rule of 40 (Growth + Profitability)"
                ],
                "valuation_multiples": {
                    "saas": "8-15x ARR for growth companies",
                    "marketplace": "10-20x revenue",
                    "enterprise_software": "6-12x revenue",
                    "consumer_tech": "4-8x revenue"
                },
                "success_factors": [
                    "Product-market fit validation",
                    "Scalable technology architecture",
                    "Strong unit economics",
                    "Experienced technical team",
                    "Clear go-to-market strategy"
                ]
            },
            "biotech": {
                "key_metrics": [
                    "Pipeline value and stage",
                    "Intellectual property portfolio",
                    "Regulatory pathway clarity",
                    "Clinical trial success probability",
                    "Market exclusivity period",
                    "Manufacturing scalability"
                ],
                "valuation_methods": [
                    "Risk-adjusted NPV (rNPV)",
                    "Peak sales projections",
                    "Probability-weighted scenarios",
                    "Comparable transaction analysis"
                ],
                "risk_factors": [
                    "Clinical trial failure risk",
                    "Regulatory approval uncertainty",
                    "Competition from established players",
                    "Manufacturing and supply chain",
                    "Reimbursement and pricing pressure"
                ]
            },
            "fintech": {
                "key_metrics": [
                    "Transaction volume and growth",
                    "Take rate and monetization",
                    "User acquisition and retention",
                    "Regulatory compliance costs",
                    "Network effects strength"
                ],
                "regulatory_considerations": [
                    "Banking and financial regulations",
                    "Data privacy and security",
                    "Anti-money laundering (AML)",
                    "Know Your Customer (KYC)",
                    "Payment processing regulations"
                ],
                "competitive_moats": [
                    "Network effects",
                    "Regulatory barriers",
                    "Data advantages",
                    "Switching costs",
                    "Brand and trust"
                ]
            }
        }

    def _load_financial_metrics(self) -> Dict[str, Any]:
        """Load comprehensive financial metrics and benchmarks."""
        return {
            "profitability_metrics": {
                "gross_margin": {
                    "description": "Revenue minus cost of goods sold, divided by revenue",
                    "benchmarks": {
                        "saas": "75-85%",
                        "manufacturing": "20-40%",
                        "retail": "25-50%",
                        "services": "50-70%"
                    },
                    "interpretation": "Higher margins indicate pricing power and operational efficiency"
                },
                "operating_margin": {
                    "description": "Operating income divided by revenue",
                    "benchmarks": {
                        "mature_companies": "10-20%",
                        "growth_companies": "0-15%",
                        "early_stage": "Often negative"
                    }
                },
                "ebitda_margin": {
                    "description": "EBITDA divided by revenue",
                    "benchmarks": {
                        "technology": "20-40%",
                        "healthcare": "15-30%",
                        "manufacturing": "8-15%"
                    }
                }
            },
            "growth_metrics": {
                "revenue_growth": {
                    "description": "Year-over-year revenue growth rate",
                    "benchmarks": {
                        "early_stage": "100%+ annually",
                        "growth_stage": "50-100% annually",
                        "mature_stage": "10-30% annually"
                    }
                },
                "customer_growth": {
                    "description": "Rate of customer base expansion",
                    "key_indicators": [
                        "New customer acquisition rate",
                        "Customer retention rate",
                        "Net revenue retention"
                    ]
                }
            },
            "efficiency_metrics": {
                "capital_efficiency": {
                    "description": "Revenue generated per dollar of capital invested",
                    "metrics": [
                        "Return on Invested Capital (ROIC)",
                        "Return on Assets (ROA)",
                        "Asset turnover ratio"
                    ]
                },
                "operational_efficiency": {
                    "description": "Operational leverage and scalability",
                    "metrics": [
                        "Revenue per employee",
                        "Operating leverage ratio",
                        "Fixed vs. variable cost ratio"
                    ]
                }
            }
        }

    def _load_market_analysis_guides(self) -> Dict[str, Any]:
        """Load market analysis frameworks and guides."""
        return {
            "tam_sam_som_analysis": {
                "description": "Total Addressable Market, Serviceable Addressable Market, Serviceable Obtainable Market",
                "methodology": {
                    "tam": "Total market demand for a product or service",
                    "sam": "Portion of TAM targeted by your products and services",
                    "som": "Portion of SAM that you can realistically capture"
                },
                "calculation_methods": {
                    "top_down": "Start with industry reports and narrow down",
                    "bottom_up": "Build from unit economics and customer segments",
                    "value_theory": "Estimate value created for customers"
                },
                "validation_approaches": [
                    "Industry research and reports",
                    "Customer surveys and interviews",
                    "Competitive analysis",
                    "Pilot program results"
                ]
            },
            "competitive_analysis": {
                "framework": {
                    "direct_competitors": "Companies offering similar products/services",
                    "indirect_competitors": "Alternative solutions to the same problem",
                    "potential_competitors": "Companies that could enter the market"
                },
                "analysis_dimensions": [
                    "Product features and quality",
                    "Pricing and business model",
                    "Market share and position",
                    "Strengths and weaknesses",
                    "Strategic direction"
                ],
                "competitive_moats": [
                    "Network effects",
                    "Switching costs",
                    "Brand and reputation",
                    "Regulatory barriers",
                    "Economies of scale",
                    "Proprietary technology"
                ]
            },
            "market_timing": {
                "factors": [
                    "Market maturity and adoption curve",
                    "Regulatory environment changes",
                    "Technology enablers",
                    "Economic conditions",
                    "Customer behavior shifts"
                ],
                "assessment_criteria": [
                    "Early adopter feedback",
                    "Market research trends",
                    "Competitive landscape evolution",
                    "Technology readiness",
                    "Economic indicators"
                ]
            }
        }

    def retrieve_relevant_frameworks(self, filing_data: Dict[str, Any], query_type: str = "general") -> Dict[str, Any]:
        """Retrieve relevant financial frameworks based on filing characteristics."""

        industry = filing_data.get("industry_group", "").lower()
        offering_amount = filing_data.get("offering_amount", 0)

        # Determine company stage based on offering amount and other factors
        if offering_amount < 5000000:  # < $5M
            stage = "early_stage"
        elif offering_amount < 50000000:  # < $50M
            stage = "growth_stage"
        else:
            stage = "mature_stage"

        relevant_frameworks = {
            "valuation_methods": self._select_valuation_methods(industry, stage, offering_amount),
            "risk_assessment": self._select_risk_frameworks(industry, stage),
            "industry_analysis": self._select_industry_template(industry),
            "financial_metrics": self._select_relevant_metrics(industry, stage),
            "market_analysis": self.market_analysis_guides
        }

        return relevant_frameworks

    def _select_valuation_methods(self, industry: str, stage: str, amount: float) -> List[Dict[str, Any]]:
        """Select appropriate valuation methods based on company characteristics."""
        methods = []

        if stage == "early_stage":
            methods.append(self.valuation_frameworks["venture_capital_method"])
            if "biotech" in industry or "pharma" in industry:
                # Add risk-adjusted NPV for biotech
                methods.append({
                    "name": "Risk-Adjusted NPV",
                    "description": "NPV adjusted for clinical and regulatory risks",
                    "specific_to": "biotech"
                })

        if stage in ["growth_stage", "mature_stage"]:
            methods.append(self.valuation_frameworks["comparable_multiples"])
            if amount > 10000000:  # > $10M
                methods.append(self.valuation_frameworks["dcf_analysis"])

        # Always include asset-based for certain industries
        if any(term in industry for term in ["real estate", "manufacturing", "energy"]):
            methods.append(self.valuation_frameworks["asset_based_valuation"])

        return methods

    def _select_risk_frameworks(self, industry: str, stage: str) -> List[Dict[str, Any]]:
        """Select relevant risk assessment frameworks."""
        frameworks = [
            self.risk_frameworks["market_risk"],
            self.risk_frameworks["financial_risk"],
            self.risk_frameworks["operational_risk"]
        ]

        # Add technology risk for tech companies
        if any(term in industry for term in ["technology", "software", "biotech", "fintech"]):
            frameworks.append(self.risk_frameworks["technology_risk"])

        return frameworks

    def _select_industry_template(self, industry: str) -> Optional[Dict[str, Any]]:
        """Select industry-specific analysis template."""
        industry_lower = industry.lower()

        if any(term in industry_lower for term in ["technology", "software", "saas"]):
            return self.industry_templates["technology"]
        elif any(term in industry_lower for term in ["biotech", "pharma", "medical"]):
            return self.industry_templates["biotech"]
        elif any(term in industry_lower for term in ["fintech", "financial"]):
            return self.industry_templates["fintech"]

        return None

    def _select_relevant_metrics(self, industry: str, stage: str) -> Dict[str, Any]:
        """Select relevant financial metrics based on industry and stage."""
        metrics = {
            "profitability": self.financial_metrics["profitability_metrics"],
            "growth": self.financial_metrics["growth_metrics"]
        }

        if stage in ["growth_stage", "mature_stage"]:
            metrics["efficiency"] = self.financial_metrics["efficiency_metrics"]

        return metrics

    def generate_analysis_context(self, filing_data: Dict[str, Any]) -> str:
        """Generate comprehensive analysis context for LLM prompts."""

        frameworks = self.retrieve_relevant_frameworks(filing_data)

        context = "# FINANCIAL ANALYSIS FRAMEWORKS\n\n"

        # Add valuation methods
        context += "## Recommended Valuation Methods:\n"
        for method in frameworks["valuation_methods"]:
            context += f"### {method['name']}\n"
            context += f"{method['description']}\n"
            if "best_for" in method:
                context += f"Best for: {', '.join(method['best_for'])}\n"
            context += "\n"

        # Add risk assessment framework
        context += "## Risk Assessment Framework:\n"
        for framework in frameworks["risk_assessment"]:
            context += f"### {framework['description']}\n"
            context += f"Key factors: {', '.join(framework['risk_factors'])}\n\n"

        # Add industry-specific guidance
        if frameworks["industry_analysis"]:
            template = frameworks["industry_analysis"]
            context += "## Industry-Specific Analysis:\n"
            context += f"Key metrics: {', '.join(template['key_metrics'])}\n"
            if "success_factors" in template:
                context += f"Success factors: {', '.join(template['success_factors'])}\n"
            context += "\n"

        # Add financial metrics guidance
        context += "## Relevant Financial Metrics:\n"
        for category, metrics in frameworks["financial_metrics"].items():
            context += f"### {category.title()} Metrics:\n"
            for metric_name, metric_info in metrics.items():
                if isinstance(metric_info, dict) and "description" in metric_info:
                    context += f"- {metric_name}: {metric_info['description']}\n"
            context += "\n"

        return context
