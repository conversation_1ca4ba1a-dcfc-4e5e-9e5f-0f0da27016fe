#!/usr/bin/env python3
"""
Prompt Evaluation Framework

This module implements a framework for evaluating and improving prompts:
1. Generating prompt variations
2. Evaluating prompt effectiveness
3. Automatically refining prompts based on outcomes
"""

import json
import logging
import re
import random
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path


class PromptEvaluator:
    """
    Evaluator for prompt effectiveness.
    """

    def __init__(self, data_dir: str = "data/prompt_evaluation"):
        """
        Initialize the prompt evaluator.

        Args:
            data_dir: Directory for storing evaluation data
        """
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True, parents=True)
        
        # Track prompt performance
        self.prompt_performance = {}
        
        # Load existing performance data if available
        self._load_performance_data()

    def _load_performance_data(self):
        """Load existing performance data from disk."""
        performance_file = self.data_dir / "prompt_performance.json"
        if performance_file.exists():
            try:
                with open(performance_file, "r") as f:
                    self.prompt_performance = json.load(f)
                logging.info(f"Loaded performance data for {len(self.prompt_performance)} prompts")
            except Exception as e:
                logging.error(f"Error loading prompt performance data: {e}")
                self.prompt_performance = {}

    def _save_performance_data(self):
        """Save performance data to disk."""
        performance_file = self.data_dir / "prompt_performance.json"
        try:
            with open(performance_file, "w") as f:
                json.dump(self.prompt_performance, f, indent=2)
            logging.info(f"Saved performance data for {len(self.prompt_performance)} prompts")
        except Exception as e:
            logging.error(f"Error saving prompt performance data: {e}")

    def generate_variations(self, base_prompt: str, n_variations: int = 3) -> List[str]:
        """
        Generate variations of a base prompt.

        Args:
            base_prompt: Base prompt to generate variations from
            n_variations: Number of variations to generate

        Returns:
            List of prompt variations
        """
        variations = [base_prompt]  # Always include the original
        
        # Simple variation strategies
        strategies = [
            self._add_examples,
            self._modify_instructions,
            self._change_formatting,
            self._adjust_specificity
        ]
        
        # Generate variations using different strategies
        for _ in range(n_variations):
            strategy = random.choice(strategies)
            variation = strategy(base_prompt)
            variations.append(variation)
        
        return variations

    def _add_examples(self, prompt: str) -> str:
        """Add examples to the prompt."""
        # Simple example for Form D filings
        example = """
# Example
For a filing with:
- Offering amount of $10M
- Tech industry
- First-time issuer

A good analysis would include:
- Comparison to typical seed/Series A rounds
- Assessment of valuation implied
- Identification of key investors if disclosed
"""
        
        # Find a good insertion point (before response format)
        if "Response Format" in prompt:
            parts = prompt.split("Response Format")
            return parts[0] + example + "\nResponse Format" + parts[1]
        else:
            return prompt + "\n" + example

    def _modify_instructions(self, prompt: str) -> str:
        """Modify the instructions in the prompt."""
        # Add emphasis on certain aspects
        emphasis_phrases = [
            "Pay special attention to the offering amount and how it compares to industry norms.",
            "Focus particularly on identifying unusual patterns or outliers in this filing.",
            "Consider the broader market context when assessing significance.",
            "Be especially alert to signs of follow-on offerings or serial entrepreneurs."
        ]
        
        emphasis = random.choice(emphasis_phrases)
        
        # Add after the initial instructions
        lines = prompt.split("\n")
        for i, line in enumerate(lines):
            if "Task" in line or "Analyze" in line:
                lines.insert(i + 2, emphasis)
                break
        
        return "\n".join(lines)

    def _change_formatting(self, prompt: str) -> str:
        """Change the formatting of the prompt."""
        # Modify JSON format slightly
        if "Response Format" in prompt and "json" in prompt.lower():
            # Change indentation or add comments
            if "  " in prompt:  # Has indentation
                return prompt.replace("  ", "    ")  # Increase indentation
            else:
                return prompt.replace("}", "  // End of response\n}")
        return prompt

    def _adjust_specificity(self, prompt: str) -> str:
        """Adjust the specificity of the prompt."""
        # Make more specific by adding criteria
        specificity_additions = [
            "Consider industry-specific metrics when evaluating this filing.",
            "Evaluate the filing in the context of current market conditions and trends.",
            "Assess the geographic implications of this investment.",
            "Consider the regulatory environment specific to this industry sector."
        ]
        
        addition = random.choice(specificity_additions)
        
        # Add to analysis criteria
        if "# Analysis" in prompt:
            return prompt.replace("# Analysis", f"# Analysis\n{addition}")
        else:
            return prompt + f"\n\n{addition}"

    def evaluate_prompt(self, prompt: str, llm_output: str, 
                       parsed_output: Dict[str, Any]) -> Dict[str, Any]:
        """
        Evaluate the effectiveness of a prompt.

        Args:
            prompt: The prompt that was used
            llm_output: Raw output from LLM
            parsed_output: Parsed structured output

        Returns:
            Evaluation metrics
        """
        # Calculate basic metrics
        metrics = {
            "prompt_id": self._generate_prompt_id(prompt),
            "output_length": len(llm_output),
            "parse_success": self._check_parse_success(parsed_output),
            "structure_adherence": self._evaluate_structure(llm_output),
            "content_quality": self._evaluate_content(parsed_output),
            "overall_score": 0.0  # Will be calculated
        }
        
        # Calculate overall score (weighted average)
        weights = {
            "parse_success": 0.4,
            "structure_adherence": 0.3,
            "content_quality": 0.3
        }
        
        overall_score = sum(metrics[k] * weights[k] for k in weights)
        metrics["overall_score"] = overall_score
        
        # Store performance data
        self._update_performance(metrics["prompt_id"], metrics)
        
        return metrics

    def _generate_prompt_id(self, prompt: str) -> str:
        """Generate a unique ID for a prompt."""
        # Simple hash-based ID
        import hashlib
        return hashlib.md5(prompt.encode()).hexdigest()[:8]

    def _check_parse_success(self, parsed_output: Dict[str, Any]) -> float:
        """Check if parsing was successful."""
        # Check if any default values remain
        if "Failed to parse" in str(parsed_output):
            return 0.0
        
        # Check if required fields are present and non-empty
        required_fields = ["relevance_score", "summary"]
        missing_fields = [f for f in required_fields if not parsed_output.get(f)]
        
        if missing_fields:
            return 0.5  # Partial success
        return 1.0  # Full success

    def _evaluate_structure(self, llm_output: str) -> float:
        """Evaluate adherence to requested structure."""
        # Check for JSON format
        if "```json" in llm_output or re.search(r'^\s*{', llm_output) is not None:
            try:
                # Try to extract and parse JSON
                json_match = re.search(r'```json\s*(.*?)\s*```', llm_output, re.DOTALL)
                if json_match:
                    json.loads(json_match.group(1))
                    return 1.0
                else:
                    # Try without code block markers
                    json_match = re.search(r'({.*})', llm_output, re.DOTALL)
                    if json_match:
                        json.loads(json_match.group(1))
                        return 0.8  # Good but not perfect format
            except:
                return 0.3  # JSON-like but not valid
        return 0.0  # No JSON structure

    def _evaluate_content(self, parsed_output: Dict[str, Any]) -> float:
        """Evaluate the quality of content."""
        score = 0.0
        checks = 0
        
        # Check summary length (aim for 2-3 sentences)
        if "summary" in parsed_output:
            summary = parsed_output["summary"]
            sentences = len(re.split(r'[.!?]+', summary))
            if 2 <= sentences <= 4:
                score += 1.0
            elif 1 <= sentences <= 5:
                score += 0.5
            checks += 1
        
        # Check for reasoning or analysis
        if "reasoning" in parsed_output or "analysis" in parsed_output or "reasoning_chain" in parsed_output:
            reasoning = parsed_output.get("reasoning", "") or str(parsed_output.get("analysis", "")) or str(parsed_output.get("reasoning_chain", ""))
            if len(reasoning) > 100:  # Substantial reasoning
                score += 1.0
            elif len(reasoning) > 50:  # Some reasoning
                score += 0.5
            checks += 1
        
        # Avoid division by zero
        return score / max(1, checks)

    def _update_performance(self, prompt_id: str, metrics: Dict[str, Any]):
        """Update performance data for a prompt."""
        if prompt_id not in self.prompt_performance:
            self.prompt_performance[prompt_id] = {
                "metrics": [],
                "avg_score": 0.0,
                "uses": 0
            }
        
        # Add new metrics
        self.prompt_performance[prompt_id]["metrics"].append(metrics)
        self.prompt_performance[prompt_id]["uses"] += 1
        
        # Update average score
        scores = [m["overall_score"] for m in self.prompt_performance[prompt_id]["metrics"]]
        self.prompt_performance[prompt_id]["avg_score"] = sum(scores) / len(scores)
        
        # Save updated data
        self._save_performance_data()

    def get_best_prompt(self, prompt_variations: List[str]) -> str:
        """
        Get the best performing prompt from a list of variations.

        Args:
            prompt_variations: List of prompt variations

        Returns:
            The best performing prompt
        """
        best_score = -1
        best_prompt = prompt_variations[0]  # Default to first
        
        for prompt in prompt_variations:
            prompt_id = self._generate_prompt_id(prompt)
            if prompt_id in self.prompt_performance:
                score = self.prompt_performance[prompt_id]["avg_score"]
                if score > best_score:
                    best_score = score
                    best_prompt = prompt
        
        return best_prompt

    def refine_prompt(self, prompt: str, metrics: Dict[str, Any]) -> str:
        """
        Automatically refine a prompt based on evaluation metrics.

        Args:
            prompt: Original prompt
            metrics: Evaluation metrics

        Returns:
            Refined prompt
        """
        refined_prompt = prompt
        
        # If parsing failed, add more structure guidance
        if metrics["parse_success"] < 0.5:
            refined_prompt = self._add_structure_guidance(refined_prompt)
        
        # If structure adherence is low, emphasize format
        if metrics["structure_adherence"] < 0.5:
            refined_prompt = self._emphasize_format(refined_prompt)
        
        # If content quality is low, add more specific instructions
        if metrics["content_quality"] < 0.5:
            refined_prompt = self._add_specific_instructions(refined_prompt)
        
        return refined_prompt

    def _add_structure_guidance(self, prompt: str) -> str:
        """Add more structure guidance to the prompt."""
        structure_guidance = """
IMPORTANT: Your response MUST be in valid JSON format.
Wrap your JSON with ```json and ``` markers.
Ensure all JSON fields match exactly what is requested.
Do not include any text outside the JSON structure.
"""
        
        # Add before response format section
        if "Response Format" in prompt:
            parts = prompt.split("Response Format")
            return parts[0] + structure_guidance + "\nResponse Format" + parts[1]
        else:
            return prompt + "\n" + structure_guidance

    def _emphasize_format(self, prompt: str) -> str:
        """Emphasize the required format."""
        # Make format section more prominent
        if "Response Format" in prompt:
            return prompt.replace("Response Format", "RESPONSE FORMAT (STRICTLY REQUIRED)")
        return prompt

    def _add_specific_instructions(self, prompt: str) -> str:
        """Add more specific instructions for content."""
        specificity = """
For high-quality analysis:
- Provide concrete details and specific insights
- Include quantitative assessments where possible
- Reference industry benchmarks and comparables
- Highlight unusual or noteworthy aspects
"""
        
        # Add after initial instructions
        lines = prompt.split("\n")
        for i, line in enumerate(lines):
            if "Task" in line or "Analyze" in line:
                lines.insert(i + 2, specificity)
                break
        
        return "\n".join(lines)
