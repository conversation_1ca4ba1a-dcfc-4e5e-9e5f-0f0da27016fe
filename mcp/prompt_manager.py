#!/usr/bin/env python3
"""
MCP Prompt Manager <PERSON><PERSON><PERSON>les LLM prompt creation and output parsing for the Model Control Point:
1. Creating analysis prompts for SEC Form D filings
2. Parsing LLM outputs into structured data
3. Managing prompt templates
"""

import json
import logging
import re
from typing import Dict, List, Any, Optional

class PromptManager:
    """
    Prompt manager for LLM interactions.
    """

    def __init__(self):
        """Initialize the prompt manager with templates."""
        # Define prompt templates
        self.analysis_template = """
You are an expert financial analyst specializing in private market investments and SEC Form D filings.
Analyze the following Form D filing information and provide:
1. A relevance score (0.0-1.0) indicating how significant this filing is
2. A concise summary of key insights (2-3 sentences)
3. A draft email alert (if relevance score > 0.7)

# Filing Information
{filing_info}

# Historical Context
{historical_context}

# News Context
{news_context}

# Response Format
Respond in JSON format with the following structure:
{{
  "relevance_score": float,  // 0.0-1.0 where 1.0 is highest relevance
  "summary": "string",       // 2-3 sentence summary
  "email_draft": "string"    // Draft email content (if relevance_score > 0.7)
}}
"""

    def create_analysis_prompt(self, entry: Dict[str, Any]) -> str:
        """
        Create an analysis prompt for a filing entry.

        Args:
            entry: Enriched filing entry

        Returns:
            Formatted prompt string
        """
        # Format filing information
        filing_info = f"""
Title: {entry.get('title', 'Unknown')}
Filing Date: {entry.get('filing_date', 'Unknown')}
Offering Amount: ${entry.get('offering_amount', 0):,.2f}
Industry: {entry.get('industry', 'Unknown')}
Summary: {entry.get('summary', 'No summary available')}
Link: {entry.get('link', 'No link available')}
"""

        # Add SEC API data if available
        if entry.get('sec_api_enriched', False) and 'company_info' in entry:
            company_info = entry.get('company_info', {})
            filing_info += f"""
Company Information (SEC API):
Company Name: {company_info.get('name', 'Unknown')}
SIC Code: {company_info.get('sic', 'Unknown')} - {company_info.get('sic_description', 'Unknown')}
State of Incorporation: {company_info.get('state_of_incorporation', 'Unknown')}
Total SEC Filings: {company_info.get('filing_count', 0)}
"""

        # Format historical context
        similar_filings = entry.get('similar_filings', [])
        if similar_filings:
            historical_context = f"""
Found {len(similar_filings)} similar historical filings.
Average Similarity Distance: {entry.get('avg_similarity_distance', 0.0):.4f}
Same Industry Count: {entry.get('same_industry_count', 0)}
Offering Amount Percentile: {entry.get('amount_percentile', 0.0):.2f}

Top Similar Filings:
"""
            # Add details for up to 3 similar filings
            for i, filing in enumerate(similar_filings[:3]):
                metadata = filing.get('metadata', {})
                historical_context += f"""
{i+1}. Issuer: {metadata.get('issuer_name', 'Unknown')}
   Filing Date: {metadata.get('filing_date', 'Unknown')}
   Offering Amount: ${float(metadata.get('offering_amount', 0)):,.2f}
   Distance: {filing.get('distance', 0.0):.4f}
"""
        else:
            historical_context = "No similar historical filings found."

        # Format news context
        news_items = entry.get('news_context', [])
        if news_items:
            news_context = f"""
Found {len(news_items)} news articles related to this filing.

Top News Articles:
"""
            # Add details for up to 3 news articles
            for i, news in enumerate(news_items[:3]):
                news_context += f"""
{i+1}. Title: {news.get('title', 'Unknown')}
   Source: {news.get('source', 'Unknown')}
   Date: {news.get('date', 'Unknown')[:10]}
   Snippet: {news.get('snippet', 'No snippet available')}
"""
        else:
            news_context = "No related news articles found."

        # Format the full prompt
        prompt = self.analysis_template.format(
            filing_info=filing_info,
            historical_context=historical_context,
            news_context=news_context
        )

        return prompt

    def parse_llm_output(self, llm_output: str) -> Dict[str, Any]:
        """
        Parse LLM output into structured data.

        Args:
            llm_output: Raw output from LLM

        Returns:
            Parsed output as dictionary
        """
        # Default values
        parsed = {
            "relevance_score": 0.0,
            "summary": "Failed to parse LLM output",
            "email_draft": ""
        }

        try:
            # Try to extract JSON from the output
            # First, look for JSON block
            json_match = re.search(r'```json\s*(.*?)\s*```', llm_output, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # Try to find JSON without code block markers
                json_match = re.search(r'({.*})', llm_output, re.DOTALL)
                if json_match:
                    json_str = json_match.group(1)
                else:
                    # Use the whole output as a last resort
                    json_str = llm_output

            # Parse the JSON
            parsed_json = json.loads(json_str)

            # Extract fields
            if "relevance_score" in parsed_json:
                score = float(parsed_json["relevance_score"])
                # Ensure score is in valid range
                parsed["relevance_score"] = max(0.0, min(1.0, score))

            if "summary" in parsed_json:
                parsed["summary"] = parsed_json["summary"]

            if "email_draft" in parsed_json:
                parsed["email_draft"] = parsed_json["email_draft"]

        except Exception as e:
            logging.error(f"Error parsing LLM output: {e}")
            logging.debug(f"Raw LLM output: {llm_output}")

            # Fallback: Try to extract relevance score using regex
            try:
                score_match = re.search(r'relevance_score["\s:]+([0-9.]+)', llm_output)
                if score_match:
                    score = float(score_match.group(1))
                    parsed["relevance_score"] = max(0.0, min(1.0, score))
            except:
                pass

            # Fallback: Try to extract summary using regex
            try:
                summary_match = re.search(r'summary["\s:]+(["\'])(.*?)\1', llm_output)
                if summary_match:
                    parsed["summary"] = summary_match.group(2)
            except:
                pass

        return parsed
