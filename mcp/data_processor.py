#!/usr/bin/env python3
"""
MCP Data Processor Module

Handles data processing tasks for the Model Control Point:
1. Normalizing data from different sources (ATOM feed, bulk ZIPs)
2. Enriching entries with historical context and SEC API data
3. Feature extraction for LLM analysis
"""

import os
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import numpy as np

# Import SEC API client
from ingest.sec_api import enrich_filing_with_company_data

# Local imports
from vector_store.embed import embedder, client
from db.supabase_manager import SupabaseDatabaseManager

class DataProcessor:
    """
    Data processor for SEC Form D filings.
    """

    def __init__(self, data_dir: str = "data"):
        """
        Initialize the data processor.

        Args:
            data_dir: Directory for data storage
        """
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True, parents=True)

        # Initialize database connection
        try:
            self.db = SupabaseDatabaseManager()
            self.database_available = True
            logging.info("✅ Database connection established for context retrieval")
        except Exception as e:
            logging.warning(f"Database initialization failed: {e}")
            self.database_available = False

        # Initialize vector store collections
        try:
            self.feed_collection = client.get_or_create_collection("formd_feed")
            self.bulk_collection = client.get_or_create_collection("formd_bulk")
            self.vector_store_available = True
        except Exception as e:
            logging.warning(f"Vector store initialization failed: {e}")
            self.vector_store_available = False

    def normalize_feed_entries(self, feed_entries: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Normalize feed entries to a consistent format.

        Args:
            feed_entries: List of feed entries from SEC ATOM feed

        Returns:
            List of normalized entries
        """
        normalized = []

        for entry in feed_entries:
            # Extract basic fields
            normalized_entry = {
                "id": entry.get("id", ""),
                "title": entry.get("title", ""),
                "updated": entry.get("updated", ""),
                "link": entry.get("link", ""),
                "summary": entry.get("summary", ""),
                "source": "atom_feed"
            }

            # Extract offering amount if available
            try:
                # Try to extract amount from summary using pattern matching
                summary = entry.get("summary", "")
                if "Amount:" in summary:
                    amount_str = summary.split("Amount:")[1].split("<br>")[0].strip()
                    # Remove non-numeric characters except decimal point
                    amount_str = ''.join(c for c in amount_str if c.isdigit() or c == '.')
                    if amount_str:
                        normalized_entry["offering_amount"] = float(amount_str)
                    else:
                        normalized_entry["offering_amount"] = 0.0
                else:
                    normalized_entry["offering_amount"] = 0.0
            except Exception:
                normalized_entry["offering_amount"] = 0.0

            # Extract industry/sector if available
            try:
                summary = entry.get("summary", "")
                if "Industry:" in summary:
                    industry = summary.split("Industry:")[1].split("<br>")[0].strip()
                    normalized_entry["industry"] = industry
                else:
                    normalized_entry["industry"] = "Unknown"
            except Exception:
                normalized_entry["industry"] = "Unknown"

            # Extract filing date
            try:
                if "Filed:" in entry.get("summary", ""):
                    date_str = entry.get("summary", "").split("Filed:")[1].split("<b>")[0].strip()
                    normalized_entry["filing_date"] = date_str
                else:
                    # Use updated date as fallback
                    normalized_entry["filing_date"] = entry.get("updated", "").split("T")[0]
            except Exception:
                normalized_entry["filing_date"] = entry.get("updated", "").split("T")[0]

            normalized.append(normalized_entry)

        return normalized

    def get_database_context(self, entry: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get historical context from database using SQL queries.

        Args:
            entry: Filing entry to get context for

        Returns:
            Dictionary with database context
        """
        if not self.database_available:
            return {}

        try:
            context = {}

            # Get company name and industry for queries
            company_name = entry.get("issuer_name", "").strip()
            industry = entry.get("industry", "").strip()
            offering_amount = entry.get("offering_amount", 0.0)

            if company_name:
                # Find similar companies by name (partial match) - LIMIT TO PREVENT MEMORY ISSUES
                similar_companies = self.db.search_filings(
                    issuer_name=company_name,
                    limit=3  # Reduced from 5 to prevent memory issues
                )
                context["similar_companies"] = similar_companies[:2]  # Top 2 only

            if industry and industry != "Unknown":
                # Find companies in same industry - LIMIT TO PREVENT MEMORY ISSUES
                industry_filings = self.db.search_filings(
                    industry_group=industry,
                    limit=5  # Reduced from 10
                )
                context["industry_filings"] = industry_filings[:3]  # Top 3 only

            if offering_amount > 0:
                # Find filings with similar offering amounts (±50%) - LIMIT TO PREVENT MEMORY ISSUES
                min_amount = offering_amount * 0.5
                max_amount = offering_amount * 1.5
                similar_amounts = self.db.search_filings(
                    min_amount=min_amount,
                    max_amount=max_amount,
                    limit=3  # Reduced from 5
                )
                context["similar_amounts"] = similar_amounts[:2]  # Top 2 only

            # Get recent filings for trend analysis - LIMIT TO PREVENT MEMORY ISSUES
            from datetime import datetime, timedelta
            recent_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")  # Reduced from 90 days
            recent_filings = self.db.search_filings(
                date_from=recent_date,
                limit=3  # Reduced from 10
            )
            context["recent_trends"] = recent_filings[:2]  # Top 2 only

            logging.debug(f"Retrieved database context with {len(context)} categories")
            return context

        except Exception as e:
            logging.error(f"Error getting database context: {e}")
            return {}

    def enrich_with_historical_context(self, entries: List[Dict[str, Any]], use_sec_api: bool = True) -> List[Dict[str, Any]]:
        """
        Enrich entries with historical context from database and vector store.

        Args:
            entries: List of normalized entries
            use_sec_api: Whether to enrich with SEC API data

        Returns:
            List of enriched entries
        """
        enriched = []

        for entry in entries:
            # Create a copy of the entry to enrich
            enriched_entry = entry.copy()

            # First, get structured context from database (fast SQL queries)
            database_context = self.get_database_context(entry)
            enriched_entry["database_context"] = database_context

            # Second, get semantic similarity from vector store (if available)
            if self.vector_store_available:
                try:
                    # Create query text from title and summary
                    query_text = f"{entry.get('title', '')} {entry.get('summary', '')}"

                    # Embed the query
                    query_embedding = embedder.encode([query_text], convert_to_numpy=True)[0]

                    # Query the bulk collection for similar filings (semantic similarity)
                    results = self.bulk_collection.query(
                        query_embeddings=[query_embedding],
                        n_results=3  # Reduced to 3 since we have database context
                    )

                    # Extract similar filings
                    similar_filings = []
                    if results and "ids" in results and len(results["ids"]) > 0:
                        for i, doc_id in enumerate(results["ids"][0]):
                            if i < len(results["metadatas"][0]) and i < len(results["documents"][0]):
                                similar_filing = {
                                    "id": doc_id,
                                    "metadata": results["metadatas"][0][i],
                                    "content": results["documents"][0][i],
                                    "distance": results["distances"][0][i] if "distances" in results else 0.0
                                }
                                similar_filings.append(similar_filing)

                    # Add similar filings to enriched entry
                    enriched_entry["similar_filings"] = similar_filings

                    # Calculate similarity metrics
                    if similar_filings:
                        # Average distance to similar filings
                        avg_distance = np.mean([f.get("distance", 0.0) for f in similar_filings])
                        enriched_entry["avg_similarity_distance"] = float(avg_distance)

                        # Count filings in same industry
                        entry_industry = entry.get("industry", "Unknown").lower()
                        same_industry_count = sum(
                            1 for f in similar_filings
                            if f.get("metadata", {}).get("industry", "").lower() == entry_industry
                        )
                        enriched_entry["same_industry_count"] = same_industry_count

                        # Calculate offering amount percentile
                        entry_amount = entry.get("offering_amount", 0.0)
                        if entry_amount > 0:
                            similar_amounts = [
                                float(f.get("metadata", {}).get("offering_amount", 0.0))
                                for f in similar_filings
                            ]
                            similar_amounts = [a for a in similar_amounts if a > 0]

                            if similar_amounts:
                                percentile = sum(1 for a in similar_amounts if a < entry_amount) / len(similar_amounts)
                                enriched_entry["amount_percentile"] = percentile
                    else:
                        enriched_entry["similar_filings"] = []
                        enriched_entry["avg_similarity_distance"] = 0.0
                        enriched_entry["same_industry_count"] = 0
                        enriched_entry["amount_percentile"] = 0.0

                except Exception as e:
                    logging.error(f"Error enriching entry with vector store: {e}")
                    enriched_entry["similar_filings"] = []
                    enriched_entry["avg_similarity_distance"] = 0.0
                    enriched_entry["same_industry_count"] = 0
                    enriched_entry["amount_percentile"] = 0.0
                    enriched_entry["enrichment_error"] = str(e)
            else:
                # No vector store available, set defaults
                enriched_entry["similar_filings"] = []
                enriched_entry["avg_similarity_distance"] = 0.0
                enriched_entry["same_industry_count"] = 0
                enriched_entry["amount_percentile"] = 0.0

            # Enrich with SEC API data if requested
            if use_sec_api:
                try:
                    # Use SEC API to get company information
                    sec_enriched = enrich_filing_with_company_data(enriched_entry)

                    # If enrichment was successful, update the entry
                    if sec_enriched.get("sec_api_enriched", False):
                        logging.info(f"Successfully enriched entry with SEC API data: {sec_enriched.get('id', '')}")
                        enriched_entry = sec_enriched
                    else:
                        logging.debug(f"SEC API enrichment not available for entry: {enriched_entry.get('id', '')}")
                except Exception as e:
                    logging.error(f"Error enriching entry with SEC API data: {e}")

            enriched.append(enriched_entry)

        return enriched

    def extract_features(self, entry: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract features from an entry for analysis.

        Args:
            entry: Enriched entry

        Returns:
            Dictionary of extracted features
        """
        features = {}

        # Basic features
        features["has_offering_amount"] = entry.get("offering_amount", 0.0) > 0
        features["offering_amount"] = entry.get("offering_amount", 0.0)
        features["industry"] = entry.get("industry", "Unknown")

        # Similarity features
        features["has_similar_filings"] = len(entry.get("similar_filings", [])) > 0
        features["avg_similarity_distance"] = entry.get("avg_similarity_distance", 0.0)
        features["same_industry_count"] = entry.get("same_industry_count", 0)
        features["amount_percentile"] = entry.get("amount_percentile", 0.0)

        # Temporal features
        try:
            filing_date = datetime.fromisoformat(entry.get("filing_date", "").replace("Z", "+00:00"))
            features["days_since_filing"] = (datetime.now() - filing_date).days
        except:
            features["days_since_filing"] = 0

        # SEC API features
        features["sec_api_enriched"] = entry.get("sec_api_enriched", False)

        # Company info features (if available)
        company_info = entry.get("company_info", {})
        if company_info:
            features["company_name"] = company_info.get("name", "")
            features["sic_code"] = company_info.get("sic", "")
            features["sic_description"] = company_info.get("sic_description", "")
            features["state_of_incorporation"] = company_info.get("state_of_incorporation", "")
            features["fiscal_year_end"] = company_info.get("fiscal_year_end", "")
            features["filing_count"] = company_info.get("filing_count", 0)

            # Derived features
            features["is_new_company"] = company_info.get("filing_count", 0) <= 3

            # SIC code category (high level industry grouping)
            sic_code = company_info.get("sic", "")
            if sic_code:
                try:
                    sic_num = int(sic_code)
                    if sic_num < 1000:
                        features["sic_category"] = "Agriculture, Forestry, Fishing"
                    elif sic_num < 1500:
                        features["sic_category"] = "Mining"
                    elif sic_num < 1800:
                        features["sic_category"] = "Construction"
                    elif sic_num < 4000:
                        features["sic_category"] = "Manufacturing"
                    elif sic_num < 5000:
                        features["sic_category"] = "Transportation & Utilities"
                    elif sic_num < 5200:
                        features["sic_category"] = "Wholesale Trade"
                    elif sic_num < 6000:
                        features["sic_category"] = "Retail Trade"
                    elif sic_num < 6800:
                        features["sic_category"] = "Finance, Insurance, Real Estate"
                    elif sic_num < 9000:
                        features["sic_category"] = "Services"
                    else:
                        features["sic_category"] = "Public Administration"
                except:
                    features["sic_category"] = "Unknown"
            else:
                features["sic_category"] = "Unknown"

        return features
