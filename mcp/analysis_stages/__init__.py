#!/usr/bin/env python3
"""
Multi-stage Analysis Pipeline for SEC Form D Filings

This module implements a three-stage analysis pipeline:
1. Screening - Quick assessment to determine if a filing is worth analyzing
2. Detailed Analysis - In-depth analysis of relevant filings
3. Action Generation - Creating actionable insights and recommendations

Each stage has specialized prompts and processing logic.
"""

from .screening import ScreeningStage
from .detailed_analysis import DetailedAnalysisStage
from .action_generation import ActionGenerationStage

__all__ = [
    "ScreeningStage",
    "DetailedAnalysisStage",
    "ActionGenerationStage"
]
