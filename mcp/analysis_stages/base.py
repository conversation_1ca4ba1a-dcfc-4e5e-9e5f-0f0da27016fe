#!/usr/bin/env python3
"""
Base Analysis Stage

Defines the base class for all analysis stages in the pipeline.
Each stage should inherit from this class and implement its methods.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional


class BaseAnalysisStage(ABC):
    """
    Base class for analysis stages.
    """

    def __init__(self, name: str):
        """
        Initialize the analysis stage.

        Args:
            name: Name of the stage
        """
        self.name = name

    @abstractmethod
    def create_prompt(self, entry: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> str:
        """
        Create a prompt for this analysis stage.

        Args:
            entry: Filing entry to analyze
            context: Optional context from previous stages

        Returns:
            Formatted prompt string
        """
        pass

    @abstractmethod
    def parse_output(self, llm_output: str) -> Dict[str, Any]:
        """
        Parse LLM output for this stage.

        Args:
            llm_output: Raw output from LLM

        Returns:
            Parsed output as dictionary
        """
        pass

    @abstractmethod
    def should_proceed(self, parsed_output: Dict[str, Any]) -> bool:
        """
        Determine if the pipeline should proceed to the next stage.

        Args:
            parsed_output: Parsed output from this stage

        Returns:
            <PERSON><PERSON><PERSON> indicating whether to proceed
        """
        pass

    def get_next_stage_context(self, parsed_output: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get context to pass to the next stage.

        Args:
            parsed_output: Parsed output from this stage

        Returns:
            Context dictionary for next stage
        """
        # Default implementation passes all parsed output as context
        return parsed_output
