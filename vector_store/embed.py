# vector_store/embed.py
"""
Embed feed summaries and bulk filings into a Chroma vector store.
"""

import json
from pathlib import Path
from sentence_transformers import SentenceTransformer

import chromadb
from chromadb import PersistentClient
from chromadb.config import Settings, DEFAULT_TENANT, DEFAULT_DATABASE

# 1. Initialize embedder & Chroma client
try:
    embedder = SentenceTransformer("all-MiniLM-L6-v2")
except Exception as e:
    print(f"[embed] WARNING: Could not load SentenceTransformer: {e}")
    print("[embed] Using mock embedder for development")

    # Create a mock embedder for development
    class MockEmbedder:
        def encode(self, texts, convert_to_numpy=True):
            import numpy as np
            # Return random embeddings of the right shape
            if isinstance(texts, list):
                return np.random.rand(len(texts), 384)  # 384 is the dimension of all-MiniLM-L6-v2
            else:
                return np.random.rand(1, 384)

    embedder = MockEmbedder()

# Use the new PersistentClient for a local on-disk store
try:
    client = PersistentClient(
        path="vector_store/chroma_db",
        settings=Settings(anonymized_telemetry=False),
        tenant=DEFAULT_TENANT,
        database=DEFAULT_DATABASE,
    )

    # Ensure collections exist
    feed_col = client.get_or_create_collection("formd_feed")
    bulk_col = client.get_or_create_collection("formd_bulk")
except Exception as e:
    print(f"[embed] WARNING: Could not initialize Chroma client: {e}")
    print("[embed] Using mock client for development")

    # Create mock collections for development
    class MockCollection:
        def __init__(self, name):
            self.name = name

        def upsert(self, ids, embeddings, metadatas=None, documents=None):
            print(f"[embed] Mock upsert to {self.name}: {len(ids)} items")
            return True

        def query(self, query_embeddings, n_results=10):
            import numpy as np
            # Return mock query results
            return {
                "ids": [["mock_id_1", "mock_id_2"] for _ in range(len(query_embeddings))],
                "distances": [np.random.rand(n_results) for _ in range(len(query_embeddings))],
                "metadatas": [[{"source": "mock"}] for _ in range(len(query_embeddings))],
                "documents": [["mock doc"] for _ in range(len(query_embeddings))]
            }

    class MockClient:
        def get_or_create_collection(self, name):
            return MockCollection(name)

    client = MockClient()
    feed_col = client.get_or_create_collection("formd_feed")
    bulk_col = client.get_or_create_collection("formd_bulk")

def upsert_feed_entries(entries):
    """Embed and upsert feed entries, with duplicate ID handling."""
    if not entries:
        print("[embed] No entries to upsert")
        return

    # Ensure unique IDs by using a dictionary
    unique_entries = {}
    for entry in entries:
        entry_id = entry["id"]
        if entry_id in unique_entries:
            # If duplicate, keep the most recently updated one
            if entry.get("updated", "") > unique_entries[entry_id].get("updated", ""):
                unique_entries[entry_id] = entry
        else:
            unique_entries[entry_id] = entry

    # If we found and removed duplicates, log it
    if len(unique_entries) < len(entries):
        print(f"[embed] Removed {len(entries) - len(unique_entries)} duplicate entries")

    # Convert back to list and proceed
    unique_entries_list = list(unique_entries.values())

    try:
        texts = [e["title"] + " " + e["summary"] for e in unique_entries_list]
        ids = [e["id"] for e in unique_entries_list]
        embs = embedder.encode(texts, convert_to_numpy=True)

        # Double-check for duplicates before upserting
        # Use a dictionary to ensure unique IDs while preserving the order
        unique_items = {}
        for i, id_val in enumerate(ids):
            # If duplicate, keep the last occurrence
            unique_items[id_val] = {
                "text": texts[i],
                "emb": embs[i].tolist(),
                "meta": unique_entries_list[i]
            }

        # Extract the unique items in a deterministic order
        unique_ids = list(unique_items.keys())
        unique_texts = [unique_items[id_val]["text"] for id_val in unique_ids]
        unique_embs = [unique_items[id_val]["emb"] for id_val in unique_ids]
        unique_metas = [unique_items[id_val]["meta"] for id_val in unique_ids]

        # Log if we found and removed duplicates in this second check
        if len(unique_ids) < len(ids):
            print(f"[embed] Removed {len(ids) - len(unique_ids)} additional duplicate IDs")

        # Upsert in batches to avoid memory issues
        batch_size = 5000  # Safe batch size for ChromaDB
        total_items = len(unique_ids)

        for i in range(0, total_items, batch_size):
            batch_end = min(i + batch_size, total_items)
            batch_ids = unique_ids[i:batch_end]
            batch_embs = unique_embs[i:batch_end]
            batch_metas = unique_metas[i:batch_end]
            batch_texts = unique_texts[i:batch_end]

            # Perform the upsert with guaranteed unique IDs
            feed_col.upsert(
                ids=batch_ids,
                embeddings=batch_embs,
                metadatas=batch_metas,
                documents=batch_texts
            )

            if total_items > batch_size:
                print(f"[embed] ✓ Upserted batch {i//batch_size + 1}/{(total_items + batch_size - 1)//batch_size} " +
                      f"({len(batch_ids)} items) of feed embeddings")

        print(f"[embed] ✓ Upserted {len(unique_ids)} feed embeddings")
    except Exception as e:
        print(f"[embed] ERROR upserting feed entries: {e}")
        # Continue execution rather than crashing the pipeline
        return

def upsert_bulk_entries(data_dir="data/raw"):
    """Embed and upsert bulk entries from JSONL files with error handling."""
    try:
        jsonl_files = list(Path(data_dir).rglob("formd_*.jsonl"))
        if not jsonl_files:
            print(f"[embed] No JSONL files found in {data_dir}")
            return

        print(f"[embed] Found {len(jsonl_files)} JSONL files to process")

        for file in jsonl_files:
            try:
                file_content = file.read_text().splitlines()
                if not file_content:
                    print(f"[embed] Empty file: {file}")
                    continue

                items = []
                for line in file_content:
                    try:
                        items.append(json.loads(line))
                    except json.JSONDecodeError:
                        print(f"[embed] Invalid JSON in {file}: {line[:50]}...")

                if not items:
                    print(f"[embed] No valid items in {file}")
                    continue

                texts, ids, metas = [], [], []
                for idx, obj in enumerate(items):
                    # Get a meaningful description - try multiple fields
                    desc = (
                        obj.get("offeringAmount") or
                        obj.get("issuerName", "") or
                        obj.get("summary", "")
                    )
                    if not desc:
                        print(f"[embed] No description for item {idx} in {file}")
                        continue

                    texts.append(str(desc))
                    # Create a unique ID using file stem and index
                    id_ = f"{file.stem}__{idx}"
                    ids.append(id_)
                    # Add more metadata for better searchability
                    meta = {
                        "source_file": file.name,
                        "issuer_name": obj.get("issuerName", ""),
                        "filing_date": obj.get("filingDate", ""),
                    }
                    metas.append(meta)

                if not texts:
                    print(f"[embed] No valid texts extracted from {file}")
                    continue

                # Embed and upsert in batches to avoid memory issues
                batch_size = 5000  # Safe batch size for ChromaDB
                total_items = len(texts)

                for i in range(0, total_items, batch_size):
                    batch_end = min(i + batch_size, total_items)
                    batch_texts = texts[i:batch_end]
                    batch_ids = ids[i:batch_end]
                    batch_metas = metas[i:batch_end]

                    # Embed the batch
                    batch_embs = embedder.encode(batch_texts, convert_to_numpy=True)

                    # Upsert the batch
                    bulk_col.upsert(
                        ids=batch_ids,
                        embeddings=batch_embs.tolist(),
                        metadatas=batch_metas,
                        documents=batch_texts
                    )

                    print(f"[embed] ✓ Upserted batch {i//batch_size + 1}/{(total_items + batch_size - 1)//batch_size} " +
                          f"({len(batch_ids)} items) from {file.name}")

            except Exception as e:
                print(f"[embed] ERROR processing {file}: {e}")
                # Continue with next file rather than crashing
                continue

    except Exception as e:
        print(f"[embed] ERROR in bulk upsert: {e}")
        # Continue execution rather than crashing the pipeline
        return
